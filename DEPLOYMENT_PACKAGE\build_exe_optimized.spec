# -*- mode: python ; coding: utf-8 -*-
"""
تكوين PyInstaller محسن لنظام إدارة التدريب
Optimized PyInstaller Configuration for Training Management System
"""

import sys
import os
from PyInstaller.utils.hooks import collect_data_files, collect_submodules

# المسار الأساسي للمشروع
project_path = os.path.dirname(os.path.abspath(SPEC))

# جمع البيانات والملفات المطلوبة
datas = []

# إضافة ملفات Flask والإضافات
datas += collect_data_files('flask')
datas += collect_data_files('flask_sqlalchemy')
datas += collect_data_files('flask_login')
datas += collect_data_files('flask_wtf')
datas += collect_data_files('wtforms')
datas += collect_data_files('email_validator')

# إضافة ملفات معالجة البيانات
datas += collect_data_files('pandas')
datas += collect_data_files('openpyxl')
datas += collect_data_files('xlsxwriter')
datas += collect_data_files('numpy')

# إضافة ملفات النصوص العربية
datas += collect_data_files('arabic_reshaper')
datas += collect_data_files('bidi')

# إضافة ملفات التطبيق الأساسية
app_files = [
    ('templates', 'templates'),
    ('static', 'static'),
    ('training_system_deployment.db', '.'),
    ('requirements_deployment.txt', '.'),
]

# التحقق من وجود الملفات وإضافتها
for src, dst in app_files:
    src_path = os.path.join(project_path, src)
    if os.path.exists(src_path):
        datas.append((src_path, dst))
        print(f"✓ تم إضافة: {src}")
    else:
        print(f"⚠️  ملف غير موجود: {src}")

# جمع الوحدات المخفية
hiddenimports = []

# وحدات Flask
hiddenimports += collect_submodules('flask')
hiddenimports += collect_submodules('flask_sqlalchemy')
hiddenimports += collect_submodules('flask_login')
hiddenimports += collect_submodules('flask_wtf')
hiddenimports += collect_submodules('wtforms')

# وحدات قاعدة البيانات
hiddenimports += collect_submodules('sqlalchemy')
hiddenimports += [
    'sqlalchemy.dialects.sqlite',
    'sqlalchemy.pool',
    'sqlalchemy.engine.default',
    'sqlalchemy.sql.default_comparator',
]

# وحدات معالجة البيانات
hiddenimports += collect_submodules('pandas')
hiddenimports += collect_submodules('openpyxl')
hiddenimports += collect_submodules('xlsxwriter')
hiddenimports += collect_submodules('numpy')

# وحدات النصوص العربية
hiddenimports += collect_submodules('arabic_reshaper')
hiddenimports += collect_submodules('bidi')

# وحدات التحقق من البريد الإلكتروني
hiddenimports += collect_submodules('email_validator')
hiddenimports += [
    'dns.resolver',
    'dns.rdatatype',
    'dns.rdataclass',
    'dns.name',
    'dns.query',
    'dns.message',
]

# وحدات إضافية مطلوبة
hiddenimports += [
    'werkzeug.security',
    'werkzeug.utils',
    'werkzeug.exceptions',
    'jinja2',
    'markupsafe',
    'itsdangerous',
    'click',
    'colorama',
    'six',
    'dateutil',
    'pytz',
    'tqdm',
]

# تكوين التحليل
a = Analysis(
    ['app_deployment.py'],
    pathex=[project_path],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'tkinter',
        'matplotlib',
        'PIL',
        'PyQt5',
        'PyQt6',
        'PySide2',
        'PySide6',
        'test',
        'unittest',
        'doctest',
        'pdb',
        'pydoc',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=None,
    noarchive=False,
)

# إزالة الملفات غير المطلوبة
a.datas = [x for x in a.datas if not x[0].startswith('tcl')]
a.datas = [x for x in a.datas if not x[0].startswith('tk')]

# تكوين PYZ
pyz = PYZ(a.pure, a.zipped_data, cipher=None)

# تكوين EXE
exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='TrainingSystem',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,  # يمكن إضافة أيقونة هنا
    version=None,
)

# تكوين COLLECT
coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='TrainingSystem'
)

print("=" * 60)
print("    تكوين PyInstaller محسن")
print("    Optimized PyInstaller Configuration")
print("=" * 60)
print(f"📁 مسار المشروع: {project_path}")
print(f"📦 عدد ملفات البيانات: {len(datas)}")
print(f"🔧 عدد الوحدات المخفية: {len(hiddenimports)}")
print("=" * 60)
