@echo off
chcp 65001 >nul
echo ========================================
echo    🎓 نظام إدارة التدريب
echo    سكريبت البناء الرئيسي
echo    Training Management System
echo    Master Build Script
echo ========================================
echo.

cd /d "%~dp0\.."

echo 🚀 بدء عملية البناء الشاملة...
echo 🚀 Starting comprehensive build process...
echo.

echo ⏰ وقت البدء: %date% %time%
echo ⏰ Start time: %date% %time%
echo.

REM متغيرات التحكم في العملية
set step_count=0
set total_steps=7
set build_failed=false

REM الخطوة 1: تنظيف المشروع
set /a step_count+=1
echo ========================================
echo    الخطوة %step_count%/%total_steps%: تنظيف المشروع
echo    Step %step_count%/%total_steps%: Project Cleanup
echo ========================================
echo.

call "DEPLOYMENT_PACKAGE\cleanup_project.bat"
if errorlevel 1 (
    echo ❌ فشل في تنظيف المشروع
    echo ❌ Project cleanup failed
    set build_failed=true
    goto :build_summary
)

echo ✅ تم تنظيف المشروع بنجاح
echo ✅ Project cleanup completed successfully
echo.
pause

REM الخطوة 2: إنشاء بيئة افتراضية نظيفة
set /a step_count+=1
echo ========================================
echo    الخطوة %step_count%/%total_steps%: إنشاء بيئة افتراضية
echo    Step %step_count%/%total_steps%: Virtual Environment
echo ========================================
echo.

call "DEPLOYMENT_PACKAGE\create_clean_environment.bat"
if errorlevel 1 (
    echo ❌ فشل في إنشاء البيئة الافتراضية
    echo ❌ Virtual environment creation failed
    set build_failed=true
    goto :build_summary
)

echo ✅ تم إنشاء البيئة الافتراضية بنجاح
echo ✅ Virtual environment created successfully
echo.
pause

REM الخطوة 3: تثبيت المتطلبات
set /a step_count+=1
echo ========================================
echo    الخطوة %step_count%/%total_steps%: تثبيت المتطلبات
echo    Step %step_count%/%total_steps%: Installing Requirements
echo ========================================
echo.

call "DEPLOYMENT_PACKAGE\install_requirements.bat"
if errorlevel 1 (
    echo ❌ فشل في تثبيت المتطلبات
    echo ❌ Requirements installation failed
    set build_failed=true
    goto :build_summary
)

echo ✅ تم تثبيت المتطلبات بنجاح
echo ✅ Requirements installed successfully
echo.
pause

REM الخطوة 4: تحضير قاعدة البيانات
set /a step_count+=1
echo ========================================
echo    الخطوة %step_count%/%total_steps%: تحضير قاعدة البيانات
echo    Step %step_count%/%total_steps%: Database Preparation
echo ========================================
echo.

call "DEPLOYMENT_PACKAGE\prepare_database.bat"
if errorlevel 1 (
    echo ❌ فشل في تحضير قاعدة البيانات
    echo ❌ Database preparation failed
    set build_failed=true
    goto :build_summary
)

echo ✅ تم تحضير قاعدة البيانات بنجاح
echo ✅ Database prepared successfully
echo.
pause

REM الخطوة 5: بناء ملف EXE
set /a step_count+=1
echo ========================================
echo    الخطوة %step_count%/%total_steps%: بناء ملف EXE
echo    Step %step_count%/%total_steps%: Building EXE
echo ========================================
echo.

call "DEPLOYMENT_PACKAGE\build_optimized_exe.bat"
if errorlevel 1 (
    echo ❌ فشل في بناء ملف EXE
    echo ❌ EXE build failed
    set build_failed=true
    goto :build_summary
)

echo ✅ تم بناء ملف EXE بنجاح
echo ✅ EXE built successfully
echo.
pause

REM الخطوة 6: تحسين الملفات
set /a step_count+=1
echo ========================================
echo    الخطوة %step_count%/%total_steps%: تحسين الملفات
echo    Step %step_count%/%total_steps%: File Optimization
echo ========================================
echo.

call "DEPLOYMENT_PACKAGE\optimize_files.bat"
if errorlevel 1 (
    echo ❌ فشل في تحسين الملفات
    echo ❌ File optimization failed
    set build_failed=true
    goto :build_summary
)

echo ✅ تم تحسين الملفات بنجاح
echo ✅ Files optimized successfully
echo.
pause

REM الخطوة 7: اختبار النظام وإنشاء حزمة العميل
set /a step_count+=1
echo ========================================
echo    الخطوة %step_count%/%total_steps%: الاختبار وإنشاء الحزمة
echo    Step %step_count%/%total_steps%: Testing and Packaging
echo ========================================
echo.

call "DEPLOYMENT_PACKAGE\test_complete_system.bat"
if errorlevel 1 (
    echo ❌ فشل في اختبار النظام
    echo ❌ System testing failed
    set build_failed=true
    goto :build_summary
)

echo ✅ تم اختبار النظام بنجاح
echo ✅ System tested successfully
echo.

call "DEPLOYMENT_PACKAGE\create_client_package.bat"
if errorlevel 1 (
    echo ❌ فشل في إنشاء حزمة العميل
    echo ❌ Client package creation failed
    set build_failed=true
    goto :build_summary
)

echo ✅ تم إنشاء حزمة العميل بنجاح
echo ✅ Client package created successfully
echo.

:build_summary
echo ========================================
echo    📊 ملخص عملية البناء
echo    📊 Build Process Summary
echo ========================================
echo.

echo ⏰ وقت الانتهاء: %date% %time%
echo ⏰ End time: %date% %time%
echo.

if "%build_failed%"=="true" (
    echo ❌ فشلت عملية البناء!
    echo ❌ Build process failed!
    echo.
    echo 🔧 يرجى مراجعة الأخطاء أعلاه وإعادة المحاولة
    echo 🔧 Please review errors above and try again
    echo.
    goto :end
)

echo 🎉 تمت عملية البناء بنجاح!
echo 🎉 Build process completed successfully!
echo.

echo 📋 النتائج النهائية:
echo 📋 Final results:

if exist "dist\TrainingSystem\TrainingSystem.exe" (
    echo ✅ الملف التنفيذي: dist\TrainingSystem\TrainingSystem.exe
    echo ✅ Executable: dist\TrainingSystem\TrainingSystem.exe
    
    for %%f in ("dist\TrainingSystem\TrainingSystem.exe") do (
        echo    📏 الحجم: %%~zf بايت
        echo    📏 Size: %%~zf bytes
    )
) else (
    echo ❌ الملف التنفيذي غير موجود
    echo ❌ Executable not found
)

REM البحث عن حزمة العميل
for /d %%d in (TrainingSystem_Final_*) do (
    echo ✅ حزمة العميل: %%d
    echo ✅ Client package: %%d
    set client_package=%%d
)

if defined client_package (
    if exist "%client_package%.zip" (
        echo ✅ الملف المضغوط: %client_package%.zip
        echo ✅ Compressed file: %client_package%.zip
    )
)

echo.
echo 📦 ملفات التسليم الجاهزة:
echo 📦 Ready delivery files:

if defined client_package (
    echo    📁 %client_package%\ - مجلد الحزمة الكاملة
    echo    📁 %client_package%\ - Complete package folder
    
    if exist "%client_package%.zip" (
        echo    🗜️  %client_package%.zip - الملف المضغوط
        echo    🗜️  %client_package%.zip - Compressed file
    )
    
    echo    📖 دليل_التثبيت.txt - دليل التثبيت
    echo    📖 دليل_التثبيت.txt - Installation guide
    echo    📚 دليل_المستخدم.txt - دليل المستخدم
    echo    📚 دليل_المستخدم.txt - User manual
    echo    ℹ️  معلومات_الإصدار.txt - معلومات الإصدار
    echo    ℹ️  معلومات_الإصدار.txt - Version information
)

echo.
echo 🎯 الحزمة جاهزة للتسليم للعميل!
echo 🎯 Package ready for client delivery!
echo.

echo 📋 معلومات تسجيل الدخول الافتراضية:
echo 📋 Default login information:
echo    👤 اسم المستخدم: admin
echo    👤 Username: admin
echo    🔑 كلمة المرور: admin123
echo    🔑 Password: admin123
echo    ⚠️  يرجى تغيير كلمة المرور بعد أول تسجيل دخول
echo    ⚠️  Please change password after first login
echo.

echo 📞 معلومات الدعم الفني:
echo 📞 Technical support information:
echo    📧 البريد الإلكتروني: <EMAIL>
echo    📧 Email: <EMAIL>
echo    🌐 الموقع: www.trainingsystem.com
echo    🌐 Website: www.trainingsystem.com
echo.

:end
echo 🔚 انتهت عملية البناء
echo 🔚 Build process finished
echo.

pause
