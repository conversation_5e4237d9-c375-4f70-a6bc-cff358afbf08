@echo off
chcp 65001 >nul
color 0B
echo.
echo ================================================================
echo    Advanced Development and Deployment Management Tool
echo    Developer Toolkit - GUI Version
echo ================================================================
echo.
echo Features:
echo  * Modern GUI interface
echo  * Smart project analysis
echo  * Complete or custom build
echo  * Advanced database management
echo  * Automatic system testing
echo  * Advanced logging system
echo  * Interactive progress bars
echo  * Smart backup system
echo.
echo ================================================================
echo.

echo Checking runtime requirements...
echo.

REM Check Python
python --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Python is not installed or not available in PATH
    echo.
    echo Please install Python 3.7+ first
    echo.
    pause
    exit /b 1
)

echo [OK] Python available
python --version

REM Check tkinter
python -c "import tkinter" >nul 2>&1
if errorlevel 1 (
    echo [ERROR] tkinter not available
    echo.
    echo Please install tkinter:
    echo    pip install tk
    echo.
    pause
    exit /b 1
)

echo [OK] tkinter available

REM Check main file
if not exist "developer_toolkit.py" (
    echo [ERROR] developer_toolkit.py file not found
    echo.
    echo Make sure the file exists in the same folder
    echo.
    pause
    exit /b 1
)

echo [OK] Toolkit file found
echo.

echo Starting Development and Deployment Management Tool...
echo.

REM Check additional requirements
echo Checking additional requirements...

python -c "import requests" >nul 2>&1
if errorlevel 1 (
    echo [WARNING] requests library not available - installing
    pip install requests >nul 2>&1
    if errorlevel 1 (
        echo [WARNING] Failed to install requests - some features may not work
    ) else (
        echo [OK] requests installed
    )
)

echo.
echo Starting GUI interface...
echo.

REM Run the tool
python developer_toolkit.py

if errorlevel 1 (
    echo.
    echo [ERROR] An error occurred while running the tool
    echo.
    echo Troubleshooting tips:
    echo  * Make sure Python 3.7+ is installed
    echo  * Make sure tkinter is installed
    echo  * Check file permissions
    echo  * Try running as administrator
    echo.
    pause
    exit /b 1
)

echo.
echo Development and Deployment Management Tool closed
echo.
pause
