@echo off
chcp 65001 >nul
color 0B
echo.
echo ████████████████████████████████████████████████████████████████
echo ██                                                            ██
echo ██    🚀 أداة إدارة التطوير والتوزيع المتقدمة                ██
echo ██    Advanced Development & Deployment Management Tool       ██
echo ██                                                            ██
echo ████████████████████████████████████████████████████████████████
echo.
echo 🎯 أداة رسومية متقدمة للمطورين لإدارة عملية التطوير والتوزيع
echo 🎯 Advanced GUI tool for developers to manage development and deployment
echo.
echo ⚡ الميزات الرئيسية:
echo ⚡ Main Features:
echo    • واجهة رسومية حديثة وسهلة الاستخدام
echo    • تحليل وفحص شامل للمشروع
echo    • بناء كامل أو مخصص
echo    • إدارة متقدمة لقاعدة البيانات
echo    • اختبار تلقائي للنظام
echo    • نظام سجلات متقدم
echo    • أشرطة تقدم تفاعلية
echo    • نسخ احتياطي ذكي
echo.
echo ════════════════════════════════════════════════════════════════
echo.

echo 🔧 التحقق من متطلبات التشغيل...
echo 🔧 Checking runtime requirements...
echo.

REM التحقق من Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت أو غير متوفر في PATH
    echo ❌ Python is not installed or not available in PATH
    echo.
    echo 💡 يرجى تثبيت Python 3.7+ أولاً
    echo 💡 Please install Python 3.7+ first
    echo.
    pause
    exit /b 1
)

echo ✅ Python متوفر
echo ✅ Python available

REM التحقق من tkinter
python -c "import tkinter" >nul 2>&1
if errorlevel 1 (
    echo ❌ tkinter غير متوفر
    echo ❌ tkinter not available
    echo.
    echo 💡 يرجى تثبيت tkinter:
    echo 💡 Please install tkinter:
    echo    pip install tk
    echo.
    pause
    exit /b 1
)

echo ✅ tkinter متوفر
echo ✅ tkinter available

REM التحقق من الملف الرئيسي
if not exist "developer_toolkit.py" (
    echo ❌ ملف developer_toolkit.py غير موجود
    echo ❌ developer_toolkit.py file not found
    echo.
    echo 💡 تأكد من وجود الملف في نفس المجلد
    echo 💡 Make sure the file exists in the same folder
    echo.
    pause
    exit /b 1
)

echo ✅ ملف الأداة موجود
echo ✅ Toolkit file found
echo.

echo 🚀 تشغيل أداة إدارة التطوير والتوزيع...
echo 🚀 Starting Development & Deployment Management Tool...
echo.

REM تثبيت المتطلبات الإضافية إذا لزم الأمر
echo 📦 التحقق من المتطلبات الإضافية...
echo 📦 Checking additional requirements...

python -c "import requests" >nul 2>&1
if errorlevel 1 (
    echo ⚠️  مكتبة requests غير متوفرة - سيتم تثبيتها
    echo ⚠️  requests library not available - installing
    pip install requests >nul 2>&1
    if errorlevel 1 (
        echo ⚠️  فشل في تثبيت requests - بعض الميزات قد لا تعمل
        echo ⚠️  Failed to install requests - some features may not work
    ) else (
        echo ✅ تم تثبيت requests
        echo ✅ requests installed
    )
)

echo.
echo 🎨 بدء تشغيل الواجهة الرسومية...
echo 🎨 Starting GUI interface...
echo.

REM تشغيل الأداة
python developer_toolkit.py

if errorlevel 1 (
    echo.
    echo ❌ حدث خطأ أثناء تشغيل الأداة
    echo ❌ An error occurred while running the tool
    echo.
    echo 🔧 نصائح لحل المشاكل:
    echo 🔧 Troubleshooting tips:
    echo    • تأكد من تثبيت Python 3.7+
    echo    • تأكد من تثبيت tkinter
    echo    • تحقق من أذونات الملفات
    echo    • أعد تشغيل الأداة كمدير
    echo.
    pause
    exit /b 1
)

echo.
echo 👋 تم إغلاق أداة إدارة التطوير والتوزيع
echo 👋 Development & Deployment Management Tool closed
echo.
pause
