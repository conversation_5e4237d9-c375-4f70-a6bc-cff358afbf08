<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - نظام إدارة التدريب</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f8f9fa;
            color: #333;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px 0;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo-section {
            display: flex;
            align-items: center;
        }

        .logo {
            font-size: 2rem;
            margin-left: 15px;
        }

        .system-title {
            font-size: 1.5rem;
            font-weight: 600;
        }

        .user-section {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .user-info {
            text-align: left;
        }

        .logout-btn {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            padding: 8px 16px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 20px;
            text-decoration: none;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .logout-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            text-decoration: none;
            color: white;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .welcome-section {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
            text-align: center;
        }

        .welcome-title {
            font-size: 2rem;
            color: #333;
            margin-bottom: 10px;
        }

        .welcome-subtitle {
            color: #666;
            font-size: 1.1rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            text-align: center;
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-icon {
            font-size: 2.5rem;
            margin-bottom: 15px;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: #333;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #666;
            font-size: 1rem;
        }

        .actions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .action-card {
            background: white;
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }

        .action-card:hover {
            transform: translateY(-3px);
        }

        .action-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }

        .action-icon {
            font-size: 1.5rem;
            margin-left: 10px;
            color: #667eea;
        }

        .action-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: #333;
        }

        .action-description {
            color: #666;
            margin-bottom: 20px;
            line-height: 1.5;
        }

        .action-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            text-decoration: none;
            display: inline-block;
            font-weight: 500;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
            text-decoration: none;
            color: white;
        }

        .system-status {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
        }

        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }

            .actions-grid {
                grid-template-columns: 1fr;
            }

            .welcome-title {
                font-size: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="header-content">
            <div class="logo-section">
                <div class="logo">🎓</div>
                <div class="system-title">نظام إدارة التدريب</div>
            </div>
            <div class="user-section">
                <div class="user-info">
                    <div>مرحباً، {{ current_user.username }}</div>
                    <small>{{ current_user.role }}</small>
                </div>
                <a href="{{ url_for('logout') }}" class="logout-btn">تسجيل الخروج</a>
            </div>
        </div>
    </header>

    <div class="container">
        <div class="system-status">
            ✅ النظام يعمل بشكل طبيعي - جميع الخدمات متاحة
        </div>

        <div class="welcome-section">
            <h1 class="welcome-title">مرحباً بك في نظام إدارة التدريب</h1>
            <p class="welcome-subtitle">إدارة شاملة للدورات التدريبية والمشاركين</p>
        </div>

        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon">👥</div>
                <div class="stat-number" id="participants-count">-</div>
                <div class="stat-label">إجمالي المشاركين</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">📚</div>
                <div class="stat-number" id="courses-count">-</div>
                <div class="stat-label">إجمالي الدورات</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">🎯</div>
                <div class="stat-number" id="active-courses">-</div>
                <div class="stat-label">الدورات النشطة</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">📊</div>
                <div class="stat-number" id="completion-rate">-</div>
                <div class="stat-label">معدل الإنجاز</div>
            </div>
        </div>

        <div class="actions-grid">
            <div class="action-card">
                <div class="action-header">
                    <div class="action-icon">👥</div>
                    <div class="action-title">إدارة المشاركين</div>
                </div>
                <div class="action-description">
                    إضافة وتعديل بيانات المشاركين، استيراد البيانات من ملفات Excel، وإدارة المعلومات الشخصية.
                </div>
                <a href="#" class="action-btn">إدارة المشاركين</a>
            </div>

            <div class="action-card">
                <div class="action-header">
                    <div class="action-icon">📚</div>
                    <div class="action-title">إدارة الدورات</div>
                </div>
                <div class="action-description">
                    إنشاء دورات تدريبية جديدة، تعديل الدورات الموجودة، وإدارة المناهج والجداول الزمنية.
                </div>
                <a href="#" class="action-btn">إدارة الدورات</a>
            </div>

            <div class="action-card">
                <div class="action-header">
                    <div class="action-icon">📊</div>
                    <div class="action-title">التقارير والإحصائيات</div>
                </div>
                <div class="action-description">
                    إنشاء تقارير مفصلة، عرض الإحصائيات، وتصدير البيانات بصيغ مختلفة.
                </div>
                <a href="#" class="action-btn">عرض التقارير</a>
            </div>

            <div class="action-card">
                <div class="action-header">
                    <div class="action-icon">🔍</div>
                    <div class="action-title">البحث المتقدم</div>
                </div>
                <div class="action-description">
                    البحث في قاعدة البيانات، فلترة النتائج، والعثور على المعلومات بسرعة.
                </div>
                <a href="#" class="action-btn">البحث المتقدم</a>
            </div>

            <div class="action-card">
                <div class="action-header">
                    <div class="action-icon">⚙️</div>
                    <div class="action-title">إعدادات النظام</div>
                </div>
                <div class="action-description">
                    تكوين النظام، إدارة المستخدمين، النسخ الاحتياطية، وإعدادات الأمان.
                </div>
                <a href="#" class="action-btn">الإعدادات</a>
            </div>

            <div class="action-card">
                <div class="action-header">
                    <div class="action-icon">💾</div>
                    <div class="action-title">النسخ الاحتياطية</div>
                </div>
                <div class="action-description">
                    إنشاء نسخ احتياطية من البيانات، استعادة النسخ السابقة، وإدارة ملفات النسخ.
                </div>
                <a href="#" class="action-btn">النسخ الاحتياطية</a>
            </div>
        </div>
    </div>

    <script>
        // تحميل الإحصائيات
        document.addEventListener('DOMContentLoaded', function() {
            loadStatistics();
        });

        function loadStatistics() {
            // محاكاة تحميل الإحصائيات
            setTimeout(() => {
                document.getElementById('participants-count').textContent = '0';
                document.getElementById('courses-count').textContent = '1';
                document.getElementById('active-courses').textContent = '1';
                document.getElementById('completion-rate').textContent = '100%';
            }, 500);

            // التحقق من حالة النظام
            fetch('/api/system/status')
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        console.log('✅ النظام يعمل بشكل طبيعي');
                        if (data.users !== undefined) {
                            // يمكن تحديث الإحصائيات هنا
                        }
                    }
                })
                .catch(error => {
                    console.log('⚠️ خطأ في تحميل الإحصائيات:', error);
                });
        }

        // تأثيرات بصرية للبطاقات
        const cards = document.querySelectorAll('.stat-card, .action-card');
        cards.forEach((card, index) => {
            card.style.animationDelay = `${index * 0.1}s`;
            card.style.animation = 'fadeInUp 0.6s ease forwards';
        });

        // إضافة CSS للتأثيرات
        const style = document.createElement('style');
        style.textContent = `
            @keyframes fadeInUp {
                from {
                    opacity: 0;
                    transform: translateY(30px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
