/* الخط العربي المحسن */
@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700;800;900&family=Cairo:wght@300;400;500;600;700;800;900&display=swap');

/* تحسينات الأداء والتمرير السلس */
* {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    overflow-x: hidden;
    overflow-y: auto;
}

/* تحسين الأداء للعناصر المتحركة */
*[class*="animation"],
*[class*="animate"],
.card:hover,
.btn:hover {
    will-change: transform;
    transform: translateZ(0);
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
}

/* تحسينات خاصة للتمرير السلس */
body, html {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
    scroll-behavior: smooth;
}

/* تقليل إعادة الرسم أثناء التمرير */
.container, .container-fluid {
    will-change: auto;
    transform: translateZ(0);
}

/* تحسين الأداء للعناصر الثابتة */
.navbar, .sidebar {
    will-change: auto;
    transform: translateZ(0);
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
}

/* تحسين التمرير في جميع العناصر */
*, *::before, *::after {
    scroll-behavior: smooth;
}

body {
    font-family: 'Cairo', 'Tajawal', sans-serif;
    background-color: #f8f9fa;
    min-height: 100vh;
    line-height: 1.6;
}

/* تنسيق العناوين */
h1, h2, h3, h4, h5, h6 {
    font-weight: 700;
}

/* تنسيق الأزرار المحسن */
.btn {
    border-radius: 12px;
    padding: 12px 24px;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    /* تحسينات الأداء */
    will-change: transform;
    transform: translateZ(0);
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
}

.btn-primary {
    background: linear-gradient(135deg, #1976d2 0%, #42a5f5 100%);
    border: none;
    box-shadow: 0 8px 25px rgba(25, 118, 210, 0.3);
}

.btn-primary:hover {
    background: linear-gradient(135deg, #1565c0 0%, #1976d2 100%);
    transform: translateY(-2px) translateZ(0);
    box-shadow: 0 8px 25px rgba(25, 118, 210, 0.3);
}

/* تنسيق البطاقات المحسن */
.card {
    border-radius: 20px;
    overflow: hidden;
    margin-bottom: 25px;
    box-shadow: 0 15px 35px rgba(25, 118, 210, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 1);
    /* تحسينات الأداء */
    will-change: transform;
    transform: translateZ(0);
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
}

.card:hover {
    transform: translateY(-5px) translateZ(0);
    box-shadow: 0 20px 40px rgba(25, 118, 210, 0.15);
}

/* تنسيق شريط التنقل المحسن - تصميم إبداعي متطور */
.navbar {
    background: linear-gradient(135deg,
        rgba(15, 23, 42, 0.98) 0%,      /* أزرق داكن عميق */
        rgba(30, 41, 59, 0.96) 20%,     /* أزرق متوسط */
        rgba(51, 65, 85, 0.94) 40%,     /* أزرق رمادي */
        rgba(71, 85, 105, 0.92) 60%,    /* أزرق فاتح */
        rgba(100, 116, 139, 0.90) 80%,  /* أزرق ناعم */
        rgba(148, 163, 184, 0.88) 100%  /* أزرق شاحب */
    ) !important;
    box-shadow:
        0 15px 35px rgba(15, 23, 42, 0.4),
        0 8px 25px rgba(30, 41, 59, 0.3),
        0 4px 15px rgba(51, 65, 85, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.15),
        inset 0 -1px 0 rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(25px);
    border-radius: 0 0 30px 30px;
    border-bottom: 2px solid;
    border-image: linear-gradient(90deg,
        rgba(59, 130, 246, 0.5) 0%,
        rgba(96, 165, 250, 0.7) 50%,
        rgba(59, 130, 246, 0.5) 100%
    ) 1;
    position: relative;
    overflow: hidden;
    min-height: 70px;
    padding: 8px 0;
}

.navbar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 30%, rgba(59, 130, 246, 0.15) 0%, transparent 40%),
        radial-gradient(circle at 80% 70%, rgba(96, 165, 250, 0.12) 0%, transparent 45%),
        linear-gradient(90deg, rgba(147, 197, 253, 0.08) 0%, transparent 50%, rgba(147, 197, 253, 0.08) 100%);
    pointer-events: none;
    animation: headerGlow 6s ease-in-out infinite;
}

@keyframes headerGlow {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

.navbar-brand {
    font-weight: 900;
    font-size: 1.8rem;
    background: linear-gradient(135deg,
        #ffffff 0%,
        #e2e8f0 30%,
        #cbd5e1 70%,
        #94a3b8 100%
    );
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 3px 6px rgba(0, 0, 0, 0.4);
    filter: drop-shadow(0 2px 8px rgba(255, 255, 255, 0.2));
    position: relative;
    padding: 8px 15px;
    border-radius: 15px;
    transition: all 0.3s ease;
}

.navbar-brand:hover {
    transform: scale(1.05);
    filter: drop-shadow(0 4px 12px rgba(255, 255, 255, 0.3));
}

.navbar-brand i {
    background: linear-gradient(135deg, #10b981, #059669);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-left: 10px;
    filter: drop-shadow(0 2px 4px rgba(16, 185, 129, 0.5));
    animation: iconFloat 3s ease-in-out infinite;
}

@keyframes iconFloat {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-3px); }
}

/* تصميم التاريخ والساعة المحسن */
.navbar-datetime {
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.15) 0%,
        rgba(255, 255, 255, 0.1) 50%,
        rgba(255, 255, 255, 0.05) 100%
    );
    backdrop-filter: blur(15px);
    border-radius: 20px;
    padding: 8px 15px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow:
        0 4px 15px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.datetime-container {
    display: flex;
    align-items: center;
    gap: 15px;
}

.date-section, .time-section {
    display: flex;
    align-items: center;
    gap: 6px;
}

.date-section i, .time-section i {
    background: linear-gradient(135deg, #10b981, #059669);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-size: 1rem;
    filter: drop-shadow(0 1px 3px rgba(16, 185, 129, 0.5));
}

.date-text, .time-text {
    color: rgba(255, 255, 255, 0.95);
    font-size: 0.9rem;
    font-weight: 600;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
    letter-spacing: 0.5px;
}

.time-text {
    font-family: 'Courier New', monospace;
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.9) 0%,
        rgba(219, 234, 254, 0.8) 100%
    );
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: timeGlow 2s ease-in-out infinite;
}

@keyframes timeGlow {
    0%, 100% {
        filter: drop-shadow(0 0 5px rgba(255, 255, 255, 0.3));
    }
    50% {
        filter: drop-shadow(0 0 10px rgba(255, 255, 255, 0.5));
    }
}

/* تحسين روابط التنقل */
.navbar-nav .nav-link {
    color: rgba(255, 255, 255, 0.9) !important;
    font-weight: 600;
    padding: 10px 15px !important;
    border-radius: 12px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.navbar-nav .nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.navbar-nav .nav-link:hover::before {
    left: 100%;
}

.navbar-nav .nav-link:hover {
    color: white !important;
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* تحسين القائمة المنسدلة */
.dropdown-menu {
    background: linear-gradient(135deg,
        rgba(15, 23, 42, 0.95) 0%,
        rgba(30, 41, 59, 0.95) 100%
    );
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    box-shadow:
        0 15px 35px rgba(0, 0, 0, 0.3),
        0 5px 15px rgba(0, 0, 0, 0.2);
    padding: 10px 0;
    margin-top: 10px;
}

.dropdown-item {
    color: rgba(255, 255, 255, 0.9) !important;
    padding: 10px 20px !important;
    transition: all 0.3s ease;
    border-radius: 10px;
    margin: 2px 8px;
}

.dropdown-item:hover {
    background: linear-gradient(135deg,
        rgba(59, 130, 246, 0.3) 0%,
        rgba(96, 165, 250, 0.2) 100%
    ) !important;
    color: white !important;
    transform: translateX(5px);
}

/* تأثيرات إضافية للشريط العلوي */
.navbar-toggler {
    border: none !important;
    padding: 8px 12px;
    border-radius: 10px;
    background: rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.navbar-toggler:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.05);
}

.navbar-toggler-icon {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 0.9%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='m4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e") !important;
}

/* تحسين الحاوي */
.navbar .container {
    position: relative;
    z-index: 2;
}

/* تأثير التمرير */
.navbar.scrolled {
    background: linear-gradient(135deg,
        rgba(15, 23, 42, 0.99) 0%,
        rgba(30, 41, 59, 0.98) 50%,
        rgba(51, 65, 85, 0.97) 100%
    ) !important;
    box-shadow:
        0 20px 40px rgba(15, 23, 42, 0.5),
        0 10px 25px rgba(30, 41, 59, 0.4);
}

/* تنسيق التذييل المحسن */
footer {
    margin-top: 50px;
    padding: 30px 0;
    background: linear-gradient(135deg, #1565c0 0%, #1976d2 50%, #1e88e5 100%);
    color: white;
    border-radius: 25px 25px 0 0;
    box-shadow: 0 -8px 32px rgba(25, 118, 210, 0.2);
    backdrop-filter: blur(15px);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* تنسيق النماذج المحسن */
.form-control {
    border-radius: 12px;
    padding: 15px 20px;
    margin-bottom: 20px;
    border: 2px solid rgba(25, 118, 210, 0.2);
    background: rgba(255, 255, 255, 1);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #1976d2;
    box-shadow: 0 0 0 0.25rem rgba(25, 118, 210, 0.25);
    background: rgba(255, 255, 255, 1);
    transform: translateY(-2px);
}

/* تنسيق الرسائل التنبيهية المحسن */
.alert {
    border-radius: 15px;
    border: none;
    backdrop-filter: blur(10px);
    box-shadow: 0 8px 25px rgba(25, 118, 210, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.2);
    font-weight: 500;
}

.alert-success {
    background: linear-gradient(135deg, rgba(76, 175, 80, 0.9) 0%, rgba(129, 199, 132, 0.9) 100%);
    color: white;
}

.alert-info {
    background: linear-gradient(135deg, rgba(25, 118, 210, 0.9) 0%, rgba(66, 165, 245, 0.9) 100%);
    color: white;
}

.alert-warning {
    background: linear-gradient(135deg, rgba(255, 152, 0, 0.9) 0%, rgba(255, 183, 77, 0.9) 100%);
    color: white;
}

.alert-danger {
    background: linear-gradient(135deg, rgba(244, 67, 54, 0.9) 0%, rgba(239, 83, 80, 0.9) 100%);
    color: white;
}

/* تنسيق الصور */
.img-thumbnail {
    border-radius: 10px;
}

/* تنسيق الروابط المحسن */
a {
    color: #1976d2;
    text-decoration: none;
    transition: all 0.3s ease;
    position: relative;
}

a:hover {
    color: #1565c0;
    text-decoration: none;
    transform: translateY(-1px);
}

a:not(.btn):not(.nav-link):not(.sidebar-link):hover::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(135deg, #1976d2 0%, #42a5f5 100%);
    border-radius: 1px;
}

/* تنسيق القوائم */
.list-group-item {
    border-left: none;
    border-right: none;
    padding: 15px;
}

/* تنسيق الأيقونات المحسن */
.icon-circle {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    color: #1976d2;
    margin-bottom: 20px;
    box-shadow: 0 8px 25px rgba(25, 118, 210, 0.2);
    transition: all 0.3s ease;
}

.icon-circle:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(25, 118, 210, 0.3);
}

/* تنسيق الشارات المحسن */
.badge {
    padding: 8px 16px;
    border-radius: 25px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.badge.bg-primary {
    background: linear-gradient(135deg, #1976d2 0%, #42a5f5 100%) !important;
}

/* تنسيق الجداول المحسن */
.table {
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 15px 35px rgba(25, 118, 210, 0.1);
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 1);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.table thead {
    background: linear-gradient(135deg, #1976d2 0%, #42a5f5 100%);
    color: white;
}

.table tbody tr:hover {
    background: rgba(25, 118, 210, 0.05);
    transform: translateZ(0);
    transition: background-color 0.2s ease;
}

/* تنسيق الصفحة الرئيسية */
.hero-section {
    background: linear-gradient(135deg, #4a6bff 0%, #2541b2 100%);
    color: white;
    padding: 100px 0;
    margin-bottom: 50px;
    border-radius: 0 0 50px 50px;
}

.feature-icon {
    font-size: 3rem;
    color: #4a6bff;
    margin-bottom: 20px;
}

/* تنسيق لوحة التحكم */
.dashboard-stats {
    display: flex;
    justify-content: space-between;
    margin-bottom: 30px;
}

.stat-item {
    background-color: white;
    border-radius: 10px;
    padding: 20px;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    flex: 1;
    margin: 0 10px;
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: #4a6bff;
    margin: 10px 0;
}

/* تنسيق الصفحات الفرعية */
.page-header {
    background-color: #f8f9fa;
    padding: 30px 0;
    margin-bottom: 30px;
    border-radius: 10px;
}

.page-title {
    margin: 0;
    font-size: 2rem;
    color: #2541b2;
}

/* تنسيق الأقسام */
.section {
    margin-bottom: 50px;
}

.section-title {
    margin-bottom: 20px;
    position: relative;
    padding-bottom: 10px;
}

.section-title:after {
    content: '';
    position: absolute;
    bottom: 0;
    right: 0;
    width: 50px;
    height: 3px;
    background-color: #4a6bff;
}
