@echo off
chcp 65001 >nul
echo ========================================
echo    تحضير قاعدة البيانات للتوزيع
echo    Preparing Database for Distribution
echo ========================================
echo.

cd /d "%~dp0\.."

echo 🔧 التحقق من تفعيل البيئة الافتراضية...
echo 🔧 Checking virtual environment activation...

if not exist "venv_deployment\Scripts\activate.bat" (
    echo ❌ البيئة الافتراضية غير موجودة
    echo ❌ Virtual environment not found
    echo يرجى تشغيل create_clean_environment.bat أولاً
    echo Please run create_clean_environment.bat first
    pause
    exit /b 1
)

call venv_deployment\Scripts\activate.bat

echo ✅ تم تفعيل البيئة الافتراضية
echo ✅ Virtual environment activated
echo.

echo 📋 نسخ سكريبت إدارة قاعدة البيانات...
echo 📋 Copying database management script...

copy "DEPLOYMENT_PACKAGE\database_manager.py" "database_manager_temp.py" >nul

if not exist "database_manager_temp.py" (
    echo ❌ فشل في نسخ سكريبت قاعدة البيانات
    echo ❌ Failed to copy database script
    pause
    exit /b 1
)

echo.
echo 🗄️  اختيار نوع قاعدة البيانات...
echo 🗄️  Choose database type...
echo.
echo    1️⃣  قاعدة بيانات فارغة (مستحسن للتوزيع)
echo        Empty Database (Recommended for distribution)
echo.
echo    2️⃣  قاعدة بيانات كاملة مع بيانات تجريبية
echo        Full Database with sample data
echo.
echo    3️⃣  تحديث قاعدة البيانات الموجودة
echo        Update existing database
echo.

set /p db_choice="اختر نوع قاعدة البيانات (1-3): "

if "%db_choice%"=="1" (
    echo.
    echo 🗄️  إنشاء قاعدة بيانات فارغة...
    echo 🗄️  Creating empty database...
    python -c "
from database_manager_temp import DatabaseManager
db_manager = DatabaseManager()
success = db_manager.create_empty_database()
exit(0 if success else 1)
"
) else if "%db_choice%"=="2" (
    echo.
    echo 🗄️  إنشاء قاعدة بيانات كاملة...
    echo 🗄️  Creating full database...
    python -c "
from database_manager_temp import DatabaseManager
db_manager = DatabaseManager()
success = db_manager.create_full_database()
exit(0 if success else 1)
"
) else if "%db_choice%"=="3" (
    echo.
    echo 🗄️  تحديث قاعدة البيانات الموجودة...
    echo 🗄️  Updating existing database...
    python -c "
from database_manager_temp import DatabaseManager
db_manager = DatabaseManager()
success = db_manager.update_existing_database()
exit(0 if success else 1)
"
) else (
    echo ❌ اختيار غير صحيح، سيتم إنشاء قاعدة بيانات فارغة
    echo ❌ Invalid choice, creating empty database
    python -c "
from database_manager_temp import DatabaseManager
db_manager = DatabaseManager()
success = db_manager.create_empty_database()
exit(0 if success else 1)
"
)

if errorlevel 1 (
    echo ❌ فشل في تحضير قاعدة البيانات
    echo ❌ Failed to prepare database
    del "database_manager_temp.py" 2>nul
    pause
    exit /b 1
)

echo.
echo 🔄 تنظيف الملفات المؤقتة...
echo 🔄 Cleaning temporary files...

del "database_manager_temp.py" 2>nul

echo.
echo ✅ تم تحضير قاعدة البيانات بنجاح!
echo ✅ Database preparation completed successfully!
echo.

echo 📋 الملفات المنشأة:
echo 📋 Created files:
if exist "training_system_deployment.db" (
    echo    ✓ training_system_deployment.db - قاعدة البيانات المحسنة
    echo    ✓ training_system_deployment.db - Optimized database
) else (
    echo    ❌ لم يتم إنشاء قاعدة البيانات
    echo    ❌ Database was not created
)

echo.
echo 📊 معلومات قاعدة البيانات:
echo 📊 Database information:
echo    • المستخدم الإداري: admin
echo    • Admin user: admin
echo    • كلمة المرور: admin123
echo    • Password: admin123
echo    • ⚠️  يرجى تغيير كلمة المرور بعد أول تسجيل دخول
echo    • ⚠️  Please change password after first login
echo.

echo 🔄 الخطوة التالية: تشغيل create_default_data.bat
echo 🔄 Next step: Run create_default_data.bat
echo.

pause
