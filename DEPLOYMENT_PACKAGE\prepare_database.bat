@echo off
chcp 65001 >nul
echo ========================================
echo    تحضير قاعدة البيانات للتوزيع
echo    Preparing Database for Distribution
echo ========================================
echo.

cd /d "%~dp0\.."

echo 🔧 التحقق من تفعيل البيئة الافتراضية...
echo 🔧 Checking virtual environment activation...

if not exist "venv_deployment\Scripts\activate.bat" (
    echo ❌ البيئة الافتراضية غير موجودة
    echo ❌ Virtual environment not found
    echo يرجى تشغيل create_clean_environment.bat أولاً
    echo Please run create_clean_environment.bat first
    pause
    exit /b 1
)

call venv_deployment\Scripts\activate.bat

echo ✅ تم تفعيل البيئة الافتراضية
echo ✅ Virtual environment activated
echo.

echo 📋 نسخ سكريبت تحضير قاعدة البيانات...
echo 📋 Copying database preparation script...

copy "DEPLOYMENT_PACKAGE\prepare_database.py" "prepare_database_temp.py" >nul

if not exist "prepare_database_temp.py" (
    echo ❌ فشل في نسخ سكريبت قاعدة البيانات
    echo ❌ Failed to copy database script
    pause
    exit /b 1
)

echo 🗄️  تشغيل سكريبت تحضير قاعدة البيانات...
echo 🗄️  Running database preparation script...

python prepare_database_temp.py

if errorlevel 1 (
    echo ❌ فشل في تحضير قاعدة البيانات
    echo ❌ Failed to prepare database
    del "prepare_database_temp.py" 2>nul
    pause
    exit /b 1
)

echo.
echo 🔄 تنظيف الملفات المؤقتة...
echo 🔄 Cleaning temporary files...

del "prepare_database_temp.py" 2>nul

echo.
echo ✅ تم تحضير قاعدة البيانات بنجاح!
echo ✅ Database preparation completed successfully!
echo.

echo 📋 الملفات المنشأة:
echo 📋 Created files:
if exist "training_system_deployment.db" (
    echo    ✓ training_system_deployment.db - قاعدة البيانات المحسنة
    echo    ✓ training_system_deployment.db - Optimized database
) else (
    echo    ❌ لم يتم إنشاء قاعدة البيانات
    echo    ❌ Database was not created
)

echo.
echo 📊 معلومات قاعدة البيانات:
echo 📊 Database information:
echo    • المستخدم الإداري: admin
echo    • Admin user: admin
echo    • كلمة المرور: admin123
echo    • Password: admin123
echo    • ⚠️  يرجى تغيير كلمة المرور بعد أول تسجيل دخول
echo    • ⚠️  Please change password after first login
echo.

echo 🔄 الخطوة التالية: تشغيل create_default_data.bat
echo 🔄 Next step: Run create_default_data.bat
echo.

pause
