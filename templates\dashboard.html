{% extends "layout.html" %}

{% block styles %}
<style>
    /* خلفية احترافية ثابتة للصفحة */
    body {
        background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 30%, #bae6fd 70%, #dbeafe 100%);
        background-attachment: fixed;
        min-height: 100vh;
        position: relative;
        overflow-x: auto;
        overflow-y: auto;
    }

    body::before {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background:
            radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
            radial-gradient(circle at 80% 20%, rgba(16, 185, 129, 0.08) 0%, transparent 50%),
            radial-gradient(circle at 40% 40%, rgba(139, 92, 246, 0.06) 0%, transparent 50%);
        pointer-events: none;
        z-index: -1;
    }

    /* تصميم حديث للبطاقات مع تأثيرات هادئة */
    .dashboard-card {
        border-radius: 20px;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
        transition: all 0.3s ease;
        border: 1px solid rgba(226, 232, 240, 0.8);
        overflow: hidden;
        backdrop-filter: blur(10px);
        background: rgba(255, 255, 255, 0.95);
        position: relative;
    }

    .dashboard-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 12px 35px rgba(0, 0, 0, 0.12);
        border-color: rgba(59, 130, 246, 0.3);
    }

    .dashboard-card-header {
        background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
        color: white;
        padding: 20px 25px;
        font-weight: 600;
        font-size: 1.1rem;
        border-radius: 20px 20px 0 0;
    }

    /* بطاقات الإحصائيات المحسنة مع تأثيرات هادئة */
    .stat-card {
        border-radius: 20px;
        padding: 30px 20px;
        text-align: center;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        border: 1px solid rgba(255, 255, 255, 0.3);
        position: relative;
        overflow: hidden;
        backdrop-filter: blur(10px);
    }

    .stat-card:hover {
        transform: translateY(-8px);
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
    }

    .stat-primary {
        background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
        color: white;
        box-shadow: 0 8px 25px rgba(30, 64, 175, 0.3);
        border: 1px solid rgba(59, 130, 246, 0.3);
    }

    .stat-success {
        background: linear-gradient(135deg, #059669 0%, #10b981 100%);
        color: white;
        box-shadow: 0 8px 25px rgba(5, 150, 105, 0.3);
        border: 1px solid rgba(16, 185, 129, 0.3);
    }

    .stat-warning {
        background: linear-gradient(135deg, #7c3aed 0%, #8b5cf6 100%);
        color: white;
        box-shadow: 0 8px 25px rgba(124, 58, 237, 0.3);
        border: 1px solid rgba(139, 92, 246, 0.3);
    }

    .stat-danger {
        background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
        color: white;
        box-shadow: 0 8px 25px rgba(30, 64, 175, 0.3);
        border: 1px solid rgba(59, 130, 246, 0.3);
    }

    .stat-info {
        background: linear-gradient(135deg, #0f766e 0%, #14b8a6 100%);
        color: white;
        box-shadow: 0 8px 25px rgba(15, 118, 110, 0.3);
        border: 1px solid rgba(20, 184, 166, 0.3);
    }

    .stat-number {
        font-size: 3rem;
        font-weight: 800;
        margin: 15px 0;
        color: white !important;
        text-shadow: 0 3px 6px rgba(0,0,0,0.5);
    }

    .stat-icon {
        font-size: 2.5rem;
        margin-bottom: 15px;
        color: white !important;
        opacity: 0.95;
        text-shadow: 0 2px 4px rgba(0,0,0,0.4);
    }

    .stat-label {
        font-size: 0.9rem;
        color: white !important;
        opacity: 0.95;
        margin-bottom: 10px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 1px;
        text-shadow: 0 2px 4px rgba(0,0,0,0.4);
    }

    /* تحسين الشريط الجانبي بتصميم احترافي */
    .sidebar {
        background: linear-gradient(135deg, rgba(30, 64, 175, 0.95) 0%, rgba(59, 130, 246, 0.95) 100%);
        color: white;
        min-height: calc(100vh - 56px);
        padding-top: 30px;
        border-radius: 20px;
        box-shadow: 0 10px 30px rgba(30, 64, 175, 0.3);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.1);
        position: relative;
        overflow: hidden;
    }

    /* تصميم صورة المستخدم الافتراضية */
    .user-avatar {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 15px;
        box-shadow: 0 8px 20px rgba(30, 64, 175, 0.3);
        border: 2px solid rgba(255, 255, 255, 0.2);
        transition: all 0.3s ease;
    }

    .user-avatar i {
        font-size: 2rem;
        color: white;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }

    .user-avatar:hover {
        transform: scale(1.05);
        box-shadow: 0 12px 25px rgba(30, 64, 175, 0.4);
    }

    .sidebar-link {
        color: rgba(255, 255, 255, 0.8);
        padding: 12px 18px;
        display: block;
        text-decoration: none;
        transition: all 0.3s ease;
        border-radius: 12px;
        margin: 6px 12px;
        font-weight: 500;
    }

    .sidebar-link:hover, .sidebar-link.active {
        color: white;
        background: rgba(255, 255, 255, 0.15);
        transform: translateX(5px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .sidebar-link i {
        margin-left: 10px;
        width: 20px;
        text-align: center;
    }

    .sidebar-dropdown-menu {
        display: none;
        padding-right: 20px;
    }

    .sidebar-dropdown-menu.show {
        display: block;
    }

    .dropdown-toggle::after {
        display: inline-block;
        margin-right: 5px;
        vertical-align: middle;
        content: "";
        border-top: 0.3em solid;
        border-left: 0.3em solid transparent;
        border-right: 0.3em solid transparent;
    }

    /* أنماط المربعات الملونة الكبيرة */
    .category-card {
        border-radius: 18px;
        padding: 25px;
        text-align: center;
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
        transition: all 0.3s ease;
        height: 100%;
        color: white;
    }

    .category-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 12px 30px rgba(0, 0, 0, 0.12);
    }

    .category-icon {
        font-size: 3rem;
        margin-bottom: 20px;
        color: white !important;
        opacity: 0.9;
        text-shadow: 0 2px 4px rgba(0,0,0,0.4);
    }

    .category-title {
        font-size: 1.8rem;
        font-weight: bold;
        margin-bottom: 15px;
        color: white !important;
        text-shadow: 0 2px 4px rgba(0,0,0,0.5);
    }

    .category-description {
        font-size: 1rem;
        color: white !important;
        opacity: 0.9;
        text-shadow: 0 1px 3px rgba(0,0,0,0.4);
    }

    .category-primary {
        background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
        box-shadow: 0 8px 25px rgba(30, 64, 175, 0.3);
    }

    .category-success {
        background: linear-gradient(135deg, #059669 0%, #10b981 100%);
        box-shadow: 0 8px 25px rgba(5, 150, 105, 0.3);
    }

    .category-info {
        background: linear-gradient(135deg, #7c3aed 0%, #8b5cf6 100%);
        box-shadow: 0 8px 25px rgba(124, 58, 237, 0.3);
    }

    .category-warning {
        background: linear-gradient(135deg, #0f766e 0%, #14b8a6 100%);
        box-shadow: 0 8px 25px rgba(15, 118, 110, 0.3);
    }

    .category-danger {
        background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
        box-shadow: 0 8px 25px rgba(99, 102, 241, 0.3);
    }

    /* تحسين العنوان الرئيسي بتصميم هادئ */
    .page-title {
        color: #1e40af;
        font-weight: 700;
        font-size: 2.5rem;
        margin-bottom: 30px;
        text-align: center;
        position: relative;
    }

    .page-title i {
        margin-left: 15px;
        color: #3b82f6;
    }

    .page-title::after {
        content: '';
        position: absolute;
        bottom: -10px;
        left: 50%;
        transform: translateX(-50%);
        width: 100px;
        height: 3px;
        background: linear-gradient(135deg, #3b82f6 0%, #60a5fa 100%);
        border-radius: 2px;
    }

    /* تأثيرات الحركة البسيطة */
    .dashboard-card, .stat-card {
        opacity: 1;
        transform: translateY(0);
    }

    /* تحسين الحاوي الرئيسي بتصميم احترافي */
    .main-container {
        background: rgba(255, 255, 255, 0.9);
        backdrop-filter: blur(10px);
        border-radius: 20px;
        padding: 30px;
        margin-top: 20px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
        border: 1px solid rgba(226, 232, 240, 0.8);
    }

    /* تحسين الأزرار السريعة */
    .quick-actions {
        margin-top: 30px;
    }

    .action-btn {
        border-radius: 12px;
        padding: 15px 20px;
        margin: 6px;
        transition: all 0.3s ease;
        border: none;
        font-weight: 600;
        font-size: 0.95rem;
    }

    .action-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
    }
</style>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-3">
        <div class="sidebar rounded">
            <div class="text-center mb-4">
                <div class="user-avatar">
                    <i class="fas fa-user"></i>
                </div>
                <h5 class="mt-2">{{ current_user.username }}</h5>
                <p class="badge bg-primary">{{ current_user.role }}</p>
            </div>
            <hr>
            <a href="{{ url_for('dashboard') }}" class="sidebar-link active">
                <i class="fas fa-tachometer-alt"></i> لوحة التحكم
            </a>
            <a href="{{ url_for('courses') }}" class="sidebar-link">
                <i class="fas fa-graduation-cap"></i> الدورات التدريبية
            </a>
            {% if current_user.role == 'admin' %}
            <a href="{{ url_for('users') }}" class="sidebar-link">
                <i class="fas fa-users"></i> إدارة المستخدمين
            </a>
            <a href="http://localhost:5000/person_data/name_analysis" class="sidebar-link">
                <i class="fas fa-chart-pie"></i> المباينة
            </a>
            <a href="{{ url_for('person_data.person_data_table') }}" class="sidebar-link">
                <i class="fas fa-table"></i> جدول بيانات الأشخاص
            </a>
            <a href="{{ url_for('reference_tables') }}" class="sidebar-link">
                <i class="fas fa-table"></i> الجداول الترميزية
            </a>
            <a href="{{ url_for('backup') }}" class="sidebar-link">
                <i class="fas fa-database"></i> النسخ الاحتياطي
            </a>
            {% endif %}
            <a href="#" class="sidebar-link">
                <i class="fas fa-calendar-alt"></i> الجدول الزمني
            </a>
            <a href="#" class="sidebar-link">
                <i class="fas fa-certificate"></i> الشهادات
            </a>
            {% if current_user.role == 'admin' %}
            <a href="/reports/dashboard" class="sidebar-link">
                <i class="fas fa-chart-line"></i> التقارير التفاعلية
            </a>
            {% endif %}
            {% if current_user.role == 'admin' %}
            <a href="#" class="sidebar-link">
                <i class="fas fa-cog"></i> الإعدادات
            </a>
            {% endif %}
        </div>
    </div>

    <div class="col-md-9">
        <div class="main-container">
            <h2 class="page-title"><i class="fas fa-cogs"></i> لوحة التحكم</h2>



            <div class="row mb-5">
                <div class="col-md-3">
                    <div class="stat-card stat-primary">
                        <i class="fas fa-users stat-icon"></i>
                        <div class="stat-number">1,016</div>
                        <div class="stat-label">إجمالي الأشخاص</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card stat-success">
                        <i class="fas fa-graduation-cap stat-icon"></i>
                        <div class="stat-number">201</div>
                        <div class="stat-label">الدورات المتاحة</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card stat-info">
                        <i class="fas fa-user-graduate stat-icon"></i>
                        <div class="stat-number">5,720</div>
                        <div class="stat-label">إجمالي المشاركين</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card stat-warning">
                        <i class="fas fa-chart-line stat-icon"></i>
                        <div class="stat-number">95%</div>
                        <div class="stat-label">معدل النجاح</div>
                    </div>
                </div>
            </div>

        <!-- المربعات الملونة الكبيرة -->
        <div class="row mb-5">
            <h3 class="mb-4">إدارة النظام</h3>

            <div class="col-md-6 mb-4">
                <a href="{{ url_for('personal_data_list') }}" class="text-decoration-none">
                    <div class="category-card category-primary">
                        <div class="category-icon">
                            <i class="fas fa-user-graduate"></i>
                        </div>
                        <h3 class="category-title">الملتحقين</h3>
                        <p class="category-description">إدارة بيانات المتدربين والملتحقين بالدورات</p>
                    </div>
                </a>
            </div>

            <div class="col-md-6 mb-4">
                <a href="{{ url_for('graduates') }}" class="text-decoration-none">
                    <div class="category-card category-success">
                        <div class="category-icon">
                            <i class="fas fa-graduation-cap"></i>
                        </div>
                        <h3 class="category-title">الخريجين</h3>
                        <p class="category-description">عرض بيانات الخريجين من الدورات</p>
                    </div>
                </a>
            </div>

            {% if current_user.role == 'admin' %}
            <div class="col-md-6 mb-4">
                <a href="/reports/dashboard" class="text-decoration-none">
                    <div class="category-card category-info">
                        <div class="category-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <h3 class="category-title">التقارير التفاعلية</h3>
                        <p class="category-description">تقارير بصرية تفاعلية مع رسوم بيانية متطورة</p>
                    </div>
                </a>
            </div>

            <div class="col-md-6 mb-4">
                <a href="{{ url_for('backup') }}" class="text-decoration-none">
                    <div class="category-card category-warning">
                        <div class="category-icon">
                            <i class="fas fa-database"></i>
                        </div>
                        <h3 class="category-title">النسخ الاحتياطي</h3>
                        <p class="category-description">إنشاء وإدارة النسخ الاحتياطية مع عرض تاريخ الإنشاء</p>
                    </div>
                </a>
            </div>
            {% endif %}
        </div>

        <div class="row">
            <div class="col-md-6">
                <div class="dashboard-card mb-4">
                    <div class="dashboard-card-header">
                        <i class="fas fa-calendar-alt me-2"></i> الدورات القادمة
                    </div>
                    <div class="card-body">
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                <span>تطوير تطبيقات الويب</span>
                                <span class="badge bg-primary rounded-pill">غداً</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                <span>أساسيات قواعد البيانات</span>
                                <span class="badge bg-primary rounded-pill">بعد 3 أيام</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                <span>إدارة المشاريع الاحترافية</span>
                                <span class="badge bg-primary rounded-pill">بعد أسبوع</span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="dashboard-card mb-4">
                    <div class="dashboard-card-header">
                        <i class="fas fa-bell me-2"></i> آخر الإشعارات
                    </div>
                    <div class="card-body">
                        <div class="alert alert-success mb-2">
                            <small class="text-muted">منذ ساعتين</small>
                            <p class="mb-0">تم إضافة دورة جديدة: "تطوير تطبيقات الهاتف المحمول"</p>
                        </div>
                        <div class="alert alert-info mb-2">
                            <small class="text-muted">منذ 5 ساعات</small>
                            <p class="mb-0">تم تحديث جدول الدورات التدريبية للشهر القادم</p>
                        </div>
                        <div class="alert alert-warning mb-2">
                            <small class="text-muted">منذ يوم</small>
                            <p class="mb-0">تذكير: موعد تسليم المشروع النهائي بعد أسبوع</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        </div> <!-- إغلاق main-container -->
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // تفعيل القائمة المنسدلة للجداول الترميزية
    document.addEventListener('DOMContentLoaded', function() {
        const dropdownToggles = document.querySelectorAll('.dropdown-toggle');

        dropdownToggles.forEach(toggle => {
            toggle.addEventListener('click', function(e) {
                e.preventDefault();
                const dropdownMenu = this.nextElementSibling;
                dropdownMenu.classList.toggle('show');
            });
        });
    });
</script>
{% endblock %}