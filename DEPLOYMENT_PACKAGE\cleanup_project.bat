@echo off
chcp 65001 >nul
echo ========================================
echo    تنظيف المشروع للتحويل إلى EXE
echo    Cleaning Project for EXE Conversion
echo ========================================
echo.

cd /d "%~dp0\.."

echo 🧹 تنظيف الملفات المؤقتة...
echo 🧹 Cleaning temporary files...

REM حذف ملفات Python المؤقتة
if exist "__pycache__" (
    echo حذف مجلد __pycache__...
    rmdir /s /q "__pycache__"
)

REM حذف ملفات .pyc
echo حذف ملفات .pyc...
for /r . %%i in (*.pyc) do del "%%i" 2>nul

REM حذف ملفات .pyo
echo حذف ملفات .pyo...
for /r . %%i in (*.pyo) do del "%%i" 2>nul

REM حذف مجلدات __pycache__ المتداخلة
echo حذف مجلدات __pycache__ المتداخلة...
for /d /r . %%d in (__pycache__) do (
    if exist "%%d" rmdir /s /q "%%d" 2>nul
)

REM حذف ملفات البناء السابقة
if exist "build" (
    echo حذف مجلد build السابق...
    rmdir /s /q "build"
)

if exist "dist" (
    echo حذف مجلد dist السابق...
    rmdir /s /q "dist"
)

if exist "*.spec" (
    echo حذف ملفات .spec السابقة...
    del "*.spec" 2>nul
)

REM حذف ملفات السجلات المؤقتة
if exist "*.log" (
    echo حذف ملفات السجلات المؤقتة...
    del "*.log" 2>nul
)

REM حذف ملفات Excel المؤقتة
echo حذف ملفات Excel المؤقتة...
del "~$*.xlsx" 2>nul
del "tmp*.xlsx" 2>nul

REM حذف ملفات النسخ الاحتياطية المؤقتة
if exist "*.bak" (
    echo حذف ملفات النسخ الاحتياطية المؤقتة...
    del "*.bak" 2>nul
)

REM تنظيف مجلد uploads من الملفات المؤقتة
if exist "static\uploads" (
    echo تنظيف مجلد uploads...
    cd "static\uploads"
    del "tmp*.*" 2>nul
    del "temp*.*" 2>nul
    cd ..\..
)

REM تنظيف مجلد exports من الملفات القديمة
if exist "exports" (
    echo تنظيف مجلد exports...
    cd "exports"
    REM حذف الملفات الأقدم من 7 أيام
    forfiles /m *.* /d -7 /c "cmd /c del @path" 2>nul
    cd ..
)

echo.
echo ✅ تم تنظيف المشروع بنجاح!
echo ✅ Project cleaned successfully!
echo.

echo 📋 الملفات المحذوفة:
echo 📋 Deleted files:
echo    - ملفات Python المؤقتة (.pyc, .pyo)
echo    - مجلدات __pycache__
echo    - ملفات البناء السابقة (build, dist)
echo    - ملفات السجلات المؤقتة
echo    - ملفات Excel المؤقتة
echo    - ملفات النسخ الاحتياطية المؤقتة
echo.

echo 🔄 الخطوة التالية: تشغيل create_clean_environment.bat
echo 🔄 Next step: Run create_clean_environment.bat
echo.

pause
