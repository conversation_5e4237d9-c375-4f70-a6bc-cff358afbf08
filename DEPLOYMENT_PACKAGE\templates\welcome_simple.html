<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة التدريب - الصفحة الرئيسية</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }

        .container {
            text-align: center;
            max-width: 800px;
            padding: 40px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .logo {
            font-size: 4rem;
            margin-bottom: 20px;
            color: #ffd700;
            text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
        }

        h1 {
            font-size: 2.5rem;
            margin-bottom: 20px;
            font-weight: 700;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            margin-bottom: 40px;
            opacity: 0.9;
            line-height: 1.6;
        }

        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 40px 0;
        }

        .feature {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 15px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: transform 0.3s ease;
        }

        .feature:hover {
            transform: translateY(-5px);
            background: rgba(255, 255, 255, 0.15);
        }

        .feature-icon {
            font-size: 2rem;
            margin-bottom: 10px;
            color: #ffd700;
        }

        .feature h3 {
            font-size: 1.1rem;
            margin-bottom: 10px;
        }

        .feature p {
            font-size: 0.9rem;
            opacity: 0.8;
            line-height: 1.4;
        }

        .login-btn {
            display: inline-block;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 40px;
            border-radius: 50px;
            text-decoration: none;
            font-weight: 600;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            border: 2px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        }

        .login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
        }

        .version {
            margin-top: 40px;
            font-size: 0.9rem;
            opacity: 0.7;
        }

        .status {
            margin-top: 20px;
            padding: 10px;
            background: rgba(0, 255, 0, 0.1);
            border: 1px solid rgba(0, 255, 0, 0.3);
            border-radius: 10px;
            color: #90EE90;
        }

        @media (max-width: 768px) {
            .container {
                margin: 20px;
                padding: 30px 20px;
            }

            h1 {
                font-size: 2rem;
            }

            .logo {
                font-size: 3rem;
            }

            .features {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">🎓</div>
        <h1>نظام إدارة التدريب</h1>
        <p class="subtitle">
            نظام شامل لإدارة الدورات التدريبية والمشاركين<br>
            مصمم خصيصاً للمؤسسات التعليمية والتدريبية
        </p>

        <div class="features">
            <div class="feature">
                <div class="feature-icon">👥</div>
                <h3>إدارة المشاركين</h3>
                <p>إضافة وإدارة بيانات المشاركين مع إمكانية الاستيراد من Excel</p>
            </div>
            <div class="feature">
                <div class="feature-icon">📚</div>
                <h3>إدارة الدورات</h3>
                <p>إنشاء وتنظيم الدورات التدريبية مع متابعة التقدم</p>
            </div>
            <div class="feature">
                <div class="feature-icon">📊</div>
                <h3>التقارير المتقدمة</h3>
                <p>تقارير شاملة وإحصائيات مفصلة مع إمكانية التصدير</p>
            </div>
            <div class="feature">
                <div class="feature-icon">🔍</div>
                <h3>البحث الذكي</h3>
                <p>بحث متقدم وفلترة ذكية للبيانات والسجلات</p>
            </div>
        </div>

        <a href="/login" class="login-btn">
            🚀 دخول النظام
        </a>

        <div class="status">
            ✅ النظام يعمل بشكل طبيعي - جاهز للاستخدام
        </div>

        <div class="version">
            الإصدار 1.0.0 | © 2024 نظام إدارة التدريب
        </div>
    </div>

    <script>
        // التحقق من حالة النظام
        fetch('/api/system/status')
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    console.log('✅ النظام يعمل بشكل طبيعي');
                } else {
                    console.log('⚠️ مشكلة في النظام:', data.error);
                }
            })
            .catch(error => {
                console.log('⚠️ خطأ في الاتصال:', error);
            });

        // تأثيرات بصرية
        document.addEventListener('DOMContentLoaded', function() {
            const features = document.querySelectorAll('.feature');
            features.forEach((feature, index) => {
                feature.style.animationDelay = `${index * 0.1}s`;
                feature.style.animation = 'fadeInUp 0.6s ease forwards';
            });
        });

        // إضافة CSS للتأثيرات
        const style = document.createElement('style');
        style.textContent = `
            @keyframes fadeInUp {
                from {
                    opacity: 0;
                    transform: translateY(30px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
