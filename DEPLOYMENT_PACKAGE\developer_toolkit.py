#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أداة إدارة التطوير والتوزيع المتقدمة
Advanced Development & Deployment Management Tool

أداة رسومية شاملة للمطورين لإدارة عملية التطوير والتوزيع
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog, scrolledtext
import threading
import subprocess
import os
import sys
import json
import sqlite3
import shutil
from datetime import datetime
import webbrowser
from pathlib import Path

class DeveloperToolkit:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🚀 أداة إدارة التطوير والتوزيع المتقدمة - Developer Toolkit")
        self.root.geometry("1400x900")
        self.root.minsize(1200, 800)
        
        # متغيرات التحكم
        self.project_path = tk.StringVar()
        self.current_step = tk.StringVar(value="جاهز للبدء")
        self.progress_var = tk.DoubleVar()
        self.log_text = None
        self.is_running = False
        
        # إعدادات المشروع
        self.project_config = {
            'name': 'Training Management System',
            'version': '1.0.0',
            'database_type': 'empty',  # empty, full, update
            'build_type': 'optimized',  # basic, optimized, portable
            'include_samples': False,
            'create_installer': True,
            'auto_test': True
        }
        
        self.setup_ui()
        self.load_project_config()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        
        # إعداد الألوان والأنماط
        style = ttk.Style()
        style.theme_use('clam')
        
        # ألوان مخصصة
        self.colors = {
            'primary': '#2E86AB',      # أزرق أساسي
            'secondary': '#A23B72',    # بنفسجي ثانوي
            'success': '#F18F01',      # برتقالي للنجاح
            'warning': '#C73E1D',      # أحمر للتحذير
            'background': '#F5F5F5',   # خلفية فاتحة
            'card': '#FFFFFF',         # خلفية البطاقات
            'text': '#2C3E50',         # نص أساسي
            'text_light': '#7F8C8D'    # نص فاتح
        }
        
        self.root.configure(bg=self.colors['background'])
        
        # إنشاء الإطار الرئيسي
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # شريط العنوان
        self.create_header(main_frame)
        
        # الإطار الأوسط - محتوى رئيسي
        content_frame = ttk.Frame(main_frame)
        content_frame.pack(fill=tk.BOTH, expand=True, pady=(10, 0))
        
        # الجانب الأيسر - التحكم والإعدادات
        left_panel = ttk.LabelFrame(content_frame, text="🎛️ لوحة التحكم", padding=10)
        left_panel.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 5))
        
        # الجانب الأيمن - المراقبة والسجلات
        right_panel = ttk.LabelFrame(content_frame, text="📊 المراقبة والسجلات", padding=10)
        right_panel.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(5, 0))
        
        # إعداد اللوحات
        self.setup_control_panel(left_panel)
        self.setup_monitoring_panel(right_panel)
        
        # شريط الحالة
        self.create_status_bar(main_frame)
        
    def create_header(self, parent):
        """إنشاء شريط العنوان"""
        header_frame = tk.Frame(parent, bg=self.colors['primary'], height=80)
        header_frame.pack(fill=tk.X, pady=(0, 10))
        header_frame.pack_propagate(False)
        
        # العنوان الرئيسي
        title_label = tk.Label(
            header_frame,
            text="🚀 أداة إدارة التطوير والتوزيع المتقدمة",
            font=('Arial', 18, 'bold'),
            fg='white',
            bg=self.colors['primary']
        )
        title_label.pack(side=tk.LEFT, padx=20, pady=20)
        
        # معلومات المشروع
        project_info = tk.Label(
            header_frame,
            text=f"📦 {self.project_config['name']} v{self.project_config['version']}",
            font=('Arial', 12),
            fg='white',
            bg=self.colors['primary']
        )
        project_info.pack(side=tk.RIGHT, padx=20, pady=20)
        
    def setup_control_panel(self, parent):
        """إعداد لوحة التحكم"""
        
        # اختيار مسار المشروع
        path_frame = ttk.LabelFrame(parent, text="📁 مسار المشروع", padding=5)
        path_frame.pack(fill=tk.X, pady=(0, 10))
        
        path_entry = ttk.Entry(path_frame, textvariable=self.project_path, width=40)
        path_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 5))
        
        browse_btn = ttk.Button(path_frame, text="تصفح", command=self.browse_project_path)
        browse_btn.pack(side=tk.RIGHT)
        
        # إعدادات المشروع
        settings_frame = ttk.LabelFrame(parent, text="⚙️ إعدادات المشروع", padding=5)
        settings_frame.pack(fill=tk.X, pady=(0, 10))
        
        # نوع قاعدة البيانات
        db_frame = ttk.Frame(settings_frame)
        db_frame.pack(fill=tk.X, pady=2)
        
        ttk.Label(db_frame, text="نوع قاعدة البيانات:").pack(side=tk.LEFT)
        
        self.db_type_var = tk.StringVar(value=self.project_config['database_type'])
        db_combo = ttk.Combobox(
            db_frame, 
            textvariable=self.db_type_var,
            values=['empty', 'full', 'update'],
            state='readonly',
            width=15
        )
        db_combo.pack(side=tk.RIGHT)
        
        # نوع البناء
        build_frame = ttk.Frame(settings_frame)
        build_frame.pack(fill=tk.X, pady=2)
        
        ttk.Label(build_frame, text="نوع البناء:").pack(side=tk.LEFT)
        
        self.build_type_var = tk.StringVar(value=self.project_config['build_type'])
        build_combo = ttk.Combobox(
            build_frame,
            textvariable=self.build_type_var,
            values=['basic', 'optimized', 'portable'],
            state='readonly',
            width=15
        )
        build_combo.pack(side=tk.RIGHT)
        
        # خيارات إضافية
        options_frame = ttk.LabelFrame(parent, text="🔧 خيارات إضافية", padding=5)
        options_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.include_samples_var = tk.BooleanVar(value=self.project_config['include_samples'])
        ttk.Checkbutton(
            options_frame,
            text="تضمين بيانات تجريبية",
            variable=self.include_samples_var
        ).pack(anchor=tk.W)
        
        self.create_installer_var = tk.BooleanVar(value=self.project_config['create_installer'])
        ttk.Checkbutton(
            options_frame,
            text="إنشاء ملف تثبيت",
            variable=self.create_installer_var
        ).pack(anchor=tk.W)
        
        self.auto_test_var = tk.BooleanVar(value=self.project_config['auto_test'])
        ttk.Checkbutton(
            options_frame,
            text="اختبار تلقائي بعد البناء",
            variable=self.auto_test_var
        ).pack(anchor=tk.W)
        
        # أزرار العمليات الرئيسية
        actions_frame = ttk.LabelFrame(parent, text="🎯 العمليات الرئيسية", padding=5)
        actions_frame.pack(fill=tk.X, pady=(0, 10))
        
        # زر التحليل والفحص
        analyze_btn = ttk.Button(
            actions_frame,
            text="🔍 تحليل وفحص المشروع",
            command=self.analyze_project,
            style='Accent.TButton'
        )
        analyze_btn.pack(fill=tk.X, pady=2)
        
        # زر البناء الكامل
        build_btn = ttk.Button(
            actions_frame,
            text="🚀 بناء كامل",
            command=self.start_full_build,
            style='Accent.TButton'
        )
        build_btn.pack(fill=tk.X, pady=2)
        
        # زر البناء المخصص
        custom_build_btn = ttk.Button(
            actions_frame,
            text="🛠️ بناء مخصص",
            command=self.start_custom_build
        )
        custom_build_btn.pack(fill=tk.X, pady=2)
        
        # زر إدارة قاعدة البيانات
        db_btn = ttk.Button(
            actions_frame,
            text="🗄️ إدارة قاعدة البيانات",
            command=self.manage_database
        )
        db_btn.pack(fill=tk.X, pady=2)
        
        # زر الاختبار
        test_btn = ttk.Button(
            actions_frame,
            text="🧪 اختبار النظام",
            command=self.test_system
        )
        test_btn.pack(fill=tk.X, pady=2)
        
        # أدوات إضافية
        tools_frame = ttk.LabelFrame(parent, text="🔧 أدوات إضافية", padding=5)
        tools_frame.pack(fill=tk.X, pady=(0, 10))
        
        # زر تنظيف المشروع
        clean_btn = ttk.Button(
            tools_frame,
            text="🧹 تنظيف المشروع",
            command=self.clean_project
        )
        clean_btn.pack(fill=tk.X, pady=1)
        
        # زر النسخ الاحتياطي
        backup_btn = ttk.Button(
            tools_frame,
            text="💾 نسخ احتياطي",
            command=self.create_backup
        )
        backup_btn.pack(fill=tk.X, pady=1)
        
        # زر فتح مجلد المشروع
        open_folder_btn = ttk.Button(
            tools_frame,
            text="📁 فتح مجلد المشروع",
            command=self.open_project_folder
        )
        open_folder_btn.pack(fill=tk.X, pady=1)
        
    def setup_monitoring_panel(self, parent):
        """إعداد لوحة المراقبة"""
        
        # شريط التقدم الرئيسي
        progress_frame = ttk.LabelFrame(parent, text="📈 تقدم العملية", padding=5)
        progress_frame.pack(fill=tk.X, pady=(0, 10))
        
        # نص الخطوة الحالية
        self.step_label = ttk.Label(
            progress_frame,
            textvariable=self.current_step,
            font=('Arial', 10, 'bold')
        )
        self.step_label.pack(pady=(0, 5))
        
        # شريط التقدم
        self.progress_bar = ttk.Progressbar(
            progress_frame,
            variable=self.progress_var,
            maximum=100,
            length=400,
            mode='determinate'
        )
        self.progress_bar.pack(fill=tk.X, pady=(0, 5))
        
        # نسبة التقدم
        self.progress_label = ttk.Label(progress_frame, text="0%")
        self.progress_label.pack()
        
        # معلومات المشروع
        info_frame = ttk.LabelFrame(parent, text="ℹ️ معلومات المشروع", padding=5)
        info_frame.pack(fill=tk.X, pady=(0, 10))
        
        # إنشاء جدول المعلومات
        self.info_tree = ttk.Treeview(info_frame, height=6, show='tree headings')
        self.info_tree['columns'] = ('value',)
        self.info_tree.heading('#0', text='المعلومة')
        self.info_tree.heading('value', text='القيمة')
        self.info_tree.column('#0', width=150)
        self.info_tree.column('value', width=200)
        self.info_tree.pack(fill=tk.X)
        
        # سجل العمليات
        log_frame = ttk.LabelFrame(parent, text="📝 سجل العمليات", padding=5)
        log_frame.pack(fill=tk.BOTH, expand=True)
        
        # منطقة النص مع شريط التمرير
        self.log_text = scrolledtext.ScrolledText(
            log_frame,
            height=15,
            font=('Consolas', 9),
            bg='#1e1e1e',
            fg='#ffffff',
            insertbackground='white'
        )
        self.log_text.pack(fill=tk.BOTH, expand=True)
        
        # أزرار إدارة السجل
        log_buttons_frame = ttk.Frame(log_frame)
        log_buttons_frame.pack(fill=tk.X, pady=(5, 0))
        
        ttk.Button(
            log_buttons_frame,
            text="🗑️ مسح السجل",
            command=self.clear_log
        ).pack(side=tk.LEFT, padx=(0, 5))
        
        ttk.Button(
            log_buttons_frame,
            text="💾 حفظ السجل",
            command=self.save_log
        ).pack(side=tk.LEFT, padx=(0, 5))
        
        ttk.Button(
            log_buttons_frame,
            text="🔄 تحديث",
            command=self.refresh_info
        ).pack(side=tk.RIGHT)
        
    def create_status_bar(self, parent):
        """إنشاء شريط الحالة"""
        status_frame = tk.Frame(parent, bg=self.colors['card'], height=30)
        status_frame.pack(fill=tk.X, pady=(10, 0))
        status_frame.pack_propagate(False)
        
        # حالة الاتصال
        self.status_label = tk.Label(
            status_frame,
            text="🟢 جاهز",
            bg=self.colors['card'],
            fg=self.colors['text'],
            font=('Arial', 9)
        )
        self.status_label.pack(side=tk.LEFT, padx=10, pady=5)
        
        # الوقت
        self.time_label = tk.Label(
            status_frame,
            text="",
            bg=self.colors['card'],
            fg=self.colors['text_light'],
            font=('Arial', 9)
        )
        self.time_label.pack(side=tk.RIGHT, padx=10, pady=5)
        
        # تحديث الوقت
        self.update_time()
        
    def update_time(self):
        """تحديث الوقت في شريط الحالة"""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.time_label.config(text=current_time)
        self.root.after(1000, self.update_time)
        
    def log_message(self, message, level="INFO"):
        """إضافة رسالة إلى السجل"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        
        # ألوان مختلفة حسب نوع الرسالة
        colors = {
            "INFO": "#00ff00",      # أخضر
            "WARNING": "#ffff00",   # أصفر
            "ERROR": "#ff0000",     # أحمر
            "SUCCESS": "#00ffff",   # سماوي
            "DEBUG": "#ffffff"      # أبيض
        }
        
        color = colors.get(level, "#ffffff")
        
        # إدراج الرسالة
        self.log_text.insert(tk.END, f"[{timestamp}] [{level}] {message}\n")
        
        # تلوين الرسالة
        start_line = self.log_text.index(tk.END + "-2l linestart")
        end_line = self.log_text.index(tk.END + "-1l lineend")
        self.log_text.tag_add(level, start_line, end_line)
        self.log_text.tag_config(level, foreground=color)
        
        # التمرير إلى النهاية
        self.log_text.see(tk.END)
        
        # تحديث الواجهة
        self.root.update_idletasks()
        
    def update_progress(self, value, step_text=""):
        """تحديث شريط التقدم"""
        self.progress_var.set(value)
        self.progress_label.config(text=f"{value:.1f}%")
        
        if step_text:
            self.current_step.set(step_text)
            
        self.root.update_idletasks()
        
    def browse_project_path(self):
        """تصفح مسار المشروع"""
        folder = filedialog.askdirectory(title="اختر مجلد المشروع")
        if folder:
            self.project_path.set(folder)
            self.log_message(f"تم تحديد مسار المشروع: {folder}")
            self.refresh_info()
            
    def load_project_config(self):
        """تحميل إعدادات المشروع"""
        config_file = "project_config.json"
        if os.path.exists(config_file):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    self.project_config.update(json.load(f))
                self.log_message("تم تحميل إعدادات المشروع")
            except Exception as e:
                self.log_message(f"خطأ في تحميل الإعدادات: {e}", "ERROR")
                
    def save_project_config(self):
        """حفظ إعدادات المشروع"""
        self.project_config.update({
            'database_type': self.db_type_var.get(),
            'build_type': self.build_type_var.get(),
            'include_samples': self.include_samples_var.get(),
            'create_installer': self.create_installer_var.get(),
            'auto_test': self.auto_test_var.get()
        })
        
        try:
            with open("project_config.json", 'w', encoding='utf-8') as f:
                json.dump(self.project_config, f, indent=2, ensure_ascii=False)
            self.log_message("تم حفظ إعدادات المشروع")
        except Exception as e:
            self.log_message(f"خطأ في حفظ الإعدادات: {e}", "ERROR")

    def refresh_info(self):
        """تحديث معلومات المشروع"""
        # مسح المعلومات الحالية
        for item in self.info_tree.get_children():
            self.info_tree.delete(item)

        project_path = self.project_path.get()
        if not project_path or not os.path.exists(project_path):
            self.info_tree.insert('', 'end', text='حالة المشروع', values=('غير محدد',))
            return

        try:
            # معلومات أساسية
            self.info_tree.insert('', 'end', text='مسار المشروع', values=(project_path,))

            # فحص الملفات الأساسية
            main_files = ['app.py', 'main.py', 'run.py']
            main_file = None
            for file in main_files:
                if os.path.exists(os.path.join(project_path, file)):
                    main_file = file
                    break

            self.info_tree.insert('', 'end', text='الملف الرئيسي',
                                values=(main_file if main_file else 'غير موجود',))

            # فحص قاعدة البيانات
            db_files = [f for f in os.listdir(project_path) if f.endswith('.db')]
            db_status = f"{len(db_files)} ملف" if db_files else "لا توجد"
            self.info_tree.insert('', 'end', text='قواعد البيانات', values=(db_status,))

            # فحص requirements.txt
            req_file = os.path.join(project_path, 'requirements.txt')
            req_status = "موجود" if os.path.exists(req_file) else "غير موجود"
            self.info_tree.insert('', 'end', text='ملف المتطلبات', values=(req_status,))

            # فحص مجلد static
            static_dir = os.path.join(project_path, 'static')
            static_status = "موجود" if os.path.exists(static_dir) else "غير موجود"
            self.info_tree.insert('', 'end', text='مجلد static', values=(static_status,))

            # فحص مجلد templates
            templates_dir = os.path.join(project_path, 'templates')
            templates_status = "موجود" if os.path.exists(templates_dir) else "غير موجود"
            self.info_tree.insert('', 'end', text='مجلد templates', values=(templates_status,))

            self.log_message("تم تحديث معلومات المشروع")

        except Exception as e:
            self.log_message(f"خطأ في تحديث المعلومات: {e}", "ERROR")

    def analyze_project(self):
        """تحليل وفحص المشروع"""
        if not self.project_path.get():
            messagebox.showwarning("تحذير", "يرجى تحديد مسار المشروع أولاً")
            return

        self.log_message("🔍 بدء تحليل المشروع...", "INFO")
        self.update_progress(0, "تحليل بنية المشروع...")

        def analyze():
            try:
                project_path = self.project_path.get()
                issues = []
                recommendations = []

                # فحص الملف الرئيسي
                self.update_progress(10, "فحص الملف الرئيسي...")
                main_files = ['app.py', 'main.py', 'run.py']
                main_file_found = False
                for file in main_files:
                    if os.path.exists(os.path.join(project_path, file)):
                        main_file_found = True
                        self.log_message(f"✅ تم العثور على الملف الرئيسي: {file}")
                        break

                if not main_file_found:
                    issues.append("❌ لم يتم العثور على ملف رئيسي (app.py, main.py, run.py)")

                # فحص requirements.txt
                self.update_progress(20, "فحص ملف المتطلبات...")
                req_file = os.path.join(project_path, 'requirements.txt')
                if os.path.exists(req_file):
                    self.log_message("✅ ملف requirements.txt موجود")
                    # فحص محتوى الملف
                    with open(req_file, 'r', encoding='utf-8') as f:
                        requirements = f.read()
                        if 'Flask' in requirements:
                            self.log_message("✅ Flask مدرج في المتطلبات")
                        else:
                            issues.append("⚠️ Flask غير مدرج في requirements.txt")
                else:
                    issues.append("❌ ملف requirements.txt غير موجود")
                    recommendations.append("💡 إنشاء ملف requirements.txt")

                # فحص قاعدة البيانات
                self.update_progress(30, "فحص قاعدة البيانات...")
                db_files = [f for f in os.listdir(project_path) if f.endswith('.db')]
                if db_files:
                    self.log_message(f"✅ تم العثور على {len(db_files)} ملف قاعدة بيانات")
                    for db_file in db_files:
                        db_path = os.path.join(project_path, db_file)
                        try:
                            conn = sqlite3.connect(db_path)
                            cursor = conn.cursor()
                            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                            tables = cursor.fetchall()
                            conn.close()
                            self.log_message(f"  📋 {db_file}: {len(tables)} جدول")
                        except Exception as e:
                            issues.append(f"❌ خطأ في قاعدة البيانات {db_file}: {e}")
                else:
                    recommendations.append("💡 إنشاء قاعدة بيانات")

                # فحص المجلدات الأساسية
                self.update_progress(40, "فحص المجلدات...")
                essential_dirs = ['static', 'templates']
                for dir_name in essential_dirs:
                    dir_path = os.path.join(project_path, dir_name)
                    if os.path.exists(dir_path):
                        files_count = len([f for f in os.listdir(dir_path)
                                         if os.path.isfile(os.path.join(dir_path, f))])
                        self.log_message(f"✅ مجلد {dir_name}: {files_count} ملف")
                    else:
                        issues.append(f"❌ مجلد {dir_name} غير موجود")

                # فحص ملفات Python
                self.update_progress(50, "فحص ملفات Python...")
                py_files = [f for f in os.listdir(project_path) if f.endswith('.py')]
                self.log_message(f"📄 تم العثور على {len(py_files)} ملف Python")

                # فحص الاستيرادات
                self.update_progress(60, "فحص الاستيرادات...")
                imports_found = set()
                for py_file in py_files:
                    try:
                        with open(os.path.join(project_path, py_file), 'r', encoding='utf-8') as f:
                            content = f.read()
                            # البحث عن استيرادات شائعة
                            common_imports = ['flask', 'sqlite3', 'os', 'sys', 'datetime']
                            for imp in common_imports:
                                if f'import {imp}' in content or f'from {imp}' in content:
                                    imports_found.add(imp)
                    except Exception as e:
                        self.log_message(f"⚠️ خطأ في قراءة {py_file}: {e}", "WARNING")

                self.log_message(f"📦 الاستيرادات المكتشفة: {', '.join(imports_found)}")

                # فحص حجم المشروع
                self.update_progress(70, "حساب حجم المشروع...")
                total_size = 0
                file_count = 0
                for root, dirs, files in os.walk(project_path):
                    for file in files:
                        file_path = os.path.join(root, file)
                        try:
                            total_size += os.path.getsize(file_path)
                            file_count += 1
                        except:
                            pass

                size_mb = total_size / (1024 * 1024)
                self.log_message(f"📏 حجم المشروع: {size_mb:.2f} MB ({file_count} ملف)")

                # تقييم جودة المشروع
                self.update_progress(80, "تقييم جودة المشروع...")
                score = 100
                score -= len(issues) * 10
                score = max(0, score)

                quality_level = "ممتاز" if score >= 90 else "جيد" if score >= 70 else "متوسط" if score >= 50 else "يحتاج تحسين"
                quality_color = "SUCCESS" if score >= 70 else "WARNING" if score >= 50 else "ERROR"

                self.log_message(f"🏆 تقييم جودة المشروع: {score}/100 ({quality_level})", quality_color)

                # عرض النتائج
                self.update_progress(90, "إعداد التقرير...")

                if issues:
                    self.log_message("❌ المشاكل المكتشفة:", "ERROR")
                    for issue in issues:
                        self.log_message(f"  {issue}", "ERROR")

                if recommendations:
                    self.log_message("💡 التوصيات:", "INFO")
                    for rec in recommendations:
                        self.log_message(f"  {rec}", "INFO")

                self.update_progress(100, "تم الانتهاء من التحليل")
                self.log_message("✅ تم الانتهاء من تحليل المشروع", "SUCCESS")

                # عرض ملخص
                summary = f"""
📊 ملخص التحليل:
• الملفات: {file_count}
• الحجم: {size_mb:.2f} MB
• المشاكل: {len(issues)}
• التوصيات: {len(recommendations)}
• التقييم: {score}/100
                """

                messagebox.showinfo("نتائج التحليل", summary.strip())

            except Exception as e:
                self.log_message(f"❌ خطأ في التحليل: {e}", "ERROR")
                messagebox.showerror("خطأ", f"حدث خطأ أثناء التحليل:\n{e}")
            finally:
                self.is_running = False

        # تشغيل التحليل في خيط منفصل
        self.is_running = True
        thread = threading.Thread(target=analyze)
        thread.daemon = True
        thread.start()

    def start_full_build(self):
        """بدء البناء الكامل"""
        if not self.project_path.get():
            messagebox.showwarning("تحذير", "يرجى تحديد مسار المشروع أولاً")
            return

        if self.is_running:
            messagebox.showwarning("تحذير", "عملية أخرى قيد التشغيل")
            return

        # حفظ الإعدادات
        self.save_project_config()

        # تأكيد البدء
        result = messagebox.askyesno(
            "تأكيد البناء",
            "هل تريد بدء عملية البناء الكاملة؟\n\nسيتم تنفيذ جميع الخطوات تلقائياً."
        )

        if not result:
            return

        self.log_message("🚀 بدء عملية البناء الكاملة...", "INFO")

        def build_process():
            try:
                self.is_running = True
                project_path = self.project_path.get()
                os.chdir(project_path)

                steps = [
                    ("تنظيف المشروع", "cleanup_project.bat", 10),
                    ("إنشاء بيئة افتراضية", "create_clean_environment.bat", 20),
                    ("تثبيت المتطلبات", "install_requirements.bat", 35),
                    ("تحضير قاعدة البيانات", "prepare_database.bat", 50),
                    ("بناء ملف EXE", "build_optimized_exe.bat", 75),
                    ("تحسين الملفات", "optimize_files.bat", 85),
                    ("اختبار النظام", "test_complete_system.bat", 95),
                    ("إنشاء حزمة العميل", "create_client_package.bat", 100)
                ]

                for step_name, script_name, progress in steps:
                    self.update_progress(progress, f"تنفيذ: {step_name}")
                    self.log_message(f"▶️ {step_name}...", "INFO")

                    script_path = os.path.join("DEPLOYMENT_PACKAGE", script_name)
                    if os.path.exists(script_path):
                        try:
                            result = subprocess.run(
                                [script_path],
                                capture_output=True,
                                text=True,
                                encoding='utf-8',
                                shell=True,
                                timeout=600  # 10 دقائق لكل خطوة
                            )

                            if result.returncode == 0:
                                self.log_message(f"✅ {step_name} - مكتمل", "SUCCESS")
                            else:
                                self.log_message(f"❌ {step_name} - فشل", "ERROR")
                                if result.stderr:
                                    self.log_message(f"خطأ: {result.stderr}", "ERROR")
                                break

                        except subprocess.TimeoutExpired:
                            self.log_message(f"⏰ {step_name} - انتهت المهلة الزمنية", "ERROR")
                            break
                        except Exception as e:
                            self.log_message(f"❌ {step_name} - خطأ: {e}", "ERROR")
                            break
                    else:
                        self.log_message(f"⚠️ {step_name} - السكريبت غير موجود: {script_path}", "WARNING")

                self.log_message("🎉 تم الانتهاء من عملية البناء!", "SUCCESS")
                messagebox.showinfo("مكتمل", "تم الانتهاء من عملية البناء بنجاح!")

            except Exception as e:
                self.log_message(f"❌ خطأ في عملية البناء: {e}", "ERROR")
                messagebox.showerror("خطأ", f"حدث خطأ أثناء البناء:\n{e}")
            finally:
                self.is_running = False
                self.update_progress(0, "جاهز للبدء")

        # تشغيل البناء في خيط منفصل
        thread = threading.Thread(target=build_process)
        thread.daemon = True
        thread.start()

    def start_custom_build(self):
        """بدء البناء المخصص"""
        if not self.project_path.get():
            messagebox.showwarning("تحذير", "يرجى تحديد مسار المشروع أولاً")
            return

        # نافذة اختيار الخطوات
        custom_window = tk.Toplevel(self.root)
        custom_window.title("🛠️ البناء المخصص")
        custom_window.geometry("500x600")
        custom_window.resizable(False, False)

        # جعل النافذة في المقدمة
        custom_window.transient(self.root)
        custom_window.grab_set()

        # إطار الخطوات
        steps_frame = ttk.LabelFrame(custom_window, text="اختر الخطوات المطلوبة", padding=10)
        steps_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # قائمة الخطوات
        self.custom_steps = {}
        steps_list = [
            ("cleanup", "🧹 تنظيف المشروع", True),
            ("environment", "🆕 إنشاء بيئة افتراضية", True),
            ("requirements", "📦 تثبيت المتطلبات", True),
            ("database", "🗄️ تحضير قاعدة البيانات", True),
            ("build", "🔨 بناء ملف EXE", True),
            ("optimize", "⚡ تحسين الملفات", False),
            ("test", "🧪 اختبار النظام", False),
            ("package", "📦 إنشاء حزمة العميل", False)
        ]

        for step_id, step_name, default_value in steps_list:
            var = tk.BooleanVar(value=default_value)
            self.custom_steps[step_id] = var

            cb = ttk.Checkbutton(steps_frame, text=step_name, variable=var)
            cb.pack(anchor=tk.W, pady=2)

        # أزرار التحكم
        buttons_frame = ttk.Frame(custom_window)
        buttons_frame.pack(fill=tk.X, padx=10, pady=10)

        ttk.Button(
            buttons_frame,
            text="تحديد الكل",
            command=lambda: [var.set(True) for var in self.custom_steps.values()]
        ).pack(side=tk.LEFT, padx=(0, 5))

        ttk.Button(
            buttons_frame,
            text="إلغاء تحديد الكل",
            command=lambda: [var.set(False) for var in self.custom_steps.values()]
        ).pack(side=tk.LEFT, padx=(0, 5))

        ttk.Button(
            buttons_frame,
            text="🚀 بدء البناء",
            command=lambda: self.execute_custom_build(custom_window)
        ).pack(side=tk.RIGHT, padx=(5, 0))

        ttk.Button(
            buttons_frame,
            text="إلغاء",
            command=custom_window.destroy
        ).pack(side=tk.RIGHT)

    def execute_custom_build(self, window):
        """تنفيذ البناء المخصص"""
        # التحقق من اختيار خطوة واحدة على الأقل
        selected_steps = [step_id for step_id, var in self.custom_steps.items() if var.get()]

        if not selected_steps:
            messagebox.showwarning("تحذير", "يرجى اختيار خطوة واحدة على الأقل")
            return

        window.destroy()

        self.log_message(f"🛠️ بدء البناء المخصص - {len(selected_steps)} خطوة", "INFO")

        def custom_build_process():
            try:
                self.is_running = True
                project_path = self.project_path.get()
                os.chdir(project_path)

                # خريطة الخطوات
                steps_map = {
                    "cleanup": ("تنظيف المشروع", "cleanup_project.bat"),
                    "environment": ("إنشاء بيئة افتراضية", "create_clean_environment.bat"),
                    "requirements": ("تثبيت المتطلبات", "install_requirements.bat"),
                    "database": ("تحضير قاعدة البيانات", "prepare_database.bat"),
                    "build": ("بناء ملف EXE", "build_optimized_exe.bat"),
                    "optimize": ("تحسين الملفات", "optimize_files.bat"),
                    "test": ("اختبار النظام", "test_complete_system.bat"),
                    "package": ("إنشاء حزمة العميل", "create_client_package.bat")
                }

                total_steps = len(selected_steps)

                for i, step_id in enumerate(selected_steps):
                    step_name, script_name = steps_map[step_id]
                    progress = ((i + 1) / total_steps) * 100

                    self.update_progress(progress, f"تنفيذ: {step_name}")
                    self.log_message(f"▶️ {step_name}...", "INFO")

                    script_path = os.path.join("DEPLOYMENT_PACKAGE", script_name)
                    if os.path.exists(script_path):
                        try:
                            result = subprocess.run(
                                [script_path],
                                capture_output=True,
                                text=True,
                                encoding='utf-8',
                                shell=True,
                                timeout=600
                            )

                            if result.returncode == 0:
                                self.log_message(f"✅ {step_name} - مكتمل", "SUCCESS")
                            else:
                                self.log_message(f"❌ {step_name} - فشل", "ERROR")
                                if result.stderr:
                                    self.log_message(f"خطأ: {result.stderr}", "ERROR")

                                # سؤال المستخدم عن المتابعة
                                continue_build = messagebox.askyesno(
                                    "خطأ في الخطوة",
                                    f"فشلت خطوة '{step_name}'.\nهل تريد المتابعة مع الخطوات التالية؟"
                                )
                                if not continue_build:
                                    break

                        except subprocess.TimeoutExpired:
                            self.log_message(f"⏰ {step_name} - انتهت المهلة الزمنية", "ERROR")
                            break
                        except Exception as e:
                            self.log_message(f"❌ {step_name} - خطأ: {e}", "ERROR")
                            break
                    else:
                        self.log_message(f"⚠️ {step_name} - السكريبت غير موجود", "WARNING")

                self.log_message("🎉 تم الانتهاء من البناء المخصص!", "SUCCESS")
                messagebox.showinfo("مكتمل", "تم الانتهاء من البناء المخصص!")

            except Exception as e:
                self.log_message(f"❌ خطأ في البناء المخصص: {e}", "ERROR")
                messagebox.showerror("خطأ", f"حدث خطأ أثناء البناء:\n{e}")
            finally:
                self.is_running = False
                self.update_progress(0, "جاهز للبدء")

        # تشغيل البناء في خيط منفصل
        thread = threading.Thread(target=custom_build_process)
        thread.daemon = True
        thread.start()

    def manage_database(self):
        """إدارة قاعدة البيانات"""
        if not self.project_path.get():
            messagebox.showwarning("تحذير", "يرجى تحديد مسار المشروع أولاً")
            return

        # نافذة إدارة قاعدة البيانات
        db_window = tk.Toplevel(self.root)
        db_window.title("🗄️ إدارة قاعدة البيانات")
        db_window.geometry("600x500")

        db_window.transient(self.root)
        db_window.grab_set()

        # معلومات قاعدة البيانات الحالية
        info_frame = ttk.LabelFrame(db_window, text="معلومات قاعدة البيانات", padding=10)
        info_frame.pack(fill=tk.X, padx=10, pady=10)

        # عرض معلومات قواعد البيانات
        project_path = self.project_path.get()
        db_files = [f for f in os.listdir(project_path) if f.endswith('.db')]

        if db_files:
            for db_file in db_files:
                db_path = os.path.join(project_path, db_file)
                try:
                    conn = sqlite3.connect(db_path)
                    cursor = conn.cursor()
                    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                    tables = cursor.fetchall()
                    conn.close()

                    size = os.path.getsize(db_path) / 1024  # KB

                    info_text = f"📄 {db_file}\n  📋 {len(tables)} جدول\n  📏 {size:.1f} KB"
                    ttk.Label(info_frame, text=info_text).pack(anchor=tk.W, pady=2)

                except Exception as e:
                    ttk.Label(info_frame, text=f"❌ {db_file}: خطأ - {e}").pack(anchor=tk.W, pady=2)
        else:
            ttk.Label(info_frame, text="❌ لا توجد قواعد بيانات").pack(anchor=tk.W)

        # خيارات قاعدة البيانات
        options_frame = ttk.LabelFrame(db_window, text="خيارات قاعدة البيانات", padding=10)
        options_frame.pack(fill=tk.X, padx=10, pady=10)

        # أزرار العمليات
        ttk.Button(
            options_frame,
            text="🗄️ إنشاء قاعدة بيانات فارغة",
            command=lambda: self.create_database("empty", db_window)
        ).pack(fill=tk.X, pady=2)

        ttk.Button(
            options_frame,
            text="📊 إنشاء قاعدة بيانات كاملة",
            command=lambda: self.create_database("full", db_window)
        ).pack(fill=tk.X, pady=2)

        ttk.Button(
            options_frame,
            text="🔄 تحديث قاعدة البيانات الموجودة",
            command=lambda: self.create_database("update", db_window)
        ).pack(fill=tk.X, pady=2)

        ttk.Button(
            options_frame,
            text="💾 نسخ احتياطي",
            command=lambda: self.backup_database(db_window)
        ).pack(fill=tk.X, pady=2)

        ttk.Button(
            options_frame,
            text="🔍 فحص تفصيلي",
            command=lambda: self.analyze_database(db_window)
        ).pack(fill=tk.X, pady=2)

        # زر الإغلاق
        ttk.Button(
            db_window,
            text="إغلاق",
            command=db_window.destroy
        ).pack(pady=10)

    def create_database(self, db_type, parent_window):
        """إنشاء قاعدة البيانات"""
        parent_window.destroy()

        self.log_message(f"🗄️ إنشاء قاعدة بيانات نوع: {db_type}", "INFO")

        def create_db_process():
            try:
                self.is_running = True
                project_path = self.project_path.get()
                os.chdir(project_path)

                # نسخ مدير قاعدة البيانات
                db_manager_path = os.path.join("DEPLOYMENT_PACKAGE", "database_manager.py")
                if os.path.exists(db_manager_path):
                    shutil.copy2(db_manager_path, "database_manager_temp.py")

                    # تشغيل إنشاء قاعدة البيانات
                    python_code = f"""
from database_manager_temp import DatabaseManager
db_manager = DatabaseManager()
"""

                    if db_type == "empty":
                        python_code += "success = db_manager.create_empty_database()"
                        self.update_progress(50, "إنشاء قاعدة بيانات فارغة...")
                    elif db_type == "full":
                        python_code += "success = db_manager.create_full_database()"
                        self.update_progress(50, "إنشاء قاعدة بيانات كاملة...")
                    elif db_type == "update":
                        python_code += "success = db_manager.update_existing_database()"
                        self.update_progress(50, "تحديث قاعدة البيانات...")

                    python_code += "\nprint('SUCCESS' if success else 'FAILED')"

                    result = subprocess.run(
                        [sys.executable, "-c", python_code],
                        capture_output=True,
                        text=True,
                        encoding='utf-8'
                    )

                    if "SUCCESS" in result.stdout:
                        self.log_message("✅ تم إنشاء/تحديث قاعدة البيانات بنجاح", "SUCCESS")
                        self.update_progress(100, "مكتمل")
                        messagebox.showinfo("نجح", "تم إنشاء/تحديث قاعدة البيانات بنجاح!")
                    else:
                        self.log_message("❌ فشل في إنشاء/تحديث قاعدة البيانات", "ERROR")
                        if result.stderr:
                            self.log_message(f"خطأ: {result.stderr}", "ERROR")

                    # تنظيف الملفات المؤقتة
                    if os.path.exists("database_manager_temp.py"):
                        os.remove("database_manager_temp.py")

                else:
                    self.log_message("❌ مدير قاعدة البيانات غير موجود", "ERROR")

            except Exception as e:
                self.log_message(f"❌ خطأ في إنشاء قاعدة البيانات: {e}", "ERROR")
            finally:
                self.is_running = False
                self.update_progress(0, "جاهز للبدء")

        thread = threading.Thread(target=create_db_process)
        thread.daemon = True
        thread.start()

    def backup_database(self, parent_window):
        """إنشاء نسخة احتياطية من قاعدة البيانات"""
        project_path = self.project_path.get()
        db_files = [f for f in os.listdir(project_path) if f.endswith('.db')]

        if not db_files:
            messagebox.showwarning("تحذير", "لا توجد قواعد بيانات للنسخ الاحتياطي")
            return

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_folder = f"backup_{timestamp}"
        backup_path = os.path.join(project_path, backup_folder)

        try:
            os.makedirs(backup_path, exist_ok=True)

            for db_file in db_files:
                src = os.path.join(project_path, db_file)
                dst = os.path.join(backup_path, db_file)
                shutil.copy2(src, dst)

            self.log_message(f"💾 تم إنشاء نسخة احتياطية: {backup_folder}", "SUCCESS")
            messagebox.showinfo("نجح", f"تم إنشاء نسخة احتياطية في:\n{backup_folder}")

        except Exception as e:
            self.log_message(f"❌ خطأ في النسخ الاحتياطي: {e}", "ERROR")
            messagebox.showerror("خطأ", f"فشل في إنشاء النسخة الاحتياطية:\n{e}")

    def analyze_database(self, parent_window):
        """تحليل قاعدة البيانات"""
        project_path = self.project_path.get()
        db_files = [f for f in os.listdir(project_path) if f.endswith('.db')]

        if not db_files:
            messagebox.showwarning("تحذير", "لا توجد قواعد بيانات للتحليل")
            return

        # نافذة التحليل
        analysis_window = tk.Toplevel(parent_window)
        analysis_window.title("🔍 تحليل قاعدة البيانات")
        analysis_window.geometry("700x500")

        # منطقة النتائج
        results_text = scrolledtext.ScrolledText(
            analysis_window,
            height=25,
            font=('Consolas', 10)
        )
        results_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # تحليل كل قاعدة بيانات
        for db_file in db_files:
            db_path = os.path.join(project_path, db_file)
            results_text.insert(tk.END, f"📊 تحليل قاعدة البيانات: {db_file}\n")
            results_text.insert(tk.END, "=" * 50 + "\n")

            try:
                conn = sqlite3.connect(db_path)
                cursor = conn.cursor()

                # معلومات عامة
                size = os.path.getsize(db_path)
                results_text.insert(tk.END, f"📏 الحجم: {size:,} بايت ({size/1024:.1f} KB)\n")

                # الجداول
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                tables = cursor.fetchall()
                results_text.insert(tk.END, f"📋 عدد الجداول: {len(tables)}\n\n")

                # تفاصيل كل جدول
                for (table_name,) in tables:
                    results_text.insert(tk.END, f"📄 جدول: {table_name}\n")

                    # عدد السجلات
                    cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                    count = cursor.fetchone()[0]
                    results_text.insert(tk.END, f"   📊 السجلات: {count:,}\n")

                    # الأعمدة
                    cursor.execute(f"PRAGMA table_info({table_name})")
                    columns = cursor.fetchall()
                    results_text.insert(tk.END, f"   📋 الأعمدة: {len(columns)}\n")

                    for col in columns:
                        col_name, col_type = col[1], col[2]
                        results_text.insert(tk.END, f"      • {col_name} ({col_type})\n")

                    results_text.insert(tk.END, "\n")

                conn.close()

            except Exception as e:
                results_text.insert(tk.END, f"❌ خطأ في تحليل {db_file}: {e}\n")

            results_text.insert(tk.END, "\n" + "=" * 50 + "\n\n")

        # زر الإغلاق
        ttk.Button(
            analysis_window,
            text="إغلاق",
            command=analysis_window.destroy
        ).pack(pady=10)

    def test_system(self):
        """اختبار النظام"""
        if not self.project_path.get():
            messagebox.showwarning("تحذير", "يرجى تحديد مسار المشروع أولاً")
            return

        self.log_message("🧪 بدء اختبار النظام...", "INFO")

        def test_process():
            try:
                self.is_running = True
                project_path = self.project_path.get()
                os.chdir(project_path)

                # البحث عن الملف التنفيذي
                self.update_progress(10, "البحث عن الملف التنفيذي...")

                exe_paths = [
                    "dist/TrainingSystem/TrainingSystem.exe",
                    "dist/TrainingSystem.exe",
                    "TrainingSystem.exe"
                ]

                exe_path = None
                for path in exe_paths:
                    if os.path.exists(path):
                        exe_path = path
                        break

                if not exe_path:
                    self.log_message("❌ لم يتم العثور على الملف التنفيذي", "ERROR")
                    messagebox.showerror("خطأ", "لم يتم العثور على الملف التنفيذي\nيرجى بناء المشروع أولاً")
                    return

                self.log_message(f"✅ تم العثور على الملف التنفيذي: {exe_path}", "SUCCESS")

                # اختبار تشغيل الملف
                self.update_progress(30, "اختبار تشغيل الملف...")

                try:
                    # تشغيل الملف التنفيذي
                    process = subprocess.Popen(
                        [exe_path],
                        stdout=subprocess.PIPE,
                        stderr=subprocess.PIPE,
                        text=True
                    )

                    self.log_message("🚀 تم تشغيل الملف التنفيذي", "INFO")
                    self.update_progress(50, "انتظار بدء النظام...")

                    # انتظار قصير للتأكد من بدء التشغيل
                    import time
                    time.sleep(5)

                    # التحقق من أن العملية ما زالت تعمل
                    if process.poll() is None:
                        self.log_message("✅ النظام يعمل بشكل طبيعي", "SUCCESS")

                        # محاولة الوصول للنظام عبر المتصفح
                        self.update_progress(70, "اختبار الوصول عبر المتصفح...")

                        try:
                            import requests
                            response = requests.get("http://localhost:5000", timeout=10)
                            if response.status_code == 200:
                                self.log_message("✅ النظام يستجيب عبر المتصفح", "SUCCESS")

                                # فتح المتصفح
                                webbrowser.open("http://localhost:5000")
                                self.log_message("🌐 تم فتح النظام في المتصفح", "INFO")
                            else:
                                self.log_message(f"⚠️ النظام يعمل لكن يستجيب بكود: {response.status_code}", "WARNING")

                        except requests.exceptions.RequestException:
                            self.log_message("⚠️ النظام يعمل لكن لا يستجيب عبر HTTP", "WARNING")
                        except ImportError:
                            self.log_message("⚠️ مكتبة requests غير متوفرة للاختبار", "WARNING")

                        self.update_progress(90, "إيقاف النظام...")

                        # إيقاف العملية
                        process.terminate()
                        time.sleep(2)

                        if process.poll() is None:
                            process.kill()

                        self.log_message("🛑 تم إيقاف النظام", "INFO")

                    else:
                        # العملية توقفت مبكراً
                        stdout, stderr = process.communicate()
                        self.log_message("❌ النظام توقف مبكراً", "ERROR")
                        if stderr:
                            self.log_message(f"خطأ: {stderr}", "ERROR")

                except Exception as e:
                    self.log_message(f"❌ خطأ في تشغيل النظام: {e}", "ERROR")

                self.update_progress(100, "انتهى الاختبار")
                self.log_message("🎉 انتهى اختبار النظام", "SUCCESS")

                messagebox.showinfo("اكتمل الاختبار", "تم الانتهاء من اختبار النظام\nراجع السجل للتفاصيل")

            except Exception as e:
                self.log_message(f"❌ خطأ في اختبار النظام: {e}", "ERROR")
                messagebox.showerror("خطأ", f"حدث خطأ أثناء الاختبار:\n{e}")
            finally:
                self.is_running = False
                self.update_progress(0, "جاهز للبدء")

        thread = threading.Thread(target=test_process)
        thread.daemon = True
        thread.start()

    def clean_project(self):
        """تنظيف المشروع"""
        if not self.project_path.get():
            messagebox.showwarning("تحذير", "يرجى تحديد مسار المشروع أولاً")
            return

        result = messagebox.askyesno(
            "تأكيد التنظيف",
            "سيتم حذف الملفات المؤقتة ومجلدات البناء\nهل تريد المتابعة؟"
        )

        if not result:
            return

        self.log_message("🧹 بدء تنظيف المشروع...", "INFO")

        try:
            project_path = self.project_path.get()

            # المجلدات المراد حذفها
            folders_to_clean = [
                "dist",
                "build",
                "__pycache__",
                "venv_deployment",
                ".pytest_cache"
            ]

            # الملفات المراد حذفها
            files_to_clean = [
                "*.pyc",
                "*.pyo",
                "*.spec",
                "*_temp.py"
            ]

            cleaned_items = 0

            # حذف المجلدات
            for folder in folders_to_clean:
                folder_path = os.path.join(project_path, folder)
                if os.path.exists(folder_path):
                    shutil.rmtree(folder_path)
                    self.log_message(f"🗑️ تم حذف مجلد: {folder}", "INFO")
                    cleaned_items += 1

            # حذف الملفات
            import glob
            for pattern in files_to_clean:
                files = glob.glob(os.path.join(project_path, pattern))
                for file in files:
                    os.remove(file)
                    self.log_message(f"🗑️ تم حذف ملف: {os.path.basename(file)}", "INFO")
                    cleaned_items += 1

            self.log_message(f"✅ تم تنظيف {cleaned_items} عنصر", "SUCCESS")
            messagebox.showinfo("مكتمل", f"تم تنظيف المشروع\nتم حذف {cleaned_items} عنصر")

        except Exception as e:
            self.log_message(f"❌ خطأ في التنظيف: {e}", "ERROR")
            messagebox.showerror("خطأ", f"حدث خطأ أثناء التنظيف:\n{e}")

    def create_backup(self):
        """إنشاء نسخة احتياطية من المشروع"""
        if not self.project_path.get():
            messagebox.showwarning("تحذير", "يرجى تحديد مسار المشروع أولاً")
            return

        # اختيار مكان الحفظ
        backup_path = filedialog.askdirectory(title="اختر مكان حفظ النسخة الاحتياطية")
        if not backup_path:
            return

        self.log_message("💾 بدء إنشاء نسخة احتياطية...", "INFO")

        def backup_process():
            try:
                self.is_running = True
                project_path = self.project_path.get()

                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                backup_name = f"TrainingSystem_Backup_{timestamp}"
                full_backup_path = os.path.join(backup_path, backup_name)

                self.update_progress(10, "إنشاء مجلد النسخة الاحتياطية...")
                os.makedirs(full_backup_path, exist_ok=True)

                # نسخ الملفات
                self.update_progress(30, "نسخ ملفات المشروع...")

                # استثناء بعض المجلدات من النسخ
                exclude_dirs = {'dist', 'build', '__pycache__', 'venv_deployment', '.git'}

                total_files = 0
                copied_files = 0

                # حساب عدد الملفات
                for root, dirs, files in os.walk(project_path):
                    dirs[:] = [d for d in dirs if d not in exclude_dirs]
                    total_files += len(files)

                # نسخ الملفات
                for root, dirs, files in os.walk(project_path):
                    dirs[:] = [d for d in dirs if d not in exclude_dirs]

                    for file in files:
                        src_file = os.path.join(root, file)
                        rel_path = os.path.relpath(src_file, project_path)
                        dst_file = os.path.join(full_backup_path, rel_path)

                        # إنشاء المجلد إذا لم يكن موجوداً
                        os.makedirs(os.path.dirname(dst_file), exist_ok=True)

                        # نسخ الملف
                        shutil.copy2(src_file, dst_file)
                        copied_files += 1

                        # تحديث التقدم
                        progress = 30 + (copied_files / total_files) * 60
                        self.update_progress(progress, f"نسخ الملفات... ({copied_files}/{total_files})")

                # إنشاء ملف معلومات النسخة الاحتياطية
                self.update_progress(95, "إنشاء ملف المعلومات...")

                info_content = f"""
نسخة احتياطية من مشروع نظام إدارة التدريب
=======================================

تاريخ الإنشاء: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
المسار الأصلي: {project_path}
عدد الملفات: {copied_files}
الإصدار: {self.project_config['version']}

ملاحظات:
- تم استثناء مجلدات البناء والملفات المؤقتة
- يمكن استعادة هذه النسخة في أي وقت
- تأكد من وجود Python وجميع المتطلبات عند الاستعادة
"""

                with open(os.path.join(full_backup_path, "معلومات_النسخة_الاحتياطية.txt"), 'w', encoding='utf-8') as f:
                    f.write(info_content.strip())

                self.update_progress(100, "مكتمل")
                self.log_message(f"✅ تم إنشاء النسخة الاحتياطية: {backup_name}", "SUCCESS")

                messagebox.showinfo(
                    "مكتمل",
                    f"تم إنشاء النسخة الاحتياطية بنجاح!\n\nالمكان: {full_backup_path}\nالملفات: {copied_files}"
                )

            except Exception as e:
                self.log_message(f"❌ خطأ في إنشاء النسخة الاحتياطية: {e}", "ERROR")
                messagebox.showerror("خطأ", f"حدث خطأ أثناء إنشاء النسخة الاحتياطية:\n{e}")
            finally:
                self.is_running = False
                self.update_progress(0, "جاهز للبدء")

        thread = threading.Thread(target=backup_process)
        thread.daemon = True
        thread.start()

    def open_project_folder(self):
        """فتح مجلد المشروع"""
        if not self.project_path.get():
            messagebox.showwarning("تحذير", "يرجى تحديد مسار المشروع أولاً")
            return

        project_path = self.project_path.get()
        if os.path.exists(project_path):
            os.startfile(project_path)
            self.log_message(f"📁 تم فتح مجلد المشروع: {project_path}", "INFO")
        else:
            messagebox.showerror("خطأ", "مجلد المشروع غير موجود")

    def clear_log(self):
        """مسح السجل"""
        self.log_text.delete(1.0, tk.END)
        self.log_message("🗑️ تم مسح السجل", "INFO")

    def save_log(self):
        """حفظ السجل"""
        log_content = self.log_text.get(1.0, tk.END)

        if not log_content.strip():
            messagebox.showwarning("تحذير", "السجل فارغ")
            return

        file_path = filedialog.asksaveasfilename(
            title="حفظ السجل",
            defaultextension=".txt",
            filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
        )

        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(log_content)
                self.log_message(f"💾 تم حفظ السجل: {file_path}", "SUCCESS")
                messagebox.showinfo("مكتمل", "تم حفظ السجل بنجاح")
            except Exception as e:
                self.log_message(f"❌ خطأ في حفظ السجل: {e}", "ERROR")
                messagebox.showerror("خطأ", f"فشل في حفظ السجل:\n{e}")

    def run(self):
        """تشغيل التطبيق"""
        # رسالة ترحيب
        self.log_message("🚀 مرحباً بك في أداة إدارة التطوير والتوزيع المتقدمة", "SUCCESS")
        self.log_message("💡 يرجى تحديد مسار المشروع للبدء", "INFO")

        # تشغيل الحلقة الرئيسية
        self.root.mainloop()

# نقطة الدخول الرئيسية
if __name__ == "__main__":
    try:
        app = DeveloperToolkit()
        app.run()
    except Exception as e:
        print(f"خطأ في تشغيل التطبيق: {e}")
        input("اضغط Enter للخروج...")
