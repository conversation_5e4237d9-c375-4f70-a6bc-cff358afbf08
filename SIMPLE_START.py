#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة التدريب - تشغيل مبسط
Training Management System - Simple Start
"""

import os
import sys
import subprocess
import platform
import webbrowser
from threading import Timer

def print_banner():
    """طباعة شعار النظام"""
    print("=" * 60)
    print("🎓 نظام إدارة التدريب المتطور")
    print("   Training Management System")
    print("=" * 60)
    print()

def check_requirements():
    """فحص المتطلبات الأساسية"""
    print("🔧 فحص المتطلبات...")
    
    # فحص Python
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print(f"❌ Python {version.major}.{version.minor} - يحتاج 3.8+")
        return False
    print(f"✅ Python {version.major}.{version.minor}.{version.micro}")
    
    # فحص البيئة الافتراضية
    if not os.path.exists('.venv'):
        print("❌ البيئة الافتراضية غير موجودة")
        return False
    print("✅ البيئة الافتراضية موجودة")
    
    # فحص الملفات الأساسية
    required_files = ['app.py', 'templates', 'static']
    for file in required_files:
        if not os.path.exists(file):
            print(f"❌ {file} غير موجود")
            return False
    print("✅ جميع الملفات الأساسية موجودة")
    
    return True

def setup_system():
    """إعداد النظام"""
    print("\n📦 إعداد النظام...")
    
    # إنشاء البيئة الافتراضية
    if not os.path.exists('.venv'):
        print("📦 إنشاء البيئة الافتراضية...")
        try:
            subprocess.run([sys.executable, '-m', 'venv', '.venv'], check=True)
            print("✅ تم إنشاء البيئة الافتراضية")
        except subprocess.CalledProcessError:
            print("❌ فشل في إنشاء البيئة الافتراضية")
            return False
    
    # تحديد مسار pip
    if platform.system() == "Windows":
        pip_path = os.path.join('.venv', 'Scripts', 'pip.exe')
    else:
        pip_path = os.path.join('.venv', 'bin', 'pip')
    
    # تثبيت المكتبات
    print("📦 تثبيت المكتبات...")
    packages = [
        'Flask', 'Flask-SQLAlchemy', 'Flask-Login', 'Flask-WTF',
        'pandas', 'openpyxl', 'xlsxwriter', 'arabic-reshaper', 
        'python-bidi', 'tqdm', 'email-validator'
    ]
    
    try:
        subprocess.run([pip_path, 'install', '--upgrade', 'pip'], check=True)
        subprocess.run([pip_path, 'install'] + packages, check=True)
        print("✅ تم تثبيت المكتبات")
    except subprocess.CalledProcessError:
        print("❌ فشل في تثبيت المكتبات")
        return False
    
    # إنشاء المجلدات
    print("📁 إنشاء المجلدات...")
    directories = ['static/css', 'static/js', 'static/libs', 'uploads', 'exports', 'backups']
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
    print("✅ تم إنشاء المجلدات")
    
    return True

def start_application():
    """تشغيل التطبيق"""
    print("\n🚀 تشغيل النظام...")
    
    # تحديد مسار Python
    if platform.system() == "Windows":
        python_path = os.path.join('.venv', 'Scripts', 'python.exe')
    else:
        python_path = os.path.join('.venv', 'bin', 'python')
    
    print("🌐 النظام متاح على: http://localhost:5000")
    print("🔑 المستخدم الافتراضي: admin / admin")
    print("⏹️ للإيقاف: اضغط Ctrl+C")
    print()
    
    # فتح المتصفح بعد ثانيتين
    Timer(2.0, lambda: webbrowser.open('http://localhost:5000')).start()
    
    try:
        # تشغيل التطبيق
        subprocess.run([python_path, 'app.py'])
    except KeyboardInterrupt:
        print("\n👋 تم إغلاق النظام بنجاح")
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل النظام: {e}")

def main():
    """الدالة الرئيسية"""
    print_banner()
    
    # فحص المتطلبات
    if not check_requirements():
        print("\n⚠️ المتطلبات غير مكتملة")
        response = input("هل تريد إعداد النظام الآن؟ (y/n): ")
        
        if response.lower() in ['y', 'yes', 'نعم']:
            if setup_system():
                print("\n✅ تم إعداد النظام بنجاح!")
                start_application()
            else:
                print("\n❌ فشل في إعداد النظام")
        else:
            print("\nيرجى إعداد النظام لاحقاً")
            print("يمكنك استخدام: python SIMPLE_START.py")
    else:
        # تشغيل النظام مباشرة
        start_application()

if __name__ == "__main__":
    main()
