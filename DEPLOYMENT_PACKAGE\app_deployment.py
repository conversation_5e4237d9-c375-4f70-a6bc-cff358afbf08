#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تطبيق Flask محسن للتوزيع كملف EXE
Flask Application Optimized for EXE Distribution
"""

import os
import sys
import socket
import webbrowser
import threading
import time
from pathlib import Path

# إضافة المسار الحالي إلى sys.path
if hasattr(sys, '_MEIPASS'):
    # تشغيل من ملف EXE
    base_path = sys._MEIPASS
else:
    # تشغيل من Python مباشرة
    base_path = os.path.dirname(os.path.abspath(__file__))

sys.path.insert(0, base_path)

# تحديد مسارات الملفات
template_dir = os.path.join(base_path, 'templates')
static_dir = os.path.join(base_path, 'static')
database_path = os.path.join(base_path, 'training_system_deployment.db')

# إعداد متغيرات البيئة
os.environ['FLASK_ENV'] = 'production'
os.environ['FLASK_DEBUG'] = '0'

from flask import Flask, render_template, redirect, url_for, flash, request, jsonify
from flask_sqlalchemy import SQLAlchemy
from flask_login import LoginManager, UserMixin, login_user, logout_user, login_required, current_user
from flask_wtf import FlaskForm, CSRFProtect
from wtforms import StringField, PasswordField, SubmitField, BooleanField
from wtforms.validators import DataRequired, Email, Length
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime, timezone
import sqlite3

def find_free_port(start_port=5000, max_attempts=100):
    """البحث عن بورت متاح"""
    for port in range(start_port, start_port + max_attempts):
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.bind(('localhost', port))
                return port
        except OSError:
            continue
    return None

def create_app():
    """إنشاء تطبيق Flask"""
    
    # إنشاء التطبيق مع مسارات محددة
    app = Flask(__name__, 
                template_folder=template_dir,
                static_folder=static_dir)
    
    # تكوين التطبيق
    app.config['SECRET_KEY'] = 'training-system-deployment-key-2024'
    app.config['SQLALCHEMY_DATABASE_URI'] = f'sqlite:///{database_path}'
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    app.config['WTF_CSRF_ENABLED'] = True
    app.config['WTF_CSRF_TIME_LIMIT'] = None
    
    # تهيئة الإضافات
    db = SQLAlchemy(app)
    csrf = CSRFProtect(app)
    login_manager = LoginManager(app)
    login_manager.login_view = 'login'
    login_manager.login_message = 'يرجى تسجيل الدخول للوصول إلى هذه الصفحة'
    login_manager.login_message_category = 'info'
    
    # نموذج المستخدم
    class User(UserMixin, db.Model):
        id = db.Column(db.Integer, primary_key=True)
        username = db.Column(db.String(20), unique=True, nullable=False)
        email = db.Column(db.String(120), unique=True, nullable=False)
        password = db.Column(db.String(60), nullable=False)
        role = db.Column(db.String(20), nullable=False, default='trainee')
        created_at = db.Column(db.DateTime, nullable=False, default=lambda: datetime.now(timezone.utc))
        
        def __repr__(self):
            return f"User('{self.username}', '{self.email}', '{self.role}')"
    
    @login_manager.user_loader
    def load_user(user_id):
        return User.query.get(int(user_id))
    
    # نموذج تسجيل الدخول
    class LoginForm(FlaskForm):
        username = StringField('اسم المستخدم', validators=[DataRequired(), Length(min=2, max=20)])
        password = PasswordField('كلمة المرور', validators=[DataRequired()])
        remember = BooleanField('تذكرني')
        submit = SubmitField('تسجيل الدخول')
    
    # الصفحة الرئيسية
    @app.route('/')
    def index():
        return render_template('welcome_simple.html')
    
    # صفحة تسجيل الدخول
    @app.route('/login', methods=['GET', 'POST'])
    def login():
        if current_user.is_authenticated:
            return redirect(url_for('dashboard'))
        
        form = LoginForm()
        if form.validate_on_submit():
            user = User.query.filter_by(username=form.username.data).first()
            if user and check_password_hash(user.password, form.password.data):
                login_user(user, remember=form.remember.data)
                next_page = request.args.get('next')
                flash(f'مرحباً {user.username}! تم تسجيل الدخول بنجاح', 'success')
                return redirect(next_page) if next_page else redirect(url_for('dashboard'))
            else:
                flash('فشل تسجيل الدخول. تحقق من اسم المستخدم وكلمة المرور', 'danger')
        
        return render_template('login.html', form=form)
    
    # تسجيل الخروج
    @app.route('/logout')
    @login_required
    def logout():
        logout_user()
        flash('تم تسجيل الخروج بنجاح', 'info')
        return redirect(url_for('index'))
    
    # لوحة التحكم
    @app.route('/dashboard')
    @login_required
    def dashboard():
        return render_template('dashboard.html')
    
    # API للتحقق من حالة النظام
    @app.route('/api/system/status')
    def system_status():
        try:
            # التحقق من قاعدة البيانات
            user_count = User.query.count()
            
            return jsonify({
                'status': 'success',
                'database': 'connected',
                'users': user_count,
                'version': '1.0.0',
                'timestamp': datetime.now().isoformat()
            })
        except Exception as e:
            return jsonify({
                'status': 'error',
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }), 500
    
    # معالج الأخطاء
    @app.errorhandler(404)
    def not_found_error(error):
        return render_template('404.html'), 404
    
    @app.errorhandler(500)
    def internal_error(error):
        db.session.rollback()
        return render_template('500.html'), 500
    
    return app, db

def check_database():
    """التحقق من وجود قاعدة البيانات وإنشاؤها إذا لزم الأمر"""

    if not os.path.exists(database_path):
        print(f"⚠️  قاعدة البيانات غير موجودة: {database_path}")
        print("🔄 محاولة إنشاء قاعدة بيانات جديدة...")

        try:
            # استخدام مدير قاعدة البيانات المتقدم إذا كان متوفراً
            try:
                # محاولة استيراد مدير قاعدة البيانات المتقدم
                import importlib.util
                spec = importlib.util.spec_from_file_location("database_manager",
                    os.path.join(base_path, "database_manager.py"))
                if spec and spec.loader:
                    database_manager = importlib.util.module_from_spec(spec)
                    spec.loader.exec_module(database_manager)

                    db_manager = database_manager.DatabaseManager(database_path)
                    success = db_manager.create_empty_database()

                    if success:
                        print("✅ تم إنشاء قاعدة البيانات المتقدمة بنجاح")
                        print("👤 المستخدم الإداري: admin")
                        print("🔑 كلمة المرور: admin123")
                        return True
                    else:
                        raise Exception("فشل في إنشاء قاعدة البيانات المتقدمة")

            except Exception as e:
                print(f"⚠️  تعذر استخدام مدير قاعدة البيانات المتقدم: {e}")
                print("🔄 استخدام النظام البسيط...")

            # النظام البسيط كبديل
            conn = sqlite3.connect(database_path)
            cursor = conn.cursor()

            # إنشاء جدول المستخدمين الأساسي
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS user (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    username VARCHAR(20) UNIQUE NOT NULL,
                    email VARCHAR(120) UNIQUE NOT NULL,
                    password VARCHAR(60) NOT NULL,
                    role VARCHAR(20) NOT NULL DEFAULT 'trainee',
                    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    is_active BOOLEAN DEFAULT 1,
                    last_login DATETIME,
                    phone VARCHAR(20),
                    full_name VARCHAR(100)
                )
            ''')

            # إنشاء جداول أساسية إضافية
            basic_tables = [
                '''CREATE TABLE IF NOT EXISTS course (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    course_number VARCHAR(20) UNIQUE NOT NULL,
                    title VARCHAR(100) NOT NULL,
                    description TEXT NOT NULL,
                    category VARCHAR(50) NOT NULL,
                    level VARCHAR(20) NOT NULL,
                    start_date DATETIME NOT NULL,
                    end_date DATETIME NOT NULL,
                    duration_days INTEGER NOT NULL,
                    trainer_id INTEGER NOT NULL,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    is_active BOOLEAN DEFAULT 1,
                    FOREIGN KEY (trainer_id) REFERENCES user (id)
                )''',

                '''CREATE TABLE IF NOT EXISTS personal_data (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    full_name VARCHAR(100) NOT NULL,
                    national_number VARCHAR(20) UNIQUE NOT NULL,
                    phone_yemen_mobile VARCHAR(20),
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    is_active BOOLEAN DEFAULT 1
                )''',

                '''CREATE TABLE IF NOT EXISTS course_participant (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    course_id INTEGER NOT NULL,
                    personal_data_id INTEGER NOT NULL,
                    enrollment_date DATETIME DEFAULT CURRENT_TIMESTAMP,
                    status VARCHAR(20) DEFAULT "enrolled",
                    FOREIGN KEY (course_id) REFERENCES course (id),
                    FOREIGN KEY (personal_data_id) REFERENCES personal_data (id),
                    UNIQUE(course_id, personal_data_id)
                )'''
            ]

            for table_sql in basic_tables:
                cursor.execute(table_sql)

            # إنشاء مستخدم إداري افتراضي
            admin_password = generate_password_hash('admin123')
            cursor.execute('''
                INSERT OR IGNORE INTO user (username, email, password, role, full_name)
                VALUES (?, ?, ?, ?, ?)
            ''', ('admin', '<EMAIL>', admin_password, 'admin', 'المدير العام'))

            # إنشاء فهارس أساسية
            indexes = [
                "CREATE INDEX IF NOT EXISTS idx_user_username ON user(username)",
                "CREATE INDEX IF NOT EXISTS idx_user_email ON user(email)",
                "CREATE INDEX IF NOT EXISTS idx_course_number ON course(course_number)",
                "CREATE INDEX IF NOT EXISTS idx_personal_data_national ON personal_data(national_number)"
            ]

            for index_sql in indexes:
                cursor.execute(index_sql)

            conn.commit()
            conn.close()

            print("✅ تم إنشاء قاعدة البيانات الأساسية بنجاح")
            print("👤 المستخدم الإداري: admin")
            print("🔑 كلمة المرور: admin123")

        except Exception as e:
            print(f"❌ فشل في إنشاء قاعدة البيانات: {e}")
            return False

    return True

def open_browser(url, delay=2):
    """فتح المتصفح بعد تأخير"""
    time.sleep(delay)
    try:
        webbrowser.open(url)
    except Exception as e:
        print(f"تعذر فتح المتصفح: {e}")

def main():
    """الدالة الرئيسية"""
    
    print("=" * 60)
    print("    🎓 نظام إدارة التدريب - Training Management System")
    print("    📦 إصدار التوزيع - Distribution Version 1.0.0")
    print("=" * 60)
    print()
    
    # التحقق من قاعدة البيانات
    if not check_database():
        print("❌ فشل في التحقق من قاعدة البيانات")
        input("اضغط Enter للخروج...")
        return
    
    # البحث عن بورت متاح
    port = find_free_port()
    if not port:
        print("❌ لم يتم العثور على بورت متاح")
        input("اضغط Enter للخروج...")
        return
    
    print(f"🌐 البورت المستخدم: {port}")
    print(f"🔗 رابط النظام: http://localhost:{port}")
    print()
    
    # إنشاء التطبيق
    try:
        app, db = create_app()
        
        # إنشاء الجداول
        with app.app_context():
            db.create_all()
        
        print("✅ تم تهيئة النظام بنجاح")
        print("🚀 جاري تشغيل الخادم...")
        print()
        print("📋 معلومات تسجيل الدخول:")
        print("   👤 اسم المستخدم: admin")
        print("   🔑 كلمة المرور: admin123")
        print("   ⚠️  يرجى تغيير كلمة المرور بعد أول تسجيل دخول")
        print()
        print("🌐 سيتم فتح النظام في المتصفح تلقائياً...")
        print("❌ لإيقاف النظام: اضغط Ctrl+C")
        print("=" * 60)
        
        # فتح المتصفح في خيط منفصل
        url = f"http://localhost:{port}"
        browser_thread = threading.Thread(target=open_browser, args=(url,))
        browser_thread.daemon = True
        browser_thread.start()
        
        # تشغيل التطبيق
        app.run(host='127.0.0.1', port=port, debug=False, use_reloader=False)
        
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف النظام بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل النظام: {e}")
        input("اضغط Enter للخروج...")

if __name__ == '__main__':
    main()
