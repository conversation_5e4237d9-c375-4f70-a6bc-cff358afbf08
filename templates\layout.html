<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>{{ title }} - نظام التدريب والتأهيل</title>
    <!-- Bootstrap RTL CSS - محلي -->
    <link rel="stylesheet" href="{{ url_for('static', filename='libs/bootstrap/bootstrap.rtl.min.css') }}">
    <!-- Font Awesome - محلي -->
    <link rel="stylesheet" href="{{ url_for('static', filename='libs/fontawesome/all.min.css') }}">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/main.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/dashboard-style.css') }}">
    {% block styles %}{% endblock %}
</head>
<body>
    <header>
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container">
                <a class="navbar-brand" href="{{ url_for('home') }}">
                    <i class="fas fa-graduation-cap me-2"></i>نظام التدريب والتأهيل
                </a>

                <!-- التاريخ الهجري والساعة المحسن -->
                <div class="navbar-datetime d-none d-lg-block me-4">
                    <div class="datetime-container">
                        <div class="date-section">
                            <i class="fas fa-calendar-alt"></i>
                            <span id="hijri-date" class="date-text"></span>
                        </div>
                        <div class="time-section">
                            <i class="fas fa-clock"></i>
                            <span id="current-time" class="time-text"></span>
                        </div>
                    </div>
                </div>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav me-auto mb-2 mb-lg-0">
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('home') }}">الرئيسية</a>
                        </li>
                        {% if current_user.is_authenticated %}
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('dashboard') }}">لوحة التحكم</a>
                        </li>
                        {% endif %}
                    </ul>
                    <ul class="navbar-nav">
                        {% if current_user.is_authenticated %}
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user me-1"></i>{{ current_user.username }}
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end">
                                <li><a class="dropdown-item" href="#">الملف الشخصي</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="{{ url_for('logout') }}">تسجيل الخروج</a></li>
                            </ul>
                        </li>
                        {% else %}
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('login') }}">تسجيل الدخول</a>
                        </li>
                        {% endif %}
                    </ul>
                </div>
            </div>
        </nav>
    </header>

    <main class="container mt-4">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ category }} alert-dismissible fade show" style="border-radius: 15px; backdrop-filter: blur(10px); box-shadow: 0 5px 15px rgba(0,0,0,0.1);">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        {% block content %}{% endblock %}
    </main>

    <footer class="bg-dark text-white text-center py-3 mt-5">
        <div class="container">
            <p class="mb-0">جميع الحقوق محفوظة &copy; {{ now.year }} نظام التدريب والتأهيل</p>
        </div>
    </footer>

    <!-- jQuery - محلي -->
    <script src="{{ url_for('static', filename='libs/jquery/jquery-3.6.0.min.js') }}"></script>
    <!-- Bootstrap JS Bundle with Popper - محلي -->
    <script src="{{ url_for('static', filename='libs/bootstrap/bootstrap.bundle.min.js') }}"></script>
    <!-- Custom JS -->
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>

    <!-- التاريخ الهجري والساعة -->
    <script>
        // عرض التاريخ الهجري الصحيح
        function getHijriDate() {
            // التاريخ الهجري الحالي (يجب تحديثه يدوياً أو استخدام API)
            return '17 ذو الحجة 1446هـ';
        }

        // عرض الوقت الحالي
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleTimeString('ar-SA', {
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit',
                hour12: true
            });

            document.getElementById('current-time').textContent = timeString;
            document.getElementById('hijri-date').textContent = getHijriDate();
        }

        // تحديث الوقت كل ثانية
        setInterval(updateTime, 1000);

        // تحديث فوري عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', updateTime);

        // تأثير التمرير للشريط العلوي
        window.addEventListener('scroll', function() {
            const navbar = document.querySelector('.navbar');
            if (window.scrollY > 50) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }
        });
    </script>

    {% block scripts %}{% endblock %}
</body>
</html>
