#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مدير قاعدة البيانات المتقدم للتوزيع
Advanced Database Manager for Deployment
"""

import os
import sys
import sqlite3
import shutil
import json
from datetime import datetime, timezone
from werkzeug.security import generate_password_hash

class DatabaseManager:
    """مدير قاعدة البيانات المتقدم"""

    def __init__(self, db_path="training_system_deployment.db"):
        self.db_path = db_path
        self.backup_path = f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db"

    def create_backup(self):
        """إنشاء نسخة احتياطية من قاعدة البيانات الحالية"""
        if os.path.exists(self.db_path):
            shutil.copy2(self.db_path, self.backup_path)
            print(f"✅ تم إنشاء نسخة احتياطية: {self.backup_path}")
            return True
        return False

    def get_database_schema(self):
        """الحصول على مخطط قاعدة البيانات الكامل"""
        schema = {
            'tables': {},
            'indexes': [],
            'triggers': []
        }

        # تعريف الجداول والحقول المطلوبة
        schema['tables'] = {
            'user': {
                'columns': [
                    ('id', 'INTEGER PRIMARY KEY AUTOINCREMENT'),
                    ('username', 'VARCHAR(20) UNIQUE NOT NULL'),
                    ('email', 'VARCHAR(120) UNIQUE NOT NULL'),
                    ('password', 'VARCHAR(60) NOT NULL'),
                    ('role', 'VARCHAR(20) NOT NULL DEFAULT "trainee"'),
                    ('created_at', 'DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP'),
                    ('is_active', 'BOOLEAN DEFAULT 1'),
                    ('last_login', 'DATETIME'),
                    ('phone', 'VARCHAR(20)'),
                    ('full_name', 'VARCHAR(100)')
                ],
                'description': 'جدول المستخدمين'
            },

            'course_path': {
                'columns': [
                    ('id', 'INTEGER PRIMARY KEY AUTOINCREMENT'),
                    ('name', 'VARCHAR(100) NOT NULL UNIQUE'),
                    ('description', 'TEXT'),
                    ('code', 'VARCHAR(20)'),
                    ('is_active', 'BOOLEAN DEFAULT 1'),
                    ('created_at', 'DATETIME DEFAULT CURRENT_TIMESTAMP')
                ],
                'description': 'مسارات الدورات'
            },

            'course_path_level': {
                'columns': [
                    ('id', 'INTEGER PRIMARY KEY AUTOINCREMENT'),
                    ('name', 'VARCHAR(100) NOT NULL'),
                    ('description', 'TEXT'),
                    ('code', 'VARCHAR(20)'),
                    ('order_num', 'INTEGER'),
                    ('path_id', 'INTEGER NOT NULL'),
                    ('is_active', 'BOOLEAN DEFAULT 1'),
                    ('created_at', 'DATETIME DEFAULT CURRENT_TIMESTAMP')
                ],
                'description': 'مستويات مسارات الدورات',
                'foreign_keys': [
                    ('path_id', 'course_path', 'id')
                ]
            },

            'agency': {
                'columns': [
                    ('id', 'INTEGER PRIMARY KEY AUTOINCREMENT'),
                    ('name', 'VARCHAR(100) NOT NULL'),
                    ('code', 'VARCHAR(20)'),
                    ('description', 'TEXT'),
                    ('contact_person', 'VARCHAR(100)'),
                    ('phone', 'VARCHAR(20)'),
                    ('email', 'VARCHAR(120)'),
                    ('address', 'TEXT'),
                    ('is_active', 'BOOLEAN DEFAULT 1'),
                    ('created_at', 'DATETIME DEFAULT CURRENT_TIMESTAMP')
                ],
                'description': 'الجهات والمؤسسات'
            },

            'training_center': {
                'columns': [
                    ('id', 'INTEGER PRIMARY KEY AUTOINCREMENT'),
                    ('name', 'VARCHAR(100) NOT NULL'),
                    ('code', 'VARCHAR(20)'),
                    ('location', 'VARCHAR(200)'),
                    ('capacity', 'INTEGER'),
                    ('facilities', 'TEXT'),
                    ('contact_person', 'VARCHAR(100)'),
                    ('phone', 'VARCHAR(20)'),
                    ('is_active', 'BOOLEAN DEFAULT 1'),
                    ('created_at', 'DATETIME DEFAULT CURRENT_TIMESTAMP')
                ],
                'description': 'المراكز التدريبية'
            },

            'participant_type': {
                'columns': [
                    ('id', 'INTEGER PRIMARY KEY AUTOINCREMENT'),
                    ('name', 'VARCHAR(100) NOT NULL'),
                    ('description', 'TEXT'),
                    ('requirements', 'TEXT'),
                    ('is_active', 'BOOLEAN DEFAULT 1'),
                    ('created_at', 'DATETIME DEFAULT CURRENT_TIMESTAMP')
                ],
                'description': 'أنواع المشاركين'
            },

            'course_level': {
                'columns': [
                    ('id', 'INTEGER PRIMARY KEY AUTOINCREMENT'),
                    ('name', 'VARCHAR(50) NOT NULL'),
                    ('description', 'TEXT'),
                    ('order_num', 'INTEGER'),
                    ('is_active', 'BOOLEAN DEFAULT 1'),
                    ('created_at', 'DATETIME DEFAULT CURRENT_TIMESTAMP')
                ],
                'description': 'مستويات الدورات'
            },

            'force_classification': {
                'columns': [
                    ('id', 'INTEGER PRIMARY KEY AUTOINCREMENT'),
                    ('name', 'VARCHAR(100) NOT NULL UNIQUE'),
                    ('description', 'TEXT'),
                    ('code', 'VARCHAR(20)'),
                    ('is_active', 'BOOLEAN DEFAULT 1'),
                    ('created_at', 'DATETIME DEFAULT CURRENT_TIMESTAMP')
                ],
                'description': 'تصنيفات القوة'
            },

            'course': {
                'columns': [
                    ('id', 'INTEGER PRIMARY KEY AUTOINCREMENT'),
                    ('course_number', 'VARCHAR(20) UNIQUE NOT NULL'),
                    ('agency_course_number', 'VARCHAR(20)'),
                    ('place_course_number', 'VARCHAR(20)'),
                    ('title', 'VARCHAR(100) NOT NULL'),
                    ('description', 'TEXT NOT NULL'),
                    ('category', 'VARCHAR(50) NOT NULL'),
                    ('level', 'VARCHAR(20) NOT NULL'),
                    ('image', 'VARCHAR(100) DEFAULT "default_course.jpg"'),
                    ('start_date', 'DATETIME NOT NULL'),
                    ('end_date', 'DATETIME NOT NULL'),
                    ('start_date_hijri', 'VARCHAR(20) NOT NULL'),
                    ('end_date_hijri', 'VARCHAR(20) NOT NULL'),
                    ('duration_days', 'INTEGER NOT NULL'),
                    ('trainer_id', 'INTEGER NOT NULL'),
                    ('created_at', 'DATETIME DEFAULT CURRENT_TIMESTAMP'),
                    ('path_id', 'INTEGER'),
                    ('path_level_id', 'INTEGER'),
                    ('agency_id', 'INTEGER'),
                    ('center_id', 'INTEGER'),
                    ('place_name', 'VARCHAR(100)'),
                    ('participant_type_id', 'INTEGER'),
                    ('status_id', 'INTEGER'),
                    ('entry_date', 'DATETIME'),
                    ('exit_date', 'DATETIME'),
                    ('entry_date_hijri', 'VARCHAR(20)'),
                    ('exit_date_hijri', 'VARCHAR(20)'),
                    ('force_classification_id', 'INTEGER'),
                    ('target_agency_id', 'INTEGER'),
                    ('target_count', 'INTEGER'),
                    ('card_type', 'VARCHAR(50)'),
                    ('total_participants', 'INTEGER'),
                    ('total_graduates', 'INTEGER'),
                    ('total_dropouts', 'INTEGER'),
                    ('daily_allowance', 'FLOAT'),
                    ('transportation_allowance', 'FLOAT'),
                    ('accommodation_allowance', 'FLOAT'),
                    ('total_allowance', 'FLOAT'),
                    ('notes', 'TEXT'),
                    ('is_active', 'BOOLEAN DEFAULT 1')
                ],
                'description': 'الدورات التدريبية',
                'foreign_keys': [
                    ('trainer_id', 'user', 'id'),
                    ('path_id', 'course_path', 'id'),
                    ('path_level_id', 'course_path_level', 'id'),
                    ('agency_id', 'agency', 'id'),
                    ('center_id', 'training_center', 'id'),
                    ('participant_type_id', 'participant_type', 'id'),
                    ('status_id', 'course_level', 'id'),
                    ('force_classification_id', 'force_classification', 'id'),
                    ('target_agency_id', 'agency', 'id')
                ]
            },

            'personal_data': {
                'columns': [
                    ('id', 'INTEGER PRIMARY KEY AUTOINCREMENT'),
                    ('user_id', 'INTEGER'),
                    ('full_name', 'VARCHAR(100) NOT NULL'),
                    ('national_number', 'VARCHAR(20) UNIQUE NOT NULL'),
                    ('nickname', 'VARCHAR(50)'),
                    ('age', 'INTEGER'),
                    ('birth_date', 'DATE'),
                    ('gender', 'VARCHAR(10)'),
                    ('governorate', 'VARCHAR(50)'),
                    ('directorate', 'VARCHAR(50)'),
                    ('uzla', 'VARCHAR(50)'),
                    ('village', 'VARCHAR(50)'),
                    ('residence_house', 'TEXT'),
                    ('job_title', 'VARCHAR(100)'),
                    ('work_place_text', 'VARCHAR(100)'),
                    ('military_number', 'VARCHAR(20)'),
                    ('phone_yemen_mobile', 'VARCHAR(20)'),
                    ('phone_home', 'VARCHAR(20)'),
                    ('email', 'VARCHAR(120)'),
                    ('qualification', 'VARCHAR(100)'),
                    ('specialization', 'VARCHAR(100)'),
                    ('marital_status', 'VARCHAR(20)'),
                    ('emergency_contact', 'VARCHAR(100)'),
                    ('emergency_phone', 'VARCHAR(20)'),
                    ('notes', 'TEXT'),
                    ('created_at', 'DATETIME DEFAULT CURRENT_TIMESTAMP'),
                    ('updated_at', 'DATETIME DEFAULT CURRENT_TIMESTAMP'),
                    ('is_active', 'BOOLEAN DEFAULT 1')
                ],
                'description': 'البيانات الشخصية',
                'foreign_keys': [
                    ('user_id', 'user', 'id')
                ]
            },

            'course_participant': {
                'columns': [
                    ('id', 'INTEGER PRIMARY KEY AUTOINCREMENT'),
                    ('course_id', 'INTEGER NOT NULL'),
                    ('personal_data_id', 'INTEGER NOT NULL'),
                    ('enrollment_date', 'DATETIME DEFAULT CURRENT_TIMESTAMP'),
                    ('status', 'VARCHAR(20) DEFAULT "enrolled"'),
                    ('final_grade', 'FLOAT'),
                    ('attendance_percentage', 'FLOAT'),
                    ('certificate_number', 'VARCHAR(50)'),
                    ('certificate_date', 'DATE'),
                    ('completion_status', 'VARCHAR(20)'),
                    ('dropout_reason', 'TEXT'),
                    ('notes', 'TEXT'),
                    ('created_at', 'DATETIME DEFAULT CURRENT_TIMESTAMP'),
                    ('updated_at', 'DATETIME DEFAULT CURRENT_TIMESTAMP')
                ],
                'description': 'مشاركو الدورات',
                'foreign_keys': [
                    ('course_id', 'course', 'id'),
                    ('personal_data_id', 'personal_data', 'id')
                ]
            },

            'material': {
                'columns': [
                    ('id', 'INTEGER PRIMARY KEY AUTOINCREMENT'),
                    ('title', 'VARCHAR(100) NOT NULL'),
                    ('description', 'TEXT'),
                    ('file_path', 'VARCHAR(255)'),
                    ('file_type', 'VARCHAR(20)'),
                    ('day_number', 'INTEGER NOT NULL'),
                    ('course_id', 'INTEGER NOT NULL'),
                    ('created_at', 'DATETIME DEFAULT CURRENT_TIMESTAMP'),
                    ('is_active', 'BOOLEAN DEFAULT 1')
                ],
                'description': 'مواد الدورات',
                'foreign_keys': [
                    ('course_id', 'course', 'id')
                ]
            },

            'course_schedule': {
                'columns': [
                    ('id', 'INTEGER PRIMARY KEY AUTOINCREMENT'),
                    ('course_id', 'INTEGER NOT NULL'),
                    ('day_number', 'INTEGER NOT NULL'),
                    ('start_time', 'VARCHAR(5) NOT NULL'),
                    ('end_time', 'VARCHAR(5) NOT NULL'),
                    ('title', 'VARCHAR(100) NOT NULL'),
                    ('description', 'TEXT'),
                    ('is_break', 'BOOLEAN DEFAULT 0'),
                    ('material_id', 'INTEGER'),
                    ('created_at', 'DATETIME DEFAULT CURRENT_TIMESTAMP')
                ],
                'description': 'جدول الدورات',
                'foreign_keys': [
                    ('course_id', 'course', 'id'),
                    ('material_id', 'material', 'id')
                ]
            },

            'attendance': {
                'columns': [
                    ('id', 'INTEGER PRIMARY KEY AUTOINCREMENT'),
                    ('course_participant_id', 'INTEGER NOT NULL'),
                    ('date', 'DATE NOT NULL'),
                    ('status', 'VARCHAR(20) DEFAULT "present"'),
                    ('arrival_time', 'TIME'),
                    ('departure_time', 'TIME'),
                    ('notes', 'TEXT'),
                    ('created_at', 'DATETIME DEFAULT CURRENT_TIMESTAMP')
                ],
                'description': 'سجل الحضور',
                'foreign_keys': [
                    ('course_participant_id', 'course_participant', 'id')
                ]
            },

            'evaluation': {
                'columns': [
                    ('id', 'INTEGER PRIMARY KEY AUTOINCREMENT'),
                    ('course_participant_id', 'INTEGER NOT NULL'),
                    ('evaluation_type', 'VARCHAR(50) NOT NULL'),
                    ('score', 'FLOAT'),
                    ('max_score', 'FLOAT'),
                    ('percentage', 'FLOAT'),
                    ('evaluation_date', 'DATE'),
                    ('evaluator_id', 'INTEGER'),
                    ('notes', 'TEXT'),
                    ('created_at', 'DATETIME DEFAULT CURRENT_TIMESTAMP')
                ],
                'description': 'تقييمات المشاركين',
                'foreign_keys': [
                    ('course_participant_id', 'course_participant', 'id'),
                    ('evaluator_id', 'user', 'id')
                ]
            }
        }

        return schema

    def check_existing_database(self):
        """فحص قاعدة البيانات الموجودة وتحديد ما ينقصها"""
        if not os.path.exists(self.db_path):
            return {
                'exists': False,
                'missing_tables': [],
                'missing_columns': {},
                'status': 'لا توجد قاعدة بيانات'
            }

        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        try:
            # الحصول على الجداول الموجودة
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            existing_tables = [row[0] for row in cursor.fetchall()]

            schema = self.get_database_schema()
            required_tables = list(schema['tables'].keys())

            missing_tables = [table for table in required_tables if table not in existing_tables]
            missing_columns = {}

            # فحص الأعمدة المفقودة في الجداول الموجودة
            for table_name in existing_tables:
                if table_name in schema['tables']:
                    cursor.execute(f"PRAGMA table_info({table_name})")
                    existing_columns = [row[1] for row in cursor.fetchall()]

                    required_columns = [col[0] for col in schema['tables'][table_name]['columns']]
                    missing_cols = [col for col in required_columns if col not in existing_columns]

                    if missing_cols:
                        missing_columns[table_name] = missing_cols

            # حساب عدد السجلات
            record_counts = {}
            for table in existing_tables:
                try:
                    cursor.execute(f"SELECT COUNT(*) FROM {table}")
                    record_counts[table] = cursor.fetchone()[0]
                except:
                    record_counts[table] = 0

            conn.close()

            return {
                'exists': True,
                'existing_tables': existing_tables,
                'missing_tables': missing_tables,
                'missing_columns': missing_columns,
                'record_counts': record_counts,
                'status': 'قاعدة بيانات موجودة'
            }

        except Exception as e:
            conn.close()
            return {
                'exists': True,
                'error': str(e),
                'status': 'خطأ في قراءة قاعدة البيانات'
            }

    def create_empty_database(self):
        """إنشاء قاعدة بيانات فارغة مع الجداول الأساسية فقط"""
        print("🗄️  إنشاء قاعدة بيانات فارغة...")

        # حذف قاعدة البيانات الموجودة إن وجدت
        if os.path.exists(self.db_path):
            os.remove(self.db_path)

        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        try:
            schema = self.get_database_schema()

            # إنشاء الجداول
            for table_name, table_info in schema['tables'].items():
                columns_sql = ', '.join([f"{col[0]} {col[1]}" for col in table_info['columns']])

                create_sql = f"CREATE TABLE {table_name} ({columns_sql})"
                cursor.execute(create_sql)
                print(f"✅ تم إنشاء جدول: {table_name}")

            # إنشاء المستخدم الإداري الافتراضي فقط
            admin_password = generate_password_hash('admin123')
            cursor.execute('''
                INSERT INTO user (username, email, password, role, full_name)
                VALUES (?, ?, ?, ?, ?)
            ''', ('admin', '<EMAIL>', admin_password, 'admin', 'المدير العام'))

            # إنشاء الفهارس الأساسية
            self._create_indexes(cursor)

            conn.commit()
            print("✅ تم إنشاء قاعدة البيانات الفارغة بنجاح")
            return True

        except Exception as e:
            print(f"❌ خطأ في إنشاء قاعدة البيانات: {e}")
            conn.rollback()
            return False
        finally:
            conn.close()

    def create_full_database(self):
        """إنشاء قاعدة بيانات كاملة مع بيانات تجريبية"""
        print("🗄️  إنشاء قاعدة بيانات كاملة مع بيانات تجريبية...")

        # إنشاء قاعدة البيانات الفارغة أولاً
        if not self.create_empty_database():
            return False

        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        try:
            # إضافة البيانات التجريبية
            self._insert_sample_data(cursor)

            conn.commit()
            print("✅ تم إنشاء قاعدة البيانات الكاملة بنجاح")
            return True

        except Exception as e:
            print(f"❌ خطأ في إضافة البيانات التجريبية: {e}")
            conn.rollback()
            return False
        finally:
            conn.close()

    def update_existing_database(self):
        """تحديث قاعدة البيانات الموجودة بإضافة الجداول والحقول المفقودة"""
        print("🔄 تحديث قاعدة البيانات الموجودة...")

        # إنشاء نسخة احتياطية أولاً
        self.create_backup()

        analysis = self.check_existing_database()
        if not analysis['exists']:
            print("❌ لا توجد قاعدة بيانات للتحديث")
            return False

        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        try:
            schema = self.get_database_schema()
            updated = False

            # إضافة الجداول المفقودة
            for table_name in analysis['missing_tables']:
                if table_name in schema['tables']:
                    table_info = schema['tables'][table_name]
                    columns_sql = ', '.join([f"{col[0]} {col[1]}" for col in table_info['columns']])

                    create_sql = f"CREATE TABLE {table_name} ({columns_sql})"
                    cursor.execute(create_sql)
                    print(f"✅ تم إضافة جدول جديد: {table_name}")
                    updated = True

            # إضافة الأعمدة المفقودة
            for table_name, missing_cols in analysis['missing_columns'].items():
                if table_name in schema['tables']:
                    table_schema = schema['tables'][table_name]

                    for col_name in missing_cols:
                        # البحث عن تعريف العمود
                        col_definition = None
                        for col_def in table_schema['columns']:
                            if col_def[0] == col_name:
                                col_definition = col_def[1]
                                break

                        if col_definition:
                            try:
                                alter_sql = f"ALTER TABLE {table_name} ADD COLUMN {col_name} {col_definition}"
                                cursor.execute(alter_sql)
                                print(f"✅ تم إضافة عمود جديد: {table_name}.{col_name}")
                                updated = True
                            except Exception as e:
                                print(f"⚠️  تعذر إضافة العمود {table_name}.{col_name}: {e}")

            # إنشاء الفهارس المفقودة
            self._create_indexes(cursor)

            # التحقق من وجود مستخدم إداري
            cursor.execute("SELECT COUNT(*) FROM user WHERE role = 'admin'")
            admin_count = cursor.fetchone()[0]

            if admin_count == 0:
                admin_password = generate_password_hash('admin123')
                cursor.execute('''
                    INSERT INTO user (username, email, password, role, full_name)
                    VALUES (?, ?, ?, ?, ?)
                ''', ('admin', '<EMAIL>', admin_password, 'admin', 'المدير العام'))
                print("✅ تم إضافة مستخدم إداري افتراضي")
                updated = True

            conn.commit()

            if updated:
                print("✅ تم تحديث قاعدة البيانات بنجاح")
            else:
                print("ℹ️  قاعدة البيانات محدثة بالفعل")

            return True

        except Exception as e:
            print(f"❌ خطأ في تحديث قاعدة البيانات: {e}")
            conn.rollback()
            return False
        finally:
            conn.close()

    def _create_indexes(self, cursor):
        """إنشاء الفهارس الأساسية"""
        indexes = [
            "CREATE INDEX IF NOT EXISTS idx_user_username ON user(username)",
            "CREATE INDEX IF NOT EXISTS idx_user_email ON user(email)",
            "CREATE INDEX IF NOT EXISTS idx_user_role ON user(role)",
            "CREATE INDEX IF NOT EXISTS idx_course_trainer ON course(trainer_id)",
            "CREATE INDEX IF NOT EXISTS idx_course_number ON course(course_number)",
            "CREATE INDEX IF NOT EXISTS idx_personal_data_national ON personal_data(national_number)",
            "CREATE INDEX IF NOT EXISTS idx_course_participant_course ON course_participant(course_id)",
            "CREATE INDEX IF NOT EXISTS idx_course_participant_person ON course_participant(personal_data_id)",
            "CREATE INDEX IF NOT EXISTS idx_agency_name ON agency(name)",
            "CREATE INDEX IF NOT EXISTS idx_training_center_name ON training_center(name)"
        ]

        for index_sql in indexes:
            try:
                cursor.execute(index_sql)
            except Exception as e:
                print(f"⚠️  تعذر إنشاء فهرس: {e}")

    def _insert_sample_data(self, cursor):
        """إدراج بيانات تجريبية"""

        # إضافة مسارات الدورات
        course_paths = [
            ('التدريب الإداري', 'دورات في الإدارة والقيادة', 'ADM'),
            ('التدريب التقني', 'دورات تقنية ومعلوماتية', 'TECH'),
            ('التدريب المهني', 'دورات مهنية متخصصة', 'PROF')
        ]

        for path in course_paths:
            cursor.execute('''
                INSERT INTO course_path (name, description, code)
                VALUES (?, ?, ?)
            ''', path)

        # إضافة مستويات المسارات
        path_levels = [
            ('المستوى الأساسي', 'مستوى للمبتدئين', 'L1', 1, 1),
            ('المستوى المتوسط', 'مستوى متوسط', 'L2', 2, 1),
            ('المستوى المتقدم', 'مستوى متقدم', 'L3', 3, 1),
            ('المستوى الأساسي', 'مستوى للمبتدئين', 'L1', 1, 2),
            ('المستوى المتوسط', 'مستوى متوسط', 'L2', 2, 2),
            ('المستوى المتقدم', 'مستوى متقدم', 'L3', 3, 2)
        ]

        for level in path_levels:
            cursor.execute('''
                INSERT INTO course_path_level (name, description, code, order_num, path_id)
                VALUES (?, ?, ?, ?, ?)
            ''', level)

        # إضافة الجهات
        agencies = [
            ('وزارة التربية والتعليم', 'EDU', 'الوزارة المسؤولة عن التعليم', 'أحمد محمد', '01234567890', '<EMAIL>', 'صنعاء'),
            ('وزارة الصحة', 'HEALTH', 'الوزارة المسؤولة عن الصحة', 'فاطمة علي', '01234567891', '<EMAIL>', 'صنعاء'),
            ('القطاع الخاص', 'PRIVATE', 'شركات القطاع الخاص', 'محمد سالم', '01234567892', '<EMAIL>', 'عدن')
        ]

        for agency in agencies:
            cursor.execute('''
                INSERT INTO agency (name, code, description, contact_person, phone, email, address)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', agency)

        # إضافة المراكز التدريبية
        centers = [
            ('مركز التدريب الرئيسي', 'MAIN', 'صنعاء - شارع الزبيري', 100, 'قاعات مكيفة، معامل حاسوب', 'سعد أحمد', '01234567893'),
            ('مركز التدريب الفرعي', 'SUB', 'عدن - كريتر', 50, 'قاعات تدريبية، مكتبة', 'نادية محمد', '01234567894')
        ]

        for center in centers:
            cursor.execute('''
                INSERT INTO training_center (name, code, location, capacity, facilities, contact_person, phone)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', center)

        # إضافة أنواع المشاركين
        participant_types = [
            ('موظفون حكوميون', 'موظفو الجهات الحكومية', 'شهادة جامعية، خبرة سنتين'),
            ('موظفو القطاع الخاص', 'موظفو الشركات الخاصة', 'شهادة ثانوية، خبرة سنة'),
            ('خريجون جدد', 'خريجو الجامعات والمعاهد', 'شهادة جامعية حديثة')
        ]

        for ptype in participant_types:
            cursor.execute('''
                INSERT INTO participant_type (name, description, requirements)
                VALUES (?, ?, ?)
            ''', ptype)

        # إضافة مستويات الدورات
        course_levels = [
            ('مبتدئ', 'مستوى للمبتدئين', 1),
            ('متوسط', 'مستوى متوسط', 2),
            ('متقدم', 'مستوى متقدم', 3),
            ('خبير', 'مستوى الخبراء', 4)
        ]

        for level in course_levels:
            cursor.execute('''
                INSERT INTO course_level (name, description, order_num)
                VALUES (?, ?, ?)
            ''', level)

        # إضافة تصنيفات القوة
        force_classifications = [
            ('ضباط', 'الضباط العسكريون', 'OFF'),
            ('ضباط صف', 'ضباط الصف', 'NCO'),
            ('جنود', 'الجنود', 'SOL'),
            ('مدنيون', 'الموظفون المدنيون', 'CIV')
        ]

        for classification in force_classifications:
            cursor.execute('''
                INSERT INTO force_classification (name, description, code)
                VALUES (?, ?, ?)
            ''', classification)

        print("✅ تم إدراج البيانات التجريبية")