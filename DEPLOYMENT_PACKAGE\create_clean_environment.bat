@echo off
chcp 65001 >nul
echo ========================================
echo    إنشاء بيئة افتراضية نظيفة
echo    Creating Clean Virtual Environment
echo ========================================
echo.

cd /d "%~dp0\.."

echo 🔧 التحقق من وجود Python...
echo 🔧 Checking Python installation...

python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت أو غير موجود في PATH
    echo ❌ Python is not installed or not in PATH
    echo.
    echo يرجى تثبيت Python 3.8 أو أحدث من:
    echo Please install Python 3.8 or newer from:
    echo https://www.python.org/downloads/
    pause
    exit /b 1
)

python --version
echo.

echo 🗑️ حذف البيئة الافتراضية السابقة إن وجدت...
echo 🗑️ Removing previous virtual environment if exists...

if exist "venv_deployment" (
    rmdir /s /q "venv_deployment"
    echo تم حذف البيئة السابقة
)

if exist ".venv_deployment" (
    rmdir /s /q ".venv_deployment"
    echo تم حذف البيئة السابقة
)

echo.
echo 🆕 إنشاء بيئة افتراضية جديدة...
echo 🆕 Creating new virtual environment...

python -m venv venv_deployment

if not exist "venv_deployment" (
    echo ❌ فشل في إنشاء البيئة الافتراضية
    echo ❌ Failed to create virtual environment
    pause
    exit /b 1
)

echo ✅ تم إنشاء البيئة الافتراضية بنجاح
echo ✅ Virtual environment created successfully
echo.

echo 🔄 تفعيل البيئة الافتراضية...
echo 🔄 Activating virtual environment...

call venv_deployment\Scripts\activate.bat

if errorlevel 1 (
    echo ❌ فشل في تفعيل البيئة الافتراضية
    echo ❌ Failed to activate virtual environment
    pause
    exit /b 1
)

echo ✅ تم تفعيل البيئة الافتراضية
echo ✅ Virtual environment activated
echo.

echo 📦 تحديث pip إلى أحدث إصدار...
echo 📦 Updating pip to latest version...

python -m pip install --upgrade pip

echo.
echo 📦 تثبيت wheel و setuptools...
echo 📦 Installing wheel and setuptools...

pip install wheel setuptools

echo.
echo ✅ تم إعداد البيئة الافتراضية النظيفة بنجاح!
echo ✅ Clean virtual environment setup completed!
echo.

echo 📋 معلومات البيئة:
echo 📋 Environment information:
echo    - مسار البيئة: %CD%\venv_deployment
echo    - Environment path: %CD%\venv_deployment
echo    - إصدار Python: 
python --version
echo    - إصدار pip:
pip --version
echo.

echo 🔄 الخطوة التالية: تشغيل install_requirements.bat
echo 🔄 Next step: Run install_requirements.bat
echo.

echo ⚠️  ملاحظة: تأكد من بقاء البيئة الافتراضية مفعلة
echo ⚠️  Note: Make sure to keep the virtual environment activated
echo.

pause
