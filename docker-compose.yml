version: '3.8'

services:
  training-system:
    build: .
    container_name: training_system_app
    ports:
      - "5000:5000"
    volumes:
      - ./data:/app/data
      - ./uploads:/app/uploads
      - ./backups:/app/backups
    environment:
      - FLASK_ENV=production
      - DATABASE_URL=sqlite:///data/training_system.db
    restart: unless-stopped
    networks:
      - training_network

  # إضافة قاعدة بيانات PostgreSQL للإنتاج (اختيارية)
  postgres:
    image: postgres:15
    container_name: training_system_db
    environment:
      POSTGRES_DB: training_system
      POSTGRES_USER: training_user
      POSTGRES_PASSWORD: training_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    restart: unless-stopped
    networks:
      - training_network
    profiles:
      - postgres

networks:
  training_network:
    driver: bridge

volumes:
  postgres_data:
