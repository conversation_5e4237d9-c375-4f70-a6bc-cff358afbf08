#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تحضير قاعدة البيانات للتوزيع
Database Preparation for Distribution
"""

import os
import sys
import sqlite3
import shutil
from datetime import datetime, timezone
from werkzeug.security import generate_password_hash

def create_deployment_database():
    """إنشاء قاعدة بيانات محسنة للتوزيع"""
    
    print("🗄️  إنشاء قاعدة بيانات التوزيع...")
    print("🗄️  Creating deployment database...")
    
    # مسار قاعدة البيانات الأصلية
    original_db = "training_system.db"
    
    # مسار قاعدة البيانات الجديدة
    deployment_db = "training_system_deployment.db"
    
    # حذف قاعدة البيانات القديمة إن وجدت
    if os.path.exists(deployment_db):
        os.remove(deployment_db)
        print(f"تم حذف قاعدة البيانات السابقة: {deployment_db}")
    
    # نسخ قاعدة البيانات الأصلية إذا كانت موجودة
    if os.path.exists(original_db):
        shutil.copy2(original_db, deployment_db)
        print(f"تم نسخ قاعدة البيانات من: {original_db}")
    else:
        print("قاعدة البيانات الأصلية غير موجودة، سيتم إنشاء قاعدة جديدة")
    
    # الاتصال بقاعدة البيانات
    conn = sqlite3.connect(deployment_db)
    cursor = conn.cursor()
    
    try:
        # إنشاء الجداول الأساسية إذا لم تكن موجودة
        create_tables(cursor)
        
        # إنشاء المستخدم الإداري الافتراضي
        create_default_admin(cursor)
        
        # إنشاء البيانات الأساسية
        create_basic_data(cursor)
        
        # تحسين قاعدة البيانات
        optimize_database(cursor)
        
        # حفظ التغييرات
        conn.commit()
        
        print("✅ تم إنشاء قاعدة البيانات بنجاح!")
        print("✅ Database created successfully!")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء قاعدة البيانات: {e}")
        print(f"❌ Error creating database: {e}")
        conn.rollback()
        return False
        
    finally:
        conn.close()

def create_tables(cursor):
    """إنشاء الجداول الأساسية"""
    
    print("📋 إنشاء الجداول الأساسية...")
    
    # جدول المستخدمين
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS user (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username VARCHAR(20) UNIQUE NOT NULL,
            email VARCHAR(120) UNIQUE NOT NULL,
            password VARCHAR(60) NOT NULL,
            role VARCHAR(20) NOT NULL DEFAULT 'trainee',
            created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # جدول الدورات
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS course (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            course_number VARCHAR(20) UNIQUE NOT NULL,
            agency_course_number VARCHAR(20),
            place_course_number VARCHAR(20),
            title VARCHAR(100) NOT NULL,
            description TEXT NOT NULL,
            category VARCHAR(50) NOT NULL,
            level VARCHAR(20) NOT NULL,
            image VARCHAR(100) DEFAULT 'default_course.jpg',
            start_date DATETIME NOT NULL,
            end_date DATETIME NOT NULL,
            start_date_hijri VARCHAR(20) NOT NULL,
            end_date_hijri VARCHAR(20) NOT NULL,
            duration_days INTEGER NOT NULL,
            trainer_id INTEGER NOT NULL,
            created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
            total_participants INTEGER,
            total_graduates INTEGER,
            total_dropouts INTEGER,
            notes TEXT,
            FOREIGN KEY (trainer_id) REFERENCES user (id)
        )
    ''')
    
    # جدول البيانات الشخصية
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS personal_data (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER,
            full_name VARCHAR(100) NOT NULL,
            national_number VARCHAR(20) UNIQUE NOT NULL,
            nickname VARCHAR(50),
            age INTEGER,
            governorate VARCHAR(50),
            directorate VARCHAR(50),
            uzla VARCHAR(50),
            village VARCHAR(50),
            residence_house TEXT,
            job_title VARCHAR(100),
            work_place_text VARCHAR(100),
            military_number VARCHAR(20),
            phone_yemen_mobile VARCHAR(20),
            notes TEXT,
            created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES user (id)
        )
    ''')
    
    # جدول مشاركي الدورات
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS course_participant (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            course_id INTEGER NOT NULL,
            personal_data_id INTEGER NOT NULL,
            enrollment_date DATETIME DEFAULT CURRENT_TIMESTAMP,
            status VARCHAR(20) DEFAULT 'enrolled',
            final_grade FLOAT,
            attendance_percentage FLOAT,
            notes TEXT,
            FOREIGN KEY (course_id) REFERENCES course (id),
            FOREIGN KEY (personal_data_id) REFERENCES personal_data (id),
            UNIQUE(course_id, personal_data_id)
        )
    ''')
    
    print("✅ تم إنشاء الجداول الأساسية")

def create_default_admin(cursor):
    """إنشاء المستخدم الإداري الافتراضي"""
    
    print("👤 إنشاء المستخدم الإداري الافتراضي...")
    
    # التحقق من وجود مستخدم إداري
    cursor.execute("SELECT COUNT(*) FROM user WHERE role = 'admin'")
    admin_count = cursor.fetchone()[0]
    
    if admin_count == 0:
        # إنشاء مستخدم إداري افتراضي
        admin_password = generate_password_hash('admin123')
        
        cursor.execute('''
            INSERT INTO user (username, email, password, role, created_at)
            VALUES (?, ?, ?, ?, ?)
        ''', (
            'admin',
            '<EMAIL>',
            admin_password,
            'admin',
            datetime.now(timezone.utc)
        ))
        
        print("✅ تم إنشاء المستخدم الإداري:")
        print("   اسم المستخدم: admin")
        print("   كلمة المرور: admin123")
        print("   ⚠️  يرجى تغيير كلمة المرور بعد أول تسجيل دخول!")
    else:
        print("✅ المستخدم الإداري موجود بالفعل")

def create_basic_data(cursor):
    """إنشاء البيانات الأساسية"""
    
    print("📊 إنشاء البيانات الأساسية...")
    
    # إنشاء دورة تجريبية إذا لم تكن موجودة
    cursor.execute("SELECT COUNT(*) FROM course")
    course_count = cursor.fetchone()[0]
    
    if course_count == 0:
        cursor.execute('''
            INSERT INTO course (
                course_number, title, description, category, level,
                start_date, end_date, start_date_hijri, end_date_hijri,
                duration_days, trainer_id
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            'DEMO-001',
            'دورة تجريبية - نظام إدارة التدريب',
            'هذه دورة تجريبية لاختبار النظام. يمكن حذفها بعد التأكد من عمل النظام بشكل صحيح.',
            'تجريبي',
            'مبتدئ',
            datetime.now(),
            datetime.now(),
            '1446/06/15',
            '1446/06/20',
            5,
            1  # معرف المستخدم الإداري
        ))
        
        print("✅ تم إنشاء دورة تجريبية")
    
    # إنشاء بيانات شخصية تجريبية
    cursor.execute("SELECT COUNT(*) FROM personal_data")
    personal_data_count = cursor.fetchone()[0]
    
    if personal_data_count == 0:
        cursor.execute('''
            INSERT INTO personal_data (
                full_name, national_number, governorate, directorate,
                job_title, notes
            ) VALUES (?, ?, ?, ?, ?, ?)
        ''', (
            'مستخدم تجريبي',
            '123456789',
            'صنعاء',
            'الثورة',
            'موظف',
            'بيانات تجريبية - يمكن حذفها'
        ))
        
        print("✅ تم إنشاء بيانات شخصية تجريبية")

def optimize_database(cursor):
    """تحسين قاعدة البيانات"""
    
    print("⚡ تحسين قاعدة البيانات...")
    
    # إنشاء فهارس لتحسين الأداء
    indexes = [
        "CREATE INDEX IF NOT EXISTS idx_user_username ON user(username)",
        "CREATE INDEX IF NOT EXISTS idx_user_email ON user(email)",
        "CREATE INDEX IF NOT EXISTS idx_course_number ON course(course_number)",
        "CREATE INDEX IF NOT EXISTS idx_course_trainer ON course(trainer_id)",
        "CREATE INDEX IF NOT EXISTS idx_personal_data_national ON personal_data(national_number)",
        "CREATE INDEX IF NOT EXISTS idx_course_participant_course ON course_participant(course_id)",
        "CREATE INDEX IF NOT EXISTS idx_course_participant_person ON course_participant(personal_data_id)"
    ]
    
    for index_sql in indexes:
        cursor.execute(index_sql)
    
    # تحسين قاعدة البيانات
    cursor.execute("VACUUM")
    cursor.execute("ANALYZE")
    
    print("✅ تم تحسين قاعدة البيانات")

def main():
    """الدالة الرئيسية"""
    
    print("=" * 50)
    print("    تحضير قاعدة البيانات للتوزيع")
    print("    Database Preparation for Distribution")
    print("=" * 50)
    print()
    
    try:
        success = create_deployment_database()
        
        if success:
            print()
            print("🎉 تم تحضير قاعدة البيانات بنجاح!")
            print("🎉 Database preparation completed successfully!")
            print()
            print("📋 معلومات مهمة:")
            print("📋 Important information:")
            print("   • ملف قاعدة البيانات: training_system_deployment.db")
            print("   • المستخدم الإداري: admin")
            print("   • كلمة المرور: admin123")
            print("   • يرجى تغيير كلمة المرور بعد أول تسجيل دخول")
            print()
            return True
        else:
            print()
            print("❌ فشل في تحضير قاعدة البيانات")
            print("❌ Failed to prepare database")
            return False
            
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        print(f"❌ General error: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
