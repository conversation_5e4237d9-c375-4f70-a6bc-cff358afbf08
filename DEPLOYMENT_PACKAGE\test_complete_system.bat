@echo off
chcp 65001 >nul
echo ========================================
echo    اختبار شامل للنظام
echo    Complete System Testing
echo ========================================
echo.

cd /d "%~dp0\.."

echo 📁 التحقق من وجود مجلد التوزيع...
echo 📁 Checking distribution folder...

if not exist "dist\TrainingSystem" (
    echo ❌ مجلد التوزيع غير موجود
    echo ❌ Distribution folder not found
    echo يرجى تشغيل build_optimized_exe.bat أولاً
    echo Please run build_optimized_exe.bat first
    pause
    exit /b 1
)

if not exist "dist\TrainingSystem\TrainingSystem.exe" (
    echo ❌ الملف التنفيذي غير موجود
    echo ❌ Executable file not found
    pause
    exit /b 1
)

echo ✅ تم العثور على الملف التنفيذي
echo ✅ Executable file found
echo.

cd "dist\TrainingSystem"

echo 🧪 بدء الاختبارات الشاملة...
echo 🧪 Starting comprehensive tests...
echo.

REM اختبار 1: التحقق من الملفات الأساسية
echo 📋 اختبار 1: التحقق من الملفات الأساسية
echo 📋 Test 1: Checking essential files

set test1_passed=true

if not exist "TrainingSystem.exe" (
    echo ❌ الملف التنفيذي مفقود
    set test1_passed=false
) else (
    echo ✅ الملف التنفيذي موجود
)

if not exist "_internal" (
    echo ❌ مجلد _internal مفقود
    set test1_passed=false
) else (
    echo ✅ مجلد _internal موجود
)

if exist "templates" (
    echo ✅ مجلد templates موجود
) else (
    echo ⚠️  مجلد templates مفقود
)

if exist "static" (
    echo ✅ مجلد static موجود
) else (
    echo ⚠️  مجلد static مفقود
)

if "%test1_passed%"=="true" (
    echo ✅ اختبار الملفات الأساسية: نجح
    echo ✅ Essential files test: PASSED
) else (
    echo ❌ اختبار الملفات الأساسية: فشل
    echo ❌ Essential files test: FAILED
)

echo.

REM اختبار 2: اختبار تشغيل سريع
echo 📋 اختبار 2: اختبار التشغيل السريع
echo 📋 Test 2: Quick startup test

echo 🚀 تشغيل النظام لمدة 10 ثوانٍ...
echo 🚀 Running system for 10 seconds...

start /b TrainingSystem.exe
timeout /t 10 /nobreak >nul

REM التحقق من تشغيل النظام
tasklist /fi "imagename eq TrainingSystem.exe" 2>nul | find /i "TrainingSystem.exe" >nul
if errorlevel 1 (
    echo ❌ النظام لم يبدأ بشكل صحيح
    echo ❌ System did not start properly
    set test2_passed=false
) else (
    echo ✅ النظام يعمل بشكل صحيح
    echo ✅ System is running properly
    set test2_passed=true
    
    REM إيقاف النظام
    taskkill /f /im TrainingSystem.exe >nul 2>&1
    echo 🛑 تم إيقاف النظام
    echo 🛑 System stopped
)

if "%test2_passed%"=="true" (
    echo ✅ اختبار التشغيل السريع: نجح
    echo ✅ Quick startup test: PASSED
) else (
    echo ❌ اختبار التشغيل السريع: فشل
    echo ❌ Quick startup test: FAILED
)

echo.

REM اختبار 3: اختبار قاعدة البيانات
echo 📋 اختبار 3: اختبار قاعدة البيانات
echo 📋 Test 3: Database test

set test3_passed=true

if exist "training_system_deployment.db" (
    echo ✅ ملف قاعدة البيانات موجود
    echo ✅ Database file exists
    
    REM التحقق من حجم قاعدة البيانات
    for %%f in ("training_system_deployment.db") do set db_size=%%~zf
    if !db_size! gtr 0 (
        echo ✅ قاعدة البيانات تحتوي على بيانات
        echo ✅ Database contains data
    ) else (
        echo ⚠️  قاعدة البيانات فارغة
        echo ⚠️  Database is empty
    )
) else (
    echo ❌ ملف قاعدة البيانات مفقود
    echo ❌ Database file missing
    set test3_passed=false
)

if "%test3_passed%"=="true" (
    echo ✅ اختبار قاعدة البيانات: نجح
    echo ✅ Database test: PASSED
) else (
    echo ❌ اختبار قاعدة البيانات: فشل
    echo ❌ Database test: FAILED
)

echo.

REM اختبار 4: اختبار الملفات الثابتة
echo 📋 اختبار 4: اختبار الملفات الثابتة
echo 📋 Test 4: Static files test

set test4_passed=true

if exist "static\css" (
    echo ✅ مجلد CSS موجود
    dir /b "static\css\*.css" >nul 2>&1
    if not errorlevel 1 (
        echo ✅ ملفات CSS موجودة
    ) else (
        echo ⚠️  ملفات CSS مفقودة
    )
) else (
    echo ⚠️  مجلد CSS مفقود
)

if exist "static\js" (
    echo ✅ مجلد JS موجود
) else (
    echo ⚠️  مجلد JS مفقود
)

if exist "static\img" (
    echo ✅ مجلد الصور موجود
) else (
    echo ⚠️  مجلد الصور مفقود
)

echo ✅ اختبار الملفات الثابتة: نجح
echo ✅ Static files test: PASSED

echo.

REM اختبار 5: اختبار ملفات التشغيل السريع
echo 📋 اختبار 5: اختبار ملفات التشغيل السريع
echo 📋 Test 5: Quick start files test

set test5_passed=true

if exist "START_SYSTEM.bat" (
    echo ✅ ملف التشغيل السريع موجود
) else (
    echo ❌ ملف التشغيل السريع مفقود
    set test5_passed=false
)

if exist "SYSTEM_INFO.txt" (
    echo ✅ ملف معلومات النظام موجود
) else (
    echo ❌ ملف معلومات النظام مفقود
    set test5_passed=false
)

if exist "BACKUP_DATA.bat" (
    echo ✅ ملف النسخ الاحتياطي موجود
) else (
    echo ❌ ملف النسخ الاحتياطي مفقود
    set test5_passed=false
)

if "%test5_passed%"=="true" (
    echo ✅ اختبار ملفات التشغيل السريع: نجح
    echo ✅ Quick start files test: PASSED
) else (
    echo ❌ اختبار ملفات التشغيل السريع: فشل
    echo ❌ Quick start files test: FAILED
)

echo.

REM تلخيص النتائج
echo ========================================
echo    📊 تلخيص نتائج الاختبار
echo    📊 Test Results Summary
echo ========================================
echo.

set total_tests=5
set passed_tests=0

if "%test1_passed%"=="true" set /a passed_tests+=1
if "%test2_passed%"=="true" set /a passed_tests+=1
if "%test3_passed%"=="true" set /a passed_tests+=1
if "%test4_passed%"=="true" set /a passed_tests+=1
if "%test5_passed%"=="true" set /a passed_tests+=1

echo 📈 النتائج: %passed_tests%/%total_tests% اختبارات نجحت
echo 📈 Results: %passed_tests%/%total_tests% tests passed

if %passed_tests% equ %total_tests% (
    echo.
    echo 🎉 جميع الاختبارات نجحت!
    echo 🎉 All tests passed!
    echo.
    echo ✅ النظام جاهز للتوزيع
    echo ✅ System ready for distribution
    echo.
    echo 🔄 الخطوة التالية: تشغيل create_client_package.bat
    echo 🔄 Next step: Run create_client_package.bat
) else (
    echo.
    echo ⚠️  بعض الاختبارات فشلت
    echo ⚠️  Some tests failed
    echo.
    echo 🔧 يرجى مراجعة الأخطاء وإعادة البناء
    echo 🔧 Please review errors and rebuild
)

echo.
echo 📋 معلومات إضافية:
echo 📋 Additional information:

for %%f in ("TrainingSystem.exe") do (
    echo    📏 حجم الملف التنفيذي: %%~zf بايت
    echo    📏 Executable size: %%~zf bytes
    echo    📅 تاريخ الإنشاء: %%~tf
    echo    📅 Creation date: %%~tf
)

echo.

cd ..\..
pause
