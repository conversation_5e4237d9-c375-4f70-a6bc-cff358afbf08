@echo off
chcp 65001 >nul
echo ========================================
echo    🗄️  إدارة قاعدة البيانات المتقدمة
echo    Advanced Database Management
echo ========================================
echo.

cd /d "%~dp0\.."

echo 🔧 التحقق من تفعيل البيئة الافتراضية...
echo 🔧 Checking virtual environment activation...

if not exist "venv_deployment\Scripts\activate.bat" (
    echo ❌ البيئة الافتراضية غير موجودة
    echo ❌ Virtual environment not found
    echo يرجى تشغيل create_clean_environment.bat أولاً
    echo Please run create_clean_environment.bat first
    pause
    exit /b 1
)

call venv_deployment\Scripts\activate.bat

echo ✅ تم تفعيل البيئة الافتراضية
echo ✅ Virtual environment activated
echo.

echo 📋 نسخ سكريبت إدارة قاعدة البيانات...
echo 📋 Copying database management script...

copy "DEPLOYMENT_PACKAGE\database_manager.py" "database_manager_temp.py" >nul

if not exist "database_manager_temp.py" (
    echo ❌ فشل في نسخ سكريبت قاعدة البيانات
    echo ❌ Failed to copy database script
    pause
    exit /b 1
)

:menu
echo ========================================
echo    🗄️  خيارات قاعدة البيانات
echo    Database Options
echo ========================================
echo.

REM فحص قاعدة البيانات الحالية
python -c "
from database_manager_temp import DatabaseManager
db_manager = DatabaseManager()
analysis = db_manager.check_existing_database()

if analysis['exists']:
    print('📊 حالة قاعدة البيانات الحالية:')
    print('📊 Current database status:')
    print(f'   ✅ قاعدة البيانات موجودة')
    print(f'   ✅ Database exists')
    
    if 'existing_tables' in analysis:
        print(f'   📋 عدد الجداول: {len(analysis[\"existing_tables\"])}')
        print(f'   📋 Tables count: {len(analysis[\"existing_tables\"])}')
    
    if 'record_counts' in analysis:
        total_records = sum(analysis['record_counts'].values())
        print(f'   📊 إجمالي السجلات: {total_records}')
        print(f'   📊 Total records: {total_records}')
    
    if analysis['missing_tables']:
        print(f'   ⚠️  جداول مفقودة: {len(analysis[\"missing_tables\"])}')
        print(f'   ⚠️  Missing tables: {len(analysis[\"missing_tables\"])}')
    
    if analysis['missing_columns']:
        missing_cols_count = sum(len(cols) for cols in analysis['missing_columns'].values())
        print(f'   ⚠️  أعمدة مفقودة: {missing_cols_count}')
        print(f'   ⚠️  Missing columns: {missing_cols_count}')
else:
    print('❌ لا توجد قاعدة بيانات')
    print('❌ No database found')
"

echo.
echo 📋 اختر نوع قاعدة البيانات المطلوبة:
echo 📋 Choose the required database type:
echo.
echo    1️⃣  قاعدة بيانات فارغة (جداول فقط)
echo        Empty Database (Tables only)
echo        • جداول فارغة مع مستخدم إداري واحد
echo        • Empty tables with one admin user
echo.
echo    2️⃣  قاعدة بيانات كاملة (مع بيانات تجريبية)
echo        Full Database (With sample data)
echo        • جداول مع بيانات تجريبية شاملة
echo        • Tables with comprehensive sample data
echo.
echo    3️⃣  تحديث قاعدة البيانات الموجودة
echo        Update Existing Database
echo        • إضافة الجداول والحقول المفقودة
echo        • Add missing tables and columns
echo.
echo    4️⃣  فحص قاعدة البيانات التفصيلي
echo        Detailed Database Analysis
echo        • تحليل شامل لحالة قاعدة البيانات
echo        • Comprehensive database status analysis
echo.
echo    5️⃣  نسخ احتياطي من قاعدة البيانات
echo        Backup Current Database
echo        • إنشاء نسخة احتياطية آمنة
echo        • Create safe backup copy
echo.
echo    6️⃣  استعادة من نسخة احتياطية
echo        Restore from Backup
echo        • استعادة قاعدة البيانات من نسخة سابقة
echo        • Restore database from previous backup
echo.
echo    0️⃣  العودة للقائمة الرئيسية
echo        Back to Main Menu
echo.
echo ════════════════════════════════════════════════════════════════
echo.

set /p choice="👆 أدخل رقم اختيارك (Enter your choice): "

if "%choice%"=="1" goto empty_database
if "%choice%"=="2" goto full_database
if "%choice%"=="3" goto update_database
if "%choice%"=="4" goto analyze_database
if "%choice%"=="5" goto backup_database
if "%choice%"=="6" goto restore_database
if "%choice%"=="0" goto cleanup_exit
goto invalid_choice

:empty_database
echo.
echo 🗄️  إنشاء قاعدة بيانات فارغة...
echo 🗄️  Creating empty database...
echo.
echo ⚠️  تحذير: سيتم حذف قاعدة البيانات الحالية إن وجدت
echo ⚠️  Warning: Current database will be deleted if exists
echo.
set /p confirm="هل تريد المتابعة؟ (y/n): "
if /i "%confirm%"=="y" (
    python -c "
from database_manager_temp import DatabaseManager
db_manager = DatabaseManager()
success = db_manager.create_empty_database()
if success:
    print('✅ تم إنشاء قاعدة البيانات الفارغة بنجاح!')
    print('✅ Empty database created successfully!')
    print()
    print('👤 معلومات تسجيل الدخول:')
    print('👤 Login information:')
    print('   اسم المستخدم: admin')
    print('   Username: admin')
    print('   كلمة المرور: admin123')
    print('   Password: admin123')
else:
    print('❌ فشل في إنشاء قاعدة البيانات')
    print('❌ Failed to create database')
"
) else (
    echo تم الإلغاء
    echo Cancelled
)
echo.
pause
goto menu

:full_database
echo.
echo 🗄️  إنشاء قاعدة بيانات كاملة مع بيانات تجريبية...
echo 🗄️  Creating full database with sample data...
echo.
echo ⚠️  تحذير: سيتم حذف قاعدة البيانات الحالية إن وجدت
echo ⚠️  Warning: Current database will be deleted if exists
echo.
echo 📊 ستتضمن قاعدة البيانات:
echo 📊 Database will include:
echo    • مستخدم إداري افتراضي
echo    • مسارات ومستويات دورات تجريبية
echo    • جهات ومراكز تدريبية
echo    • أنواع مشاركين وتصنيفات
echo    • بيانات أساسية شاملة
echo.
set /p confirm="هل تريد المتابعة؟ (y/n): "
if /i "%confirm%"=="y" (
    python -c "
from database_manager_temp import DatabaseManager
db_manager = DatabaseManager()
success = db_manager.create_full_database()
if success:
    print('✅ تم إنشاء قاعدة البيانات الكاملة بنجاح!')
    print('✅ Full database created successfully!')
    print()
    print('👤 معلومات تسجيل الدخول:')
    print('👤 Login information:')
    print('   اسم المستخدم: admin')
    print('   Username: admin')
    print('   كلمة المرور: admin123')
    print('   Password: admin123')
    print()
    print('📊 البيانات التجريبية المضافة:')
    print('📊 Sample data added:')
    print('   • 3 مسارات دورات')
    print('   • 6 مستويات مسارات')
    print('   • 3 جهات')
    print('   • 2 مراكز تدريبية')
    print('   • 3 أنواع مشاركين')
    print('   • 4 مستويات دورات')
    print('   • 4 تصنيفات قوة')
else:
    print('❌ فشل في إنشاء قاعدة البيانات')
    print('❌ Failed to create database')
"
) else (
    echo تم الإلغاء
    echo Cancelled
)
echo.
pause
goto menu

:update_database
echo.
echo 🔄 تحديث قاعدة البيانات الموجودة...
echo 🔄 Updating existing database...
echo.
python -c "
from database_manager_temp import DatabaseManager
db_manager = DatabaseManager()
analysis = db_manager.check_existing_database()

if not analysis['exists']:
    print('❌ لا توجد قاعدة بيانات للتحديث')
    print('❌ No database found to update')
    print('💡 يرجى إنشاء قاعدة بيانات جديدة أولاً')
    print('💡 Please create a new database first')
else:
    print('📊 تحليل قاعدة البيانات الحالية:')
    print('📊 Current database analysis:')
    
    if analysis['missing_tables']:
        print(f'   📋 جداول مفقودة: {analysis[\"missing_tables\"]}')
        print(f'   📋 Missing tables: {analysis[\"missing_tables\"]}')
    
    if analysis['missing_columns']:
        print('   📋 أعمدة مفقودة:')
        print('   📋 Missing columns:')
        for table, columns in analysis['missing_columns'].items():
            print(f'      {table}: {columns}')
    
    if not analysis['missing_tables'] and not analysis['missing_columns']:
        print('   ✅ قاعدة البيانات محدثة بالفعل')
        print('   ✅ Database is already up to date')
    else:
        print()
        confirm = input('هل تريد تحديث قاعدة البيانات؟ (y/n): ')
        if confirm.lower() == 'y':
            success = db_manager.update_existing_database()
            if success:
                print('✅ تم تحديث قاعدة البيانات بنجاح!')
                print('✅ Database updated successfully!')
            else:
                print('❌ فشل في تحديث قاعدة البيانات')
                print('❌ Failed to update database')
        else:
            print('تم الإلغاء')
            print('Cancelled')
"
echo.
pause
goto menu

:analyze_database
echo.
echo 🔍 فحص قاعدة البيانات التفصيلي...
echo 🔍 Detailed database analysis...
echo.
python -c "
from database_manager_temp import DatabaseManager
import os

db_manager = DatabaseManager()
analysis = db_manager.check_existing_database()

print('=' * 60)
print('    📊 تقرير تحليل قاعدة البيانات')
print('    📊 Database Analysis Report')
print('=' * 60)
print()

if analysis['exists']:
    print('✅ قاعدة البيانات موجودة')
    print('✅ Database exists')
    
    # حجم الملف
    if os.path.exists(db_manager.db_path):
        size = os.path.getsize(db_manager.db_path)
        size_mb = size / (1024 * 1024)
        print(f'📏 حجم الملف: {size:,} بايت ({size_mb:.2f} MB)')
        print(f'📏 File size: {size:,} bytes ({size_mb:.2f} MB)')
    
    print()
    
    if 'existing_tables' in analysis:
        print(f'📋 الجداول الموجودة ({len(analysis[\"existing_tables\"])}):')
        print(f'📋 Existing tables ({len(analysis[\"existing_tables\"])}):')
        for table in sorted(analysis['existing_tables']):
            count = analysis.get('record_counts', {}).get(table, 0)
            print(f'   • {table}: {count:,} سجل')
        print()
    
    if analysis['missing_tables']:
        print(f'❌ جداول مفقودة ({len(analysis[\"missing_tables\"])}):')
        print(f'❌ Missing tables ({len(analysis[\"missing_tables\"])}):')
        for table in analysis['missing_tables']:
            print(f'   • {table}')
        print()
    
    if analysis['missing_columns']:
        print('❌ أعمدة مفقودة:')
        print('❌ Missing columns:')
        for table, columns in analysis['missing_columns'].items():
            print(f'   📋 {table}:')
            for col in columns:
                print(f'      • {col}')
        print()
    
    if 'record_counts' in analysis:
        total_records = sum(analysis['record_counts'].values())
        print(f'📊 إجمالي السجلات: {total_records:,}')
        print(f'📊 Total records: {total_records:,}')
        print()
    
    # تقييم حالة قاعدة البيانات
    if not analysis['missing_tables'] and not analysis['missing_columns']:
        print('🎉 قاعدة البيانات في حالة ممتازة!')
        print('🎉 Database is in excellent condition!')
    elif analysis['missing_tables'] or analysis['missing_columns']:
        print('⚠️  قاعدة البيانات تحتاج إلى تحديث')
        print('⚠️  Database needs updating')
        print('💡 استخدم خيار \"تحديث قاعدة البيانات الموجودة\"')
        print('💡 Use \"Update Existing Database\" option')
    
else:
    print('❌ لا توجد قاعدة بيانات')
    print('❌ No database found')
    print('💡 يرجى إنشاء قاعدة بيانات جديدة')
    print('💡 Please create a new database')

print()
print('=' * 60)
"
echo.
pause
goto menu

:backup_database
echo.
echo 💾 إنشاء نسخة احتياطية...
echo 💾 Creating backup...
echo.
python -c "
from database_manager_temp import DatabaseManager
import os

db_manager = DatabaseManager()

if not os.path.exists(db_manager.db_path):
    print('❌ لا توجد قاعدة بيانات للنسخ الاحتياطي')
    print('❌ No database found to backup')
else:
    success = db_manager.create_backup()
    if success:
        print('✅ تم إنشاء النسخة الاحتياطية بنجاح!')
        print('✅ Backup created successfully!')
        print(f'📁 مسار النسخة: {db_manager.backup_path}')
        print(f'📁 Backup path: {db_manager.backup_path}')
    else:
        print('❌ فشل في إنشاء النسخة الاحتياطية')
        print('❌ Failed to create backup')
"
echo.
pause
goto menu

:restore_database
echo.
echo 🔄 استعادة من نسخة احتياطية...
echo 🔄 Restore from backup...
echo.
echo 📁 البحث عن النسخ الاحتياطية المتاحة...
echo 📁 Searching for available backups...
echo.

dir backup_*.db >nul 2>&1
if errorlevel 1 (
    echo ❌ لا توجد نسخ احتياطية متاحة
    echo ❌ No backup files available
    echo 💡 يرجى إنشاء نسخة احتياطية أولاً
    echo 💡 Please create a backup first
) else (
    echo 📋 النسخ الاحتياطية المتاحة:
    echo 📋 Available backups:
    echo.
    dir backup_*.db /b
    echo.
    set /p backup_file="أدخل اسم ملف النسخة الاحتياطية: "
    
    if exist "!backup_file!" (
        echo.
        echo ⚠️  تحذير: سيتم استبدال قاعدة البيانات الحالية
        echo ⚠️  Warning: Current database will be replaced
        echo.
        set /p confirm="هل تريد المتابعة؟ (y/n): "
        if /i "!confirm!"=="y" (
            copy "!backup_file!" "training_system_deployment.db" >nul
            if not errorlevel 1 (
                echo ✅ تم استعادة قاعدة البيانات بنجاح!
                echo ✅ Database restored successfully!
            ) else (
                echo ❌ فشل في استعادة قاعدة البيانات
                echo ❌ Failed to restore database
            )
        ) else (
            echo تم الإلغاء
            echo Cancelled
        )
    ) else (
        echo ❌ الملف غير موجود
        echo ❌ File not found
    )
)
echo.
pause
goto menu

:invalid_choice
echo.
echo ❌ اختيار غير صحيح، يرجى المحاولة مرة أخرى
echo ❌ Invalid choice, please try again
echo.
pause
goto menu

:cleanup_exit
echo.
echo 🔄 تنظيف الملفات المؤقتة...
echo 🔄 Cleaning temporary files...

del "database_manager_temp.py" 2>nul

echo ✅ تم الانتهاء من إدارة قاعدة البيانات
echo ✅ Database management completed
echo.
exit /b 0
