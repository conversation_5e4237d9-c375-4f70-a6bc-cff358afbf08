# دليل ميزات قاعدة البيانات المتقدمة
# Advanced Database Features Guide

## 🎯 نظرة عامة / Overview

تم تطوير نظام إدارة قاعدة البيانات المتقدم لحل جميع المشاكل المتعلقة بقواعد البيانات في عملية التحويل إلى EXE، مع توفير خيارات مرنة ونظام ذكي للتحديث.

---

## 🆕 الميزات الجديدة / New Features

### 1. 🗄️ خيارات متعددة لقاعدة البيانات

#### أ) قاعدة بيانات فارغة (Empty Database)
- **الوصف:** جداول فارغة مع مستخدم إداري واحد فقط
- **الاستخدام:** مثالي للتوزيع النظيف للعملاء
- **المحتويات:**
  - جميع الجداول المطلوبة (فارغة)
  - مستخدم إداري واحد (admin/admin123)
  - فهارس محسنة للأداء
  - بنية قاعدة بيانات كاملة

#### ب) قاعدة بيانات كاملة (Full Database)
- **الوصف:** قاعدة بيانات مع بيانات تجريبية شاملة
- **الاستخدام:** للاختبار والعرض التوضيحي
- **المحتويات:**
  - جميع الجداول مع بيانات تجريبية
  - 3 مسارات دورات تدريبية
  - 6 مستويات مسارات
  - 3 جهات ومؤسسات
  - 2 مراكز تدريبية
  - 3 أنواع مشاركين
  - 4 مستويات دورات
  - 4 تصنيفات قوة

#### ج) تحديث قاعدة البيانات الموجودة (Update Existing)
- **الوصف:** إضافة الجداول والحقول المفقودة للقاعدة الموجودة
- **الاستخدام:** تحديث قواعد البيانات القديمة
- **الميزات:**
  - فحص ذكي للجداول المفقودة
  - إضافة الأعمدة المفقودة تلقائياً
  - الحفاظ على البيانات الموجودة
  - نسخ احتياطي تلقائي قبل التحديث

---

## 🔧 كيفية الاستخدام / How to Use

### الطريقة الأولى: من خلال الواجهة التفاعلية
```bash
# تشغيل الواجهة الرئيسية
START_DEPLOYMENT.bat

# اختيار "إدارة قاعدة البيانات المتقدمة"
# Choose "Advanced Database Management"
```

### الطريقة الثانية: مباشرة
```bash
# تشغيل إدارة قاعدة البيانات مباشرة
database_options.bat
```

### الطريقة الثالثة: أثناء البناء
```bash
# أثناء تشغيل prepare_database.bat
# سيتم عرض خيارات قاعدة البيانات تلقائياً
```

---

## 📊 الجداول المدعومة / Supported Tables

### الجداول الأساسية:
1. **user** - المستخدمون
2. **course** - الدورات التدريبية
3. **personal_data** - البيانات الشخصية
4. **course_participant** - مشاركو الدورات

### الجداول المرجعية:
5. **course_path** - مسارات الدورات
6. **course_path_level** - مستويات المسارات
7. **agency** - الجهات والمؤسسات
8. **training_center** - المراكز التدريبية
9. **participant_type** - أنواع المشاركين
10. **course_level** - مستويات الدورات
11. **force_classification** - تصنيفات القوة

### الجداول المساعدة:
12. **material** - مواد الدورات
13. **course_schedule** - جدول الدورات
14. **attendance** - سجل الحضور
15. **evaluation** - تقييمات المشاركين

---

## 🔍 نظام الفحص الذكي / Smart Analysis System

### فحص قاعدة البيانات الموجودة:
- **الجداول الموجودة:** عرض جميع الجداول الحالية
- **الجداول المفقودة:** تحديد الجداول غير الموجودة
- **الأعمدة المفقودة:** فحص الحقول المفقودة في كل جدول
- **عدد السجلات:** إحصائيات مفصلة لكل جدول
- **حجم قاعدة البيانات:** معلومات الحجم والأداء

### تقرير التحليل التفصيلي:
```
📊 تقرير تحليل قاعدة البيانات
========================================
✅ قاعدة البيانات موجودة
📏 حجم الملف: 2,048,576 بايت (2.00 MB)

📋 الجداول الموجودة (8):
   • user: 1 سجل
   • course: 0 سجل
   • personal_data: 0 سجل
   • course_participant: 0 سجل

❌ جداول مفقودة (7):
   • course_path
   • agency
   • training_center
   • ...

❌ أعمدة مفقودة:
   📋 user:
      • is_active
      • last_login
      • phone
      • full_name

📊 إجمالي السجلات: 1
```

---

## 💾 نظام النسخ الاحتياطي / Backup System

### النسخ الاحتياطي التلقائي:
- يتم إنشاء نسخة احتياطية تلقائياً قبل أي تحديث
- تسمية الملفات بالتاريخ والوقت: `backup_YYYYMMDD_HHMMSS.db`
- حفظ آمن للبيانات الحالية

### النسخ الاحتياطي اليدوي:
```bash
# إنشاء نسخة احتياطية يدوية
database_options.bat
# اختيار "نسخ احتياطي من قاعدة البيانات"
```

### استعادة النسخ الاحتياطية:
```bash
# استعادة من نسخة احتياطية
database_options.bat
# اختيار "استعادة من نسخة احتياطية"
```

---

## 🔄 نظام التحديث الذكي / Smart Update System

### الميزات:
1. **فحص تلقائي:** تحديد الجداول والحقول المفقودة
2. **تحديث آمن:** نسخ احتياطي قبل التحديث
3. **إضافة تدريجية:** إضافة الجداول والحقول بدون فقدان البيانات
4. **فهارس محسنة:** إنشاء فهارس للأداء الأمثل
5. **تحقق من المستخدم الإداري:** إضافة مستخدم إداري إذا لم يكن موجوداً

### مثال على التحديث:
```
🔄 تحديث قاعدة البيانات الموجودة...
💾 تم إنشاء نسخة احتياطية: backup_20241215_143022.db

📊 تحليل قاعدة البيانات الحالية:
   📋 جداول مفقودة: ['agency', 'training_center']
   📋 أعمدة مفقودة:
      user: ['is_active', 'phone']

✅ تم إضافة جدول جديد: agency
✅ تم إضافة جدول جديد: training_center
✅ تم إضافة عمود جديد: user.is_active
✅ تم إضافة عمود جديد: user.phone
✅ تم إضافة مستخدم إداري افتراضي

✅ تم تحديث قاعدة البيانات بنجاح
```

---

## 🎯 حالات الاستخدام / Use Cases

### 1. للمطورين:
- **قاعدة بيانات كاملة:** للاختبار والتطوير
- **تحديث قاعدة البيانات:** عند إضافة ميزات جديدة

### 2. للتوزيع:
- **قاعدة بيانات فارغة:** للعملاء الجدد
- **تحديث قاعدة البيانات:** للعملاء الحاليين

### 3. للصيانة:
- **فحص تفصيلي:** لتشخيص المشاكل
- **نسخ احتياطي:** للحماية من فقدان البيانات

---

## 🔧 الإعدادات المتقدمة / Advanced Settings

### تخصيص قاعدة البيانات:
```python
# يمكن تعديل database_manager.py لإضافة:
# - جداول جديدة
# - حقول إضافية
# - بيانات تجريبية مخصصة
# - فهارس محسنة
```

### إعدادات الأداء:
- **VACUUM:** تحسين قاعدة البيانات
- **ANALYZE:** تحديث إحصائيات الاستعلام
- **فهارس محسنة:** لتسريع البحث

---

## 🚨 نصائح مهمة / Important Tips

### 1. النسخ الاحتياطية:
- احتفظ بنسخ احتياطية منتظمة
- تحقق من النسخ الاحتياطية قبل التحديث
- احفظ النسخ في مكان آمن

### 2. التحديث:
- اختبر التحديث على نسخة تجريبية أولاً
- تأكد من وجود مساحة كافية على القرص
- لا تقاطع عملية التحديث

### 3. الأداء:
- استخدم قاعدة البيانات الفارغة للتوزيع
- نظف قاعدة البيانات دورياً
- راقب حجم قاعدة البيانات

---

## 🔍 استكشاف الأخطاء / Troubleshooting

### مشاكل شائعة:

#### 1. فشل في إنشاء قاعدة البيانات:
```
❌ خطأ: فشل في إنشاء قاعدة البيانات
🔧 الحل:
   - تحقق من أذونات المجلد
   - تأكد من وجود مساحة كافية
   - أغلق أي برامج تستخدم قاعدة البيانات
```

#### 2. فشل في التحديث:
```
❌ خطأ: فشل في تحديث قاعدة البيانات
🔧 الحل:
   - استعد من النسخة الاحتياطية
   - تحقق من سلامة قاعدة البيانات
   - أعد تشغيل العملية
```

#### 3. قاعدة البيانات تالفة:
```
❌ خطأ: قاعدة البيانات تالفة
🔧 الحل:
   - استعد من نسخة احتياطية
   - أنشئ قاعدة بيانات جديدة
   - استورد البيانات يدوياً
```

---

## 📞 الدعم الفني / Technical Support

### للحصول على المساعدة:
- **البريد الإلكتروني:** <EMAIL>
- **الموقع:** www.trainingsystem.com
- **الوثائق:** راجع TROUBLESHOOTING_GUIDE.md

### عند طلب المساعدة، قدم:
1. نوع المشكلة
2. رسائل الخطأ
3. نوع قاعدة البيانات المستخدمة
4. حجم قاعدة البيانات
5. خطوات إعادة إنتاج المشكلة

---

*تم إنشاء هذا الدليل بواسطة فريق تطوير نظام التدريب*
*© 2024 Training System. جميع الحقوق محفوظة*
