@echo off
chcp 65001 >nul
echo ========================================
echo    بناء ملف EXE محسن للتوزيع
echo    Building Optimized EXE for Distribution
echo ========================================
echo.

cd /d "%~dp0\.."

echo 🔧 التحقق من تفعيل البيئة الافتراضية...
echo 🔧 Checking virtual environment activation...

if not exist "venv_deployment\Scripts\activate.bat" (
    echo ❌ البيئة الافتراضية غير موجودة
    echo ❌ Virtual environment not found
    echo يرجى تشغيل create_clean_environment.bat أولاً
    echo Please run create_clean_environment.bat first
    pause
    exit /b 1
)

call venv_deployment\Scripts\activate.bat

echo ✅ تم تفعيل البيئة الافتراضية
echo ✅ Virtual environment activated
echo.

echo 📋 نسخ ملفات التوزيع...
echo 📋 Copying deployment files...

copy "DEPLOYMENT_PACKAGE\app_deployment.py" "app_deployment.py" >nul
copy "DEPLOYMENT_PACKAGE\build_exe_optimized.spec" "build_exe_optimized.spec" >nul

if not exist "app_deployment.py" (
    echo ❌ فشل في نسخ ملف التطبيق
    echo ❌ Failed to copy application file
    pause
    exit /b 1
)

if not exist "build_exe_optimized.spec" (
    echo ❌ فشل في نسخ ملف التكوين
    echo ❌ Failed to copy configuration file
    pause
    exit /b 1
)

echo ✅ تم نسخ ملفات التوزيع
echo ✅ Deployment files copied successfully
echo.

echo 🗄️  التحقق من قاعدة البيانات...
echo 🗄️  Checking database...

if not exist "training_system_deployment.db" (
    echo ⚠️  قاعدة البيانات غير موجودة، سيتم إنشاؤها تلقائياً
    echo ⚠️  Database not found, will be created automatically
)

echo.
echo 🧹 تنظيف ملفات البناء السابقة...
echo 🧹 Cleaning previous build files...

if exist "build" (
    rmdir /s /q "build"
    echo تم حذف مجلد build
)

if exist "dist" (
    rmdir /s /q "dist"
    echo تم حذف مجلد dist
)

echo.
echo 📦 التحقق من PyInstaller...
echo 📦 Checking PyInstaller...

pyinstaller --version >nul 2>&1
if errorlevel 1 (
    echo ❌ PyInstaller غير مثبت
    echo ❌ PyInstaller not installed
    echo 🔄 تثبيت PyInstaller...
    echo 🔄 Installing PyInstaller...
    pip install pyinstaller==5.13.2
)

echo ✅ PyInstaller جاهز
echo ✅ PyInstaller ready
echo.

echo 🔨 بناء ملف EXE...
echo 🔨 Building EXE file...
echo ⏳ هذه العملية قد تستغرق عدة دقائق...
echo ⏳ This process may take several minutes...
echo.

pyinstaller build_exe_optimized.spec --clean --noconfirm

if errorlevel 1 (
    echo ❌ فشل في بناء ملف EXE
    echo ❌ Failed to build EXE file
    echo.
    echo 🔍 تحقق من الأخطاء أعلاه
    echo 🔍 Check errors above
    pause
    exit /b 1
)

echo.
echo ✅ تم بناء ملف EXE بنجاح!
echo ✅ EXE file built successfully!
echo.

echo 📁 التحقق من الملفات المنشأة...
echo 📁 Checking created files...

if exist "dist\TrainingSystem" (
    echo ✓ مجلد التوزيع: dist\TrainingSystem
    echo ✓ Distribution folder: dist\TrainingSystem
    
    if exist "dist\TrainingSystem\TrainingSystem.exe" (
        echo ✓ الملف التنفيذي: TrainingSystem.exe
        echo ✓ Executable file: TrainingSystem.exe
    ) else (
        echo ❌ الملف التنفيذي غير موجود
        echo ❌ Executable file not found
    )
) else (
    echo ❌ مجلد التوزيع غير موجود
    echo ❌ Distribution folder not found
    pause
    exit /b 1
)

echo.
echo 📊 معلومات الملف المنشأ:
echo 📊 Created file information:

for %%f in ("dist\TrainingSystem\TrainingSystem.exe") do (
    echo    📏 الحجم: %%~zf بايت
    echo    📏 Size: %%~zf bytes
    echo    📅 التاريخ: %%~tf
    echo    📅 Date: %%~tf
)

echo.
echo 🔄 تنظيف الملفات المؤقتة...
echo 🔄 Cleaning temporary files...

del "app_deployment.py" 2>nul
del "build_exe_optimized.spec" 2>nul

echo.
echo 🎉 تم إنشاء ملف EXE بنجاح!
echo 🎉 EXE file created successfully!
echo.

echo 📋 الخطوات التالية:
echo 📋 Next steps:
echo    1. تشغيل optimize_files.bat لتحسين الملفات
echo    1. Run optimize_files.bat to optimize files
echo    2. تشغيل test_complete_system.bat للاختبار
echo    2. Run test_complete_system.bat for testing
echo    3. تشغيل create_client_package.bat لإنشاء حزمة العميل
echo    3. Run create_client_package.bat to create client package
echo.

echo 🚀 لتشغيل النظام الآن:
echo 🚀 To run the system now:
echo    cd dist\TrainingSystem
echo    TrainingSystem.exe
echo.

pause
