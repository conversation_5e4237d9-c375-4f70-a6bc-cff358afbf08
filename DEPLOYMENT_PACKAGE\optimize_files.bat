@echo off
chcp 65001 >nul
echo ========================================
echo    تحسين وضغط ملفات التوزيع
echo    Optimizing and Compressing Distribution Files
echo ========================================
echo.

cd /d "%~dp0\.."

echo 📁 التحقق من وجود مجلد التوزيع...
echo 📁 Checking distribution folder...

if not exist "dist\TrainingSystem" (
    echo ❌ مجلد التوزيع غير موجود
    echo ❌ Distribution folder not found
    echo يرجى تشغيل build_optimized_exe.bat أولاً
    echo Please run build_optimized_exe.bat first
    pause
    exit /b 1
)

cd "dist\TrainingSystem"

echo ✅ تم العثور على مجلد التوزيع
echo ✅ Distribution folder found
echo.

echo 📊 حجم الملفات قبل التحسين:
echo 📊 File sizes before optimization:

for /f "tokens=3" %%a in ('dir /-c ^| find "File(s)"') do set size_before=%%a
echo    📏 الحجم الإجمالي: %size_before% بايت
echo    📏 Total size: %size_before% bytes
echo.

echo 🗑️  حذف الملفات غير المطلوبة...
echo 🗑️  Removing unnecessary files...

REM حذف ملفات التطوير
del "*.pdb" 2>nul
del "*.lib" 2>nul
del "*.exp" 2>nul

REM حذف ملفات التوثيق
for /d %%d in (*doc*) do rmdir /s /q "%%d" 2>nul
for /d %%d in (*help*) do rmdir /s /q "%%d" 2>nul

REM حذف ملفات الاختبار
for /d %%d in (*test*) do rmdir /s /q "%%d" 2>nul
for /d %%d in (*example*) do rmdir /s /q "%%d" 2>nul

REM حذف ملفات اللغات غير المطلوبة (الاحتفاظ بالعربية والإنجليزية فقط)
if exist "_internal\tcl\msgs" (
    cd "_internal\tcl\msgs"
    for /d %%d in (*) do (
        if not "%%d"=="ar" if not "%%d"=="en" if not "%%d"=="en_us" (
            rmdir /s /q "%%d" 2>nul
        )
    )
    cd ..\..\..
)

echo ✅ تم حذف الملفات غير المطلوبة
echo ✅ Unnecessary files removed
echo.

echo 🔧 تحسين ملفات CSS و JS...
echo 🔧 Optimizing CSS and JS files...

if exist "static\css" (
    cd "static\css"
    
    REM ضغط ملفات CSS (إزالة المسافات الزائدة والتعليقات)
    for %%f in (*.css) do (
        echo تحسين %%f...
        REM يمكن إضافة أدوات ضغط CSS هنا إذا كانت متوفرة
    )
    
    cd ..\..
)

if exist "static\js" (
    cd "static\js"
    
    REM ضغط ملفات JavaScript
    for %%f in (*.js) do (
        echo تحسين %%f...
        REM يمكن إضافة أدوات ضغط JS هنا إذا كانت متوفرة
    )
    
    cd ..\..
)

echo ✅ تم تحسين ملفات CSS و JS
echo ✅ CSS and JS files optimized
echo.

echo 🗄️  تحسين قاعدة البيانات...
echo 🗄️  Optimizing database...

if exist "training_system_deployment.db" (
    echo تشغيل VACUUM على قاعدة البيانات...
    sqlite3 training_system_deployment.db "VACUUM; ANALYZE;" 2>nul
    echo ✅ تم تحسين قاعدة البيانات
    echo ✅ Database optimized
) else (
    echo ⚠️  قاعدة البيانات غير موجودة
    echo ⚠️  Database not found
)

echo.
echo 📦 إنشاء ملفات التشغيل السريع...
echo 📦 Creating quick start files...

REM إنشاء ملف تشغيل سريع
(
echo @echo off
echo chcp 65001 ^>nul
echo echo ========================================
echo echo    🎓 نظام إدارة التدريب
echo echo    Training Management System
echo echo ========================================
echo echo.
echo echo 🚀 جاري تشغيل النظام...
echo echo 🚀 Starting system...
echo echo.
echo TrainingSystem.exe
echo pause
) > "START_SYSTEM.bat"

REM إنشاء ملف معلومات النظام
(
echo ========================================
echo    🎓 نظام إدارة التدريب
echo    Training Management System
echo ========================================
echo.
echo 📋 معلومات النظام:
echo    • الإصدار: 1.0.0
echo    • تاريخ البناء: %date%
echo    • نوع التوزيع: Standalone EXE
echo.
echo 👤 معلومات تسجيل الدخول الافتراضية:
echo    • اسم المستخدم: admin
echo    • كلمة المرور: admin123
echo    • ⚠️  يرجى تغيير كلمة المرور بعد أول تسجيل دخول
echo.
echo 🌐 الوصول للنظام:
echo    • سيتم فتح النظام تلقائياً في المتصفح
echo    • الرابط المحلي: http://localhost:5000
echo    • إذا لم يفتح تلقائياً، انسخ الرابط في المتصفح
echo.
echo 🔧 استكشاف الأخطاء:
echo    • تأكد من عدم تشغيل برامج أخرى على نفس البورت
echo    • أعد تشغيل النظام إذا واجهت مشاكل
echo    • تحقق من وجود ملف قاعدة البيانات
echo.
echo 📞 الدعم الفني:
echo    • البريد الإلكتروني: <EMAIL>
echo    • الموقع: www.trainingsystem.com
echo.
echo © 2024 Training System. جميع الحقوق محفوظة
) > "SYSTEM_INFO.txt"

REM إنشاء ملف النسخ الاحتياطي
(
echo @echo off
echo chcp 65001 ^>nul
echo echo ========================================
echo echo    💾 نسخ احتياطي لقاعدة البيانات
echo echo    Database Backup
echo echo ========================================
echo echo.
echo if not exist "backup" mkdir backup
echo set backup_name=backup_%%date:~-4,4%%%%date:~-10,2%%%%date:~-7,2%%_%%time:~0,2%%%%time:~3,2%%%%time:~6,2%%
echo set backup_name=%%backup_name: =0%%
echo copy "training_system_deployment.db" "backup\%%backup_name%%.db"
echo echo ✅ تم إنشاء نسخة احتياطية: backup\%%backup_name%%.db
echo pause
) > "BACKUP_DATA.bat"

echo ✅ تم إنشاء ملفات التشغيل السريع
echo ✅ Quick start files created
echo.

echo 📊 حجم الملفات بعد التحسين:
echo 📊 File sizes after optimization:

for /f "tokens=3" %%a in ('dir /-c ^| find "File(s)"') do set size_after=%%a
echo    📏 الحجم الإجمالي: %size_after% بايت
echo    📏 Total size: %size_after% bytes

if defined size_before (
    set /a saved_space=%size_before%-%size_after%
    echo    💾 المساحة المحفوظة: !saved_space! بايت
    echo    💾 Space saved: !saved_space! bytes
)

echo.
echo ✅ تم تحسين الملفات بنجاح!
echo ✅ Files optimized successfully!
echo.

echo 📋 الملفات المنشأة:
echo 📋 Created files:
echo    ✓ START_SYSTEM.bat - ملف التشغيل السريع
echo    ✓ SYSTEM_INFO.txt - معلومات النظام
echo    ✓ BACKUP_DATA.bat - ملف النسخ الاحتياطي
echo.

echo 🔄 الخطوة التالية: تشغيل test_complete_system.bat
echo 🔄 Next step: Run test_complete_system.bat
echo.

cd ..\..
pause
