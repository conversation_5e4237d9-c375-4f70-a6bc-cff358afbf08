{"toolkit_info": {"name": "أداة إدارة التطوير والتوزيع المتقدمة", "name_en": "Advanced Development & Deployment Management Tool", "version": "1.0.0", "author": "Training System Development Team", "description": "أداة رسومية شاملة لإدارة عملية التطوير والتوزيع", "description_en": "Comprehensive GUI tool for development and deployment management"}, "ui_settings": {"window": {"title": "🚀 أداة إدارة التطوير والتوزيع المتقدمة - Developer Toolkit", "width": 1400, "height": 900, "min_width": 1200, "min_height": 800, "resizable": true}, "colors": {"primary": "#2E86AB", "secondary": "#A23B72", "success": "#F18F01", "warning": "#C73E1D", "background": "#F5F5F5", "card": "#FFFFFF", "text": "#2C3E50", "text_light": "#7F8C8D"}, "fonts": {"default": ["<PERSON><PERSON>", 10], "header": ["<PERSON><PERSON>", 18, "bold"], "title": ["<PERSON><PERSON>", 12, "bold"], "console": ["Consolas", 9]}}, "project_defaults": {"database_type": "empty", "build_type": "optimized", "include_samples": false, "create_installer": true, "auto_test": true, "auto_backup": true, "cleanup_after_build": false}, "build_steps": {"full_build": [{"id": "cleanup", "name": "تنظيف المشروع", "name_en": "Project Cleanup", "script": "cleanup_project.bat", "progress": 10, "timeout": 300, "required": true}, {"id": "environment", "name": "إنشاء بيئة افتراضية", "name_en": "Create Virtual Environment", "script": "create_clean_environment.bat", "progress": 20, "timeout": 600, "required": true}, {"id": "requirements", "name": "تثبيت المتطلبات", "name_en": "Install Requirements", "script": "install_requirements.bat", "progress": 35, "timeout": 900, "required": true}, {"id": "database", "name": "تحضير قاعدة البيانات", "name_en": "Prepare Database", "script": "prepare_database.bat", "progress": 50, "timeout": 300, "required": true}, {"id": "build", "name": "بناء ملف EXE", "name_en": "Build EXE", "script": "build_optimized_exe.bat", "progress": 75, "timeout": 1800, "required": true}, {"id": "optimize", "name": "تحسين الملفات", "name_en": "Optimize Files", "script": "optimize_files.bat", "progress": 85, "timeout": 300, "required": false}, {"id": "test", "name": "اختبار النظام", "name_en": "Test System", "script": "test_complete_system.bat", "progress": 95, "timeout": 600, "required": false}, {"id": "package", "name": "إنشاء حزمة العميل", "name_en": "Create Client Package", "script": "create_client_package.bat", "progress": 100, "timeout": 300, "required": false}]}, "database_options": {"types": [{"id": "empty", "name": "قاعدة بيانات فارغة", "name_en": "Empty Database", "description": "جداول فارغة مع مستخدم إداري واحد", "description_en": "Empty tables with one admin user", "recommended_for": "distribution"}, {"id": "full", "name": "قاعدة بيانات كاملة", "name_en": "Full Database", "description": "بيانات تجريبية شاملة للاختبار", "description_en": "Comprehensive sample data for testing", "recommended_for": "development"}, {"id": "update", "name": "تحديث قاعدة البيانات", "name_en": "Update Database", "description": "إضافة الجداول والحقول المفقودة", "description_en": "Add missing tables and columns", "recommended_for": "maintenance"}]}, "build_types": {"types": [{"id": "basic", "name": "بناء أساسي", "name_en": "Basic Build", "description": "بناء سريع بدون تحسينات", "description_en": "Quick build without optimizations"}, {"id": "optimized", "name": "بنا<PERSON> محسن", "name_en": "Optimized Build", "description": "بناء محسن للأداء والحجم", "description_en": "Optimized for performance and size"}, {"id": "portable", "name": "بناء محمول", "name_en": "Portable Build", "description": "ملف و<PERSON><PERSON><PERSON> يع<PERSON>ل على أي جهاز", "description_en": "Single file that works on any device"}]}, "analysis_checks": {"essential_files": [{"files": ["app.py", "main.py", "run.py"], "name": "الملف الرئيسي", "name_en": "Main File", "required": true, "weight": 20}, {"files": ["requirements.txt"], "name": "مل<PERSON> المتطلبات", "name_en": "Requirements File", "required": true, "weight": 15}], "essential_dirs": [{"dirs": ["static"], "name": "م<PERSON><PERSON><PERSON> الملفات الثابتة", "name_en": "Static Files Directory", "required": true, "weight": 10}, {"dirs": ["templates"], "name": "م<PERSON><PERSON><PERSON> القوالب", "name_en": "Templates Directory", "required": true, "weight": 10}], "imports_check": ["flask", "sqlite3", "os", "sys", "datetime"], "quality_thresholds": {"excellent": 90, "good": 70, "average": 50, "needs_improvement": 0}}, "testing": {"exe_search_paths": ["dist/TrainingSystem/TrainingSystem.exe", "dist/TrainingSystem.exe", "TrainingSystem.exe"], "test_urls": ["http://localhost:5000", "http://127.0.0.1:5000"], "timeouts": {"startup": 10, "response": 15, "shutdown": 5}}, "backup": {"exclude_dirs": ["dist", "build", "__pycache__", "venv_deployment", ".git", ".pytest_cache", "node_modules"], "exclude_files": ["*.pyc", "*.pyo", "*.log", "*_temp.py", "*.tmp"]}, "logging": {"levels": {"INFO": {"color": "#00ff00", "name": "معلومات", "name_en": "Info"}, "WARNING": {"color": "#ffff00", "name": "تحذير", "name_en": "Warning"}, "ERROR": {"color": "#ff0000", "name": "خطأ", "name_en": "Error"}, "SUCCESS": {"color": "#00ffff", "name": "نجح", "name_en": "Success"}, "DEBUG": {"color": "#ffffff", "name": "تشخيص", "name_en": "Debug"}}, "max_lines": 1000, "auto_scroll": true, "timestamp_format": "%H:%M:%S"}, "paths": {"deployment_package": "DEPLOYMENT_PACKAGE", "scripts_dir": "DEPLOYMENT_PACKAGE", "config_file": "project_config.json", "log_dir": "logs", "backup_dir": "backups"}, "features": {"auto_update_check": true, "crash_reporting": true, "usage_analytics": false, "beta_features": false, "developer_mode": true}, "support": {"email": "<EMAIL>", "website": "www.trainingsystem.com", "documentation": "docs.trainingsystem.com", "github": "github.com/trainingsystem"}}