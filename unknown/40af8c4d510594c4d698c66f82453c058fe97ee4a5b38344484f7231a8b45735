
from flask import Flask, render_template, redirect, url_for, flash, request, send_from_directory, abort, Response, jsonify, session, send_file, make_response
from flask_sqlalchemy import SQLAlchemy
from flask_login import <PERSON><PERSON><PERSON><PERSON><PERSON>, UserMixin, login_user, logout_user, login_required, current_user
from flask_wtf import FlaskForm, CSRFProtect
from flask_wtf.file import FileField, FileAllowed, FileSize
from wtforms import StringField, PasswordField, SubmitField, BooleanField, TextAreaField, SelectField, DateField, IntegerField, FloatField, TimeField
from wtforms.validators import DataRequired, Email, Length, EqualTo, ValidationError, Optional, NumberRange
from werkzeug.security import generate_password_hash, check_password_hash
from werkzeug.utils import secure_filename
import os
import math
import csv
import io
import re
import pandas as pd
import numpy as np
import difflib
import arabic_reshaper
from bidi.algorithm import get_display
from datetime import datetime, date, timedelta, timezone
import threading
import backup_utils
import sqlalchemy
import logging
import time
from tqdm import tqdm # type: ignore
# تعطيل استيراد api_routes
# from api_routes import api, init_api
# تعطيل استيراد api_routes_simple
# from api_routes_simple import api_simple, init_api
# استيراد person_data_routes
from person_data_routes import init_person_data_routes
# استيراد مولد التقارير
from reports_generator import reports_generator

# إنشاء تطبيق Flask
app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key-here'

# سيتم تسجيل Blueprint API بعد تعريف النماذج

# تهيئة حماية CSRF
csrf = CSRFProtect(app)

# استثناء مسارات التقييم من CSRF
@csrf.exempt
def evaluation_routes_exempt():
    pass

app.config['SECRET_KEY'] = 'your-secret-key-here'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///training_system.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False  # إيقاف تتبع التعديلات لتحسين الأداء
app.config['UPLOAD_FOLDER'] = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'static/uploads')

app.config['MAX_CONTENT_LENGTH'] = 500 * 1024 * 1024  # 500 MB max upload size

# التأكد من وجود مجلد التحميل
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

# إنشاء قفل للتزامن لمنع تضارب الأرقام العامة للدورات
course_number_lock = threading.Lock()

# إعداد التسجيل (Logging)
logging.basicConfig(
    filename='import_errors.log',
    level=logging.ERROR,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# دالة مساعدة لاستيراد البيانات على دفعات
def validate_date(date_value):
    if date_value is None or date_value == '' or date_value == '0' or date_value == 0:
        return None
    try:
        if isinstance(date_value, str):
            return datetime.strptime(date_value, '%Y-%m-%d').date()
        return date_value
    except ValueError:
        return None

def import_excel_data_in_chunks(file_path, user_id, session_obj=None, chunk_size=1000):
    """
    استيراد بيانات من ملف Excel إلى قاعدة البيانات على دفعات

    Args:
        file_path (str): مسار ملف Excel
        user_id (int): معرف المستخدم الذي يقوم بالاستيراد
        session_obj (dict, optional): كائن الجلسة لتحديث التقدم
        chunk_size (int, optional): حجم الدفعة. الافتراضي هو 1000.

    Returns:
        dict: نتائج الاستيراد (عدد النجاح، عدد الفشل، البيانات المستوردة، بيانات الأخطاء)
    """
    try:
        # قراءة ملف Excel للحصول على العدد الإجمالي للصفوف
        total_rows = pd.read_excel(file_path).shape[0]
        print(f"إجمالي عدد السجلات في Excel: {total_rows}")

        # تهيئة متغيرات النتائج
        success_count = 0
        error_count = 0
        imported_data = []
        error_data = []

        # تحديث معلومات التقدم في الجلسة
        if session_obj:
            session_obj['import_progress'] = {
                'total': total_rows,
                'processed': 0,
                'success': 0,
                'error': 0,
                'status': 'جاري الاستيراد...'
            }
            session_obj.modified = True

        # قراءة ملف Excel على دفعات
        for i, chunk_df in enumerate(pd.read_excel(file_path, chunksize=chunk_size)):
            chunk_num = i + 1
            chunk_start = i * chunk_size + 1
            chunk_end = chunk_start + len(chunk_df) - 1
            print(f"معالجة الدفعة رقم {chunk_num} (صفوف من {chunk_start} إلى {chunk_end})")

            # معالجة كل صف في الدفعة
            for index, row in chunk_df.iterrows():
                try:
                    # تحديث التقدم
                    processed_row = i * chunk_size + (index - chunk_df.index[0]) + 1

                    if session_obj:
                        session_obj['import_progress']['processed'] = processed_row
                        session_obj.modified = True

                    # التحقق من أن القيم غير فارغة
                    if pd.isna(row['الاسم الشخصي']) or pd.isna(row['الرقم الوطني']):
                        error_count += 1
                        if session_obj:
                            session_obj['import_progress']['error'] = error_count
                            session_obj.modified = True
                        error_data.append({
                            'row': processed_row,
                            'error': 'بيانات فارغة',
                            'data': f"صف {processed_row}"
                        })
                        continue

                    # تحويل الرقم الوطني إلى نص
                    national_number = str(row['الرقم الوطني'])
                    if national_number.endswith('.0'):
                        national_number = national_number[:-2]

                    # تحضير البيانات
                    full_name = str(row['الاسم الشخصي'])

                    # التحقق من عدم وجود سجل بنفس الرقم الوطني
                    existing_record = db.session.execute(
                        sqlalchemy.text("SELECT id FROM personal_data WHERE national_number = :national_number"),
                        {"national_number": national_number}
                    ).fetchone()

                    if existing_record:
                        error_count += 1
                        if session_obj:
                            session_obj['import_progress']['error'] = error_count
                            session_obj.modified = True
                        error_data.append({
                            'row': processed_row,
                            'error': 'السجل موجود بالفعل',
                            'data': f"{full_name} - {national_number}"
                        })
                        continue

                    # تحضير البيانات الإضافية
                    nickname = str(row['الاسم المستعار']) if 'الاسم المستعار' in row and pd.notna(row['الاسم المستعار']) else None

                    # تحضير العمر
                    age = None
                    if 'العمر' in row and pd.notna(row['العمر']):
                        try:
                            age = int(float(str(row['العمر'])))
                        except:
                            pass

                    # تحضير بيانات النسب (حقول نصية مباشرة)
                    governorate = str(row['المحافظة']).strip() if 'المحافظة' in row and not pd.isna(row['المحافظة']) else None
                    directorate = str(row['المديرية']).strip() if 'المديرية' in row and not pd.isna(row['المديرية']) else None
                    uzla = str(row['العزلة']).strip() if 'العزلة' in row and not pd.isna(row['العزلة']) else None
                    village = str(row['الحي/القرية']).strip() if 'الحي/القرية' in row and not pd.isna(row['الحي/القرية']) else None

                    # تجميع معلومات السكن
                    residence_parts = []
                    if governorate:
                        residence_parts.append(governorate)
                    if directorate:
                        residence_parts.append(directorate)
                    if uzla:
                        residence_parts.append(uzla)
                    if village:
                        residence_parts.append(village)

                    residence_house = " - ".join(residence_parts) if residence_parts else None

                    # معلومات العمل
                    job_title = str(row['العمل']) if 'العمل' in row and pd.notna(row['العمل']) else None

                    work_place = None
                    if 'الإدارة' in row and pd.notna(row['الإدارة']):
                        work_place = str(row['الإدارة'])
                    elif 'مكان العمل' in row and pd.notna(row['مكان العمل']):
                        work_place = str(row['مكان العمل'])

                    # أرقام إضافية
                    military_number = None
                    if 'الرقم العسكري' in row and pd.notna(row['الرقم العسكري']):
                        military_number = str(row['الرقم العسكري'])
                        if military_number.endswith('.0'):
                            military_number = military_number[:-2]

                    phone = None
                    if 'رقم الهاتف' in row and pd.notna(row['رقم الهاتف']):
                        phone = str(row['رقم الهاتف'])
                        if phone.endswith('.0'):
                            phone = phone[:-2]

                    # معلومات إضافية
                    notes_parts = []
                    qualification = None
                    marital_status = None

                    if 'المؤهل العلمي' in row and pd.notna(row['المؤهل العلمي']):
                        qualification = str(row['المؤهل العلمي'])
                        notes_parts.append(f"المؤهل العلمي: {qualification}")

                    if 'الحالة الاجتماعية' in row and pd.notna(row['الحالة الاجتماعية']):
                        marital_status = str(row['الحالة الاجتماعية'])
                        notes_parts.append(f"الحالة الاجتماعية: {marital_status}")

                    notes = ", ".join(notes_parts) if notes_parts else None

                    # إنشاء سجل جديد باستخدام كائن PersonalData
                    personal_data = PersonalData(
                        user_id=user_id,
                        full_name=full_name,
                        national_number=national_number,
                        nickname=nickname,
                        age=age,
                        # استخدام الحقول النصية المباشرة
                        governorate=governorate,
                        directorate=directorate,
                        uzla=uzla,
                        village=village,
                        residence_house=residence_house,
                        job_title=job_title,
                        work_place_text=work_place,
                        military_number=military_number,
                        phone_yemen_mobile=phone,
                        notes=notes
                    )

                    # التحقق من أن personal_data هو كائن من نوع PersonalData وليس نصًا
                    if not isinstance(personal_data, PersonalData):
                        raise TypeError(f"خطأ: personal_data ليس كائن PersonalData، بل هو {type(personal_data)}")

                    # إضافة السجل إلى قاعدة البيانات
                    db.session.add(personal_data)

                    # الالتزام بالتغييرات
                    db.session.commit()

                    # تحديث العدادات
                    success_count += 1
                    if session_obj:
                        session_obj['import_progress']['success'] = success_count
                        session_obj.modified = True

                    # إضافة إلى قائمة النجاح
                    imported_data.append({
                        'name': full_name,
                        'national_id': national_number
                    })

                except Exception as e:
                    # تسجيل الخطأ
                    logger.error(f"خطأ في استيراد السجل {processed_row}: {e}")

                    error_count += 1
                    if session_obj:
                        session_obj['import_progress']['error'] = error_count
                        session_obj.modified = True
                    error_data.append({
                        'row': processed_row,
                        'error': str(e),
                        'data': f"صف {processed_row}"
                    })
                    db.session.rollback()

            # تحديث الجلسة بعد كل دفعة
            if session_obj:
                session_obj.modified = True

        # تحديث حالة الاستيراد
        if session_obj:
            session_obj['import_progress']['status'] = 'تم الاستيراد بنجاح'
            session_obj.modified = True

        # إعداد نتائج الاستيراد
        import_results = {
            'success': success_count,
            'error': error_count,
            'imported_data': imported_data,
            'error_data': error_data
        }

        return import_results

    except Exception as e:
        # تسجيل الخطأ العام
        logger.error(f"خطأ عام في استيراد البيانات: {e}")

        if session_obj:
            session_obj['import_progress']['status'] = 'فشل الاستيراد'
            session_obj.modified = True

        return {
            'success': success_count if 'success_count' in locals() else 0,
            'error': error_count if 'error_count' in locals() else 0,
            'imported_data': imported_data if 'imported_data' in locals() else [],
            'error_data': error_data if 'error_data' in locals() else [{'row': 0, 'error': str(e), 'data': 'خطأ عام'}]
        }

# إنشاء مجلد التحميلات إذا لم يكن موجودًا
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

# دالة مساعدة لتحويل حجم الملف إلى صيغة مقروءة
def format_file_size(size_bytes):
    if size_bytes == 0:
        return "0B"
    size_name = ("B", "KB", "MB", "GB", "TB")
    i = int(math.floor(math.log(size_bytes, 1024)))
    p = math.pow(1024, i)
    s = round(size_bytes / p, 2)
    return f"{s} {size_name[i]}"

# الحد الأقصى لحجم الملف بالبايت
MAX_FILE_SIZE = app.config['MAX_CONTENT_LENGTH']
# الحد الأقصى لحجم الملف بصيغة مقروءة
MAX_FILE_SIZE_READABLE = format_file_size(MAX_FILE_SIZE)

# إنشاء قاعدة البيانات
db = SQLAlchemy(app)

# استيراد النظام الذكي للنسخ الاحتياطي
from backup_utils import smart_backup_manager

# إعداد مدير تسجيل الدخول
login_manager = LoginManager(app)
login_manager.login_view = 'login'
login_manager.login_message = 'يرجى تسجيل الدخول للوصول إلى هذه الصفحة'
login_manager.login_message_category = 'info'

# تعريف نماذج قاعدة البيانات
class User(UserMixin, db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(20), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password = db.Column(db.String(60), nullable=False)
    role = db.Column(db.String(20), nullable=False, default='trainee')  # trainee, trainer, admin
    created_at = db.Column(db.DateTime, nullable=False, default=lambda: datetime.now(timezone.utc))

    # العلاقات
    courses_created = db.relationship('Course', backref='trainer', lazy=True, foreign_keys='Course.trainer_id')
    enrollments = db.relationship('Enrollment', backref='trainee', lazy=True)
    personal_data = db.relationship('PersonalData', backref='user', lazy=True, uselist=False)

    def __repr__(self):
        return f"User('{self.username}', '{self.email}', '{self.role}')"

class CoursePath(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False, unique=True)
    description = db.Column(db.Text, nullable=True)
    code = db.Column(db.String(20), nullable=True)

    # العلاقات
    levels = db.relationship('CoursePathLevel', backref='path', lazy=True, cascade="all, delete-orphan")

    def __repr__(self):
        return f"CoursePath('{self.name}')"

class CoursePathLevel(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text, nullable=True)
    code = db.Column(db.String(20), nullable=True)
    order = db.Column(db.Integer, nullable=True)  # ترتيب المستوى في المسار
    path_id = db.Column(db.Integer, db.ForeignKey('course_path.id'), nullable=False)

    def __repr__(self):
        return f"CoursePathLevel('{self.name}', Path: {self.path_id})"

class ForceClassification(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False, unique=True)
    description = db.Column(db.Text, nullable=True)

    def __repr__(self):
        return f"ForceClassification('{self.name}')"

class Course(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    course_number = db.Column(db.String(20), nullable=False, unique=True)  # رقم الدورة العام (مدخل من المستخدم)
    agency_course_number = db.Column(db.String(20), nullable=True)  # رقم الدورة في الجهة
    place_course_number = db.Column(db.String(20), nullable=True)  # رقم الدورة في المكان
    title = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text, nullable=False)
    category = db.Column(db.String(50), nullable=False)
    level = db.Column(db.String(20), nullable=False)  # beginner, intermediate, advanced
    image = db.Column(db.String(100), nullable=True, default='default_course.jpg')
    start_date = db.Column(db.DateTime, nullable=False)  # تاريخ البدء الميلادي
    end_date = db.Column(db.DateTime, nullable=False)  # تاريخ النهاية الميلادي
    start_date_hijri = db.Column(db.String(20), nullable=False)  # تاريخ البدء الهجري
    end_date_hijri = db.Column(db.String(20), nullable=False)  # تاريخ النهاية الهجري
    duration_days = db.Column(db.Integer, nullable=False)
    trainer_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    created_at = db.Column(db.DateTime, nullable=False, default=lambda: datetime.now(timezone.utc))

    # حقول المسار والمستوى
    path_id = db.Column(db.Integer, db.ForeignKey('course_path.id'), nullable=True)  # المسار
    path_level_id = db.Column(db.Integer, db.ForeignKey('course_path_level.id'), nullable=True)  # المستوى في المسار

    # حقول إضافية حسب النموذج المرفق
    agency_id = db.Column(db.Integer, db.ForeignKey('agency.id'), nullable=True)  # الجهة
    center_id = db.Column(db.Integer, db.ForeignKey('training_center.id'), nullable=True)  # المركز التدريبي
    place_name = db.Column(db.String(100), nullable=True)  # اسم المكان
    participant_type_id = db.Column(db.Integer, db.ForeignKey('participant_type.id'), nullable=True)  # نوع المشاركين
    status_id = db.Column(db.Integer, db.ForeignKey('course_level.id'), nullable=True)  # المستوى
    entry_date = db.Column(db.DateTime, nullable=True)  # تاريخ الدخول ميلادي
    exit_date = db.Column(db.DateTime, nullable=True)  # تاريخ الخروج ميلادي
    entry_date_hijri = db.Column(db.String(20), nullable=True)  # تاريخ الدخول هجري
    exit_date_hijri = db.Column(db.String(20), nullable=True)  # تاريخ الخروج هجري
    force_classification_id = db.Column(db.Integer, db.ForeignKey('force_classification.id'), nullable=True)  # تصنيف القوة
    target_agency_id = db.Column(db.Integer, db.ForeignKey('agency.id'), nullable=True)  # الجهة التابعة للمستهدفين
    target_count = db.Column(db.Integer, nullable=True)  # عدد المستهدفين
    card_type = db.Column(db.String(50), nullable=True)  # نوع البطاقة (نص بدلاً من علاقة)

    # حقول مالية
    total_participants = db.Column(db.Integer, nullable=True)  # إجمالي عدد المشاركين
    total_graduates = db.Column(db.Integer, nullable=True)  # إجمالي عدد الخريجين
    total_dropouts = db.Column(db.Integer, nullable=True)  # إجمالي عدد المنسحبين
    daily_allowance = db.Column(db.Float, nullable=True)  # مبلغ الإعاشة اليومي
    transportation_allowance = db.Column(db.Float, nullable=True)  # مبلغ المواصلات
    accommodation_allowance = db.Column(db.Float, nullable=True)  # مبلغ السكن
    total_allowance = db.Column(db.Float, nullable=True)  # إجمالي المبلغ

    # ملاحظات
    notes = db.Column(db.Text, nullable=True)

    # العلاقات
    materials = db.relationship('Material', backref='course', lazy=True, cascade="all, delete-orphan")
    enrollments = db.relationship('Enrollment', backref='course', lazy=True, cascade="all, delete-orphan")
    participants = db.relationship('CourseParticipant', backref='course', lazy=True, cascade="all, delete-orphan")
    path = db.relationship('CoursePath', backref='courses')
    path_level = db.relationship('CoursePathLevel', backref='courses')
    agency = db.relationship('Agency', foreign_keys=[agency_id], backref='agency_courses')
    center = db.relationship('TrainingCenter', backref='courses')
    participant_type = db.relationship('ParticipantType', backref='courses')
    status = db.relationship('CourseLevel', backref='courses')
    force_classification = db.relationship('ForceClassification', backref='courses')
    target_agency = db.relationship('Agency', foreign_keys=[target_agency_id], backref='target_courses')

    def __repr__(self):
        return f"Course('{self.course_number}', '{self.title}', '{self.category}')"

class Material(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text, nullable=True)
    file_path = db.Column(db.String(255), nullable=True)
    file_type = db.Column(db.String(20), nullable=True)  # pdf, video, image, presentation, document
    day_number = db.Column(db.Integer, nullable=False)  # رقم اليوم في الدورة
    course_id = db.Column(db.Integer, db.ForeignKey('course.id'), nullable=False)
    created_at = db.Column(db.DateTime, nullable=False, default=lambda: datetime.now(timezone.utc))

    def __repr__(self):
        return f"Material('{self.title}', Day {self.day_number}, '{self.file_type}')"

# الجدول الزمني للدورة
class CourseSchedule(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    course_id = db.Column(db.Integer, db.ForeignKey('course.id'), nullable=False)
    day_number = db.Column(db.Integer, nullable=False)  # رقم اليوم
    start_time = db.Column(db.String(5), nullable=False)  # وقت البدء (HH:MM)
    end_time = db.Column(db.String(5), nullable=False)  # وقت الانتهاء (HH:MM)
    title = db.Column(db.String(100), nullable=False)  # عنوان الفترة
    description = db.Column(db.Text, nullable=True)  # وصف الفترة
    is_break = db.Column(db.Boolean, default=False)  # هل هي فترة استراحة
    material_id = db.Column(db.Integer, db.ForeignKey('material.id'), nullable=True)  # المادة المرتبطة (اختياري)

    # العلاقات
    course = db.relationship('Course', backref='schedule_items')
    material = db.relationship('Material', backref='schedule_items')

    def __repr__(self):
        return f"CourseSchedule(Day: {self.day_number}, {self.start_time}-{self.end_time}, '{self.title}')"

class Enrollment(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    course_id = db.Column(db.Integer, db.ForeignKey('course.id'), nullable=False)
    enrollment_date = db.Column(db.DateTime, nullable=False, default=lambda: datetime.now(timezone.utc))
    status = db.Column(db.String(20), nullable=False, default='active')  # active, completed, dropped

    def __repr__(self):
        return f"Enrollment(User ID: {self.user_id}, Course ID: {self.course_id}, Status: {self.status})"

# المشاركين في الدورة
class CourseParticipant(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    course_id = db.Column(db.Integer, db.ForeignKey('course.id'), nullable=False)
    personal_data_id = db.Column(db.Integer, db.ForeignKey('person_data.id'), nullable=False)

    # بيانات المشارك
    entry_date = db.Column(db.DateTime, nullable=True)  # تاريخ الدخول
    exit_date = db.Column(db.DateTime, nullable=True)  # تاريخ الخروج
    status = db.Column(db.String(20), nullable=False, default='active')  # active, completed, dropped

    # بيانات مالية
    daily_allowance = db.Column(db.Float, nullable=True)  # مبلغ الإعاشة اليومي
    transportation_allowance = db.Column(db.Float, nullable=True)  # مبلغ المواصلات
    accommodation_allowance = db.Column(db.Float, nullable=True)  # مبلغ السكن
    total_allowance = db.Column(db.Float, nullable=True)  # إجمالي المبلغ

    # بيانات الدفع
    payment_status = db.Column(db.String(20), nullable=True, default='pending')  # pending, paid, cancelled
    payment_date = db.Column(db.DateTime, nullable=True)  # تاريخ الدفع
    payment_method = db.Column(db.String(50), nullable=True)  # طريقة الدفع
    payment_reference = db.Column(db.String(100), nullable=True)  # مرجع الدفع

    # ملاحظات
    notes = db.Column(db.Text, nullable=True)

    # العلاقات
    personal_data = db.relationship('PersonData', backref='course_participations')

    def __repr__(self):
        return f"CourseParticipant(Course ID: {self.course_id}, Participant: {self.personal_data.full_name if self.personal_data else 'Unknown'}, Status: {self.status})"

# الجداول الترميزية

# جدول المحافظات
class Governorate(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False, unique=True)
    code = db.Column(db.String(10), nullable=True)

    # العلاقات
    directorates = db.relationship('Directorate', backref='governorate', lazy=True, cascade="all, delete-orphan")

    def __repr__(self):
        return f"Governorate('{self.name}')"

# جدول المديريات
class Directorate(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    code = db.Column(db.String(10), nullable=True)
    governorate_id = db.Column(db.Integer, db.ForeignKey('governorate.id'), nullable=False)

    # العلاقات
    villages = db.relationship('Village', backref='directorate', lazy=True, cascade="all, delete-orphan")

    def __repr__(self):
        return f"Directorate('{self.name}', Governorate ID: {self.governorate_id})"

# جدول القرى/الأحياء
class Village(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    code = db.Column(db.String(10), nullable=True)
    directorate_id = db.Column(db.Integer, db.ForeignKey('directorate.id'), nullable=False)

    def __repr__(self):
        return f"Village('{self.name}', Directorate ID: {self.directorate_id})"

# جدول الحالة الاجتماعية
class MaritalStatus(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(50), nullable=False, unique=True)

    def __repr__(self):
        return f"MaritalStatus('{self.name}')"

# جدول فصائل الدم
class BloodType(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(10), nullable=False, unique=True)

    def __repr__(self):
        return f"BloodType('{self.name}')"

# جدول جهات الإصدار
class IssuingAuthority(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False, unique=True)
    parent_id = db.Column(db.Integer, db.ForeignKey('issuing_authority.id'), nullable=True)

    # العلاقات (شجرة)
    children = db.relationship('IssuingAuthority', backref=db.backref('parent', remote_side=[id]), lazy=True)

    def __repr__(self):
        return f"IssuingAuthority('{self.name}')"

# جدول جهات العمل
class WorkPlace(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False, unique=True)
    parent_id = db.Column(db.Integer, db.ForeignKey('work_place.id'), nullable=True)

    # العلاقات (شجرة)
    children = db.relationship('WorkPlace', backref=db.backref('parent', remote_side=[id]), lazy=True)

    def __repr__(self):
        return f"WorkPlace('{self.name}')"

# جدول المؤهلات العلمية
class QualificationType(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False, unique=True)
    description = db.Column(db.Text, nullable=True)
    level = db.Column(db.String(50), nullable=True)  # مستوى المؤهل (ابتدائي، متوسط، ثانوي، جامعي، دراسات عليا)
    code = db.Column(db.String(20), nullable=True)  # رمز المؤهل

    def __repr__(self):
        return f"QualificationType('{self.name}')"

# جدول التخصصات
class Specialization(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False, unique=True)
    description = db.Column(db.Text, nullable=True)
    field = db.Column(db.String(100), nullable=True)  # مجال التخصص (طبي، هندسي، إداري، الخ)
    code = db.Column(db.String(20), nullable=True)  # رمز التخصص

    def __repr__(self):
        return f"Specialization('{self.name}')"

# جدول أنواع التكليف
class AssignmentType(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False, unique=True)

    def __repr__(self):
        return f"AssignmentType('{self.name}')"

# جدول الرتب العسكرية
class MilitaryRank(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False, unique=True)
    code = db.Column(db.String(10), nullable=True)

    def __repr__(self):
        return f"MilitaryRank('{self.name}')"

# جدول أنواع الإصابات
class InjuryType(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False, unique=True)

    def __repr__(self):
        return f"InjuryType('{self.name}')"

# جدول أسباب الإصابات
class InjuryCause(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False, unique=True)

    def __repr__(self):
        return f"InjuryCause('{self.name}')"

# جدول أنواع الدورات
class CourseType(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False, unique=True)
    category = db.Column(db.String(50), nullable=False)  # فنية، قيادية، أمنية، أخرى

    def __repr__(self):
        return f"CourseType('{self.name}', Category: {self.category})"

# جدول أنواع المراكز التدريبية
class TrainingCenterType(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False, unique=True)
    description = db.Column(db.Text, nullable=True)

    # العلاقات
    centers = db.relationship('TrainingCenter', backref='center_type', lazy=True)

    def __repr__(self):
        return f"TrainingCenterType('{self.name}')"

# جدول المواقع
class Location(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False, unique=True)
    address = db.Column(db.String(200), nullable=True)
    governorate_id = db.Column(db.Integer, db.ForeignKey('governorate.id'), nullable=True)
    directorate_id = db.Column(db.Integer, db.ForeignKey('directorate.id'), nullable=True)

    # العلاقات
    governorate = db.relationship('Governorate', backref='locations')
    directorate = db.relationship('Directorate', backref='locations')
    centers = db.relationship('TrainingCenter', backref='specific_location', lazy=True)

    def __repr__(self):
        return f"Location('{self.name}', Address: {self.address})"

# جدول المراكز التدريبية
class TrainingCenter(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False, unique=True)
    center_type_id = db.Column(db.Integer, db.ForeignKey('training_center_type.id'), nullable=True)
    location_id = db.Column(db.Integer, db.ForeignKey('location.id'), nullable=True)
    governorate_id = db.Column(db.Integer, db.ForeignKey('governorate.id'), nullable=True)
    directorate_id = db.Column(db.Integer, db.ForeignKey('directorate.id'), nullable=True)
    agency_id = db.Column(db.Integer, db.ForeignKey('agency.id'), nullable=True)  # الجهة التابع لها المركز
    capacity = db.Column(db.Integer, nullable=True)  # السعة الاستيعابية
    is_ready = db.Column(db.Boolean, default=True)  # الجهوزية (جاهز/غير جاهز)
    not_ready_reason = db.Column(db.Text, nullable=True)  # سبب عدم الجهوزية
    notes = db.Column(db.Text, nullable=True)  # ملاحظات

    # العلاقات
    governorate = db.relationship('Governorate', backref='training_centers')
    directorate = db.relationship('Directorate', backref='training_centers')
    agency = db.relationship('Agency', backref='training_centers')

    def __repr__(self):
        return f"TrainingCenter('{self.name}', Type: {self.center_type_id}, Ready: {self.is_ready})"

# جدول الجهات
class Agency(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False, unique=True)
    code = db.Column(db.String(20), nullable=True)
    parent_id = db.Column(db.Integer, db.ForeignKey('agency.id'), nullable=True)

    # العلاقات (شجرة)
    children = db.relationship('Agency', backref=db.backref('parent', remote_side=[id]), lazy=True)

    def __repr__(self):
        return f"Agency('{self.name}')"

# جدول أنواع المشاركين
class ParticipantType(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False, unique=True)

    def __repr__(self):
        return f"ParticipantType('{self.name}')"

# جدول أنواع البطاقات
class CardType(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False, unique=True)

    def __repr__(self):
        return f"CardType('{self.name}')"

# جدول المستويات
class CourseLevel(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False, unique=True)

    def __repr__(self):
        return f"CourseLevel('{self.name}')"

# جدول تصنيفات الدورات
class CourseCategory(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False, unique=True)
    code = db.Column(db.String(20), nullable=True)

    def __repr__(self):
        return f"CourseCategory('{self.name}')"

# جدول الأقسام
class Department(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False, unique=True)
    code = db.Column(db.String(20), nullable=True)
    parent_id = db.Column(db.Integer, db.ForeignKey('department.id'), nullable=True)
    agency_id = db.Column(db.Integer, db.ForeignKey('agency.id'), nullable=True)

    # العلاقات (شجرة)
    children = db.relationship('Department', backref=db.backref('parent', remote_side=[id]), lazy=True)
    agency = db.relationship('Agency', backref='departments')

    def __repr__(self):
        return f"Department('{self.name}')"

# جدول طرق الدفع
class PaymentMethod(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False, unique=True)

    def __repr__(self):
        return f"PaymentMethod('{self.name}')"

# جدول بيانات الأفراد
class PersonData(db.Model):
    __tablename__ = 'person_data'
    id = db.Column(db.Integer, primary_key=True)
    full_name = db.Column(db.String(100), nullable=False)  # الاسم الشخصي
    nickname = db.Column(db.String(50), nullable=True)     # الاسم المستعار
    age = db.Column(db.Integer, nullable=True)             # العمر
    governorate = db.Column(db.String(100), nullable=True) # المحافظة
    directorate = db.Column(db.String(100), nullable=True) # المديرية
    uzla = db.Column(db.String(100), nullable=True)        # العزلة
    village = db.Column(db.String(100), nullable=True)     # الحي/القرية
    qualification = db.Column(db.String(100), nullable=True) # المؤهل العلمي
    marital_status = db.Column(db.String(100), nullable=True) # الحالة الاجتماعية
    job = db.Column(db.String(100), nullable=True)         # العمل
    agency = db.Column(db.String(100), nullable=True)      # الإدارة
    work_place = db.Column(db.String(100), nullable=True)  # مكان العمل
    national_number = db.Column(db.String(20), nullable=True) # الرقم الوطني
    military_number = db.Column(db.String(20), nullable=True) # الرقم العسكري
    phone = db.Column(db.String(20), nullable=True)        # رقم التلفون

    def __repr__(self):
        return f"PersonData('{self.full_name}')"

# جدول التصحيحات المخصصة للأسماء
class NameCorrection(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    wrong_name = db.Column(db.String(200), nullable=False)     # الاسم الخطأ
    correct_name = db.Column(db.String(200), nullable=False)   # الاسم الصحيح
    correction_type = db.Column(db.String(50), nullable=False) # نوع التصحيح
    created_by = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False) # المستخدم الذي أضاف التصحيح
    created_at = db.Column(db.DateTime, nullable=False, default=lambda: datetime.now(timezone.utc))
    is_active = db.Column(db.Boolean, nullable=False, default=True) # هل التصحيح نشط
    usage_count = db.Column(db.Integer, nullable=False, default=0)  # عدد مرات الاستخدام

    # العلاقات
    creator = db.relationship('User', backref='name_corrections', lazy=True)

    def __repr__(self):
        return f"NameCorrection('{self.wrong_name}' -> '{self.correct_name}')"

# جدول البيانات الشخصية المبسطة
class SimplePersonalData(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, nullable=False)
    full_name = db.Column(db.String(100), nullable=False)  # الاسم الشخصي
    nickname = db.Column(db.String(50), nullable=True)     # الاسم المستعار
    age = db.Column(db.Integer, nullable=True)             # العمر
    governorate = db.Column(db.String(100), nullable=True) # المحافظة
    directorate = db.Column(db.String(100), nullable=True) # المديرية
    uzla = db.Column(db.String(100), nullable=True)        # العزلة
    village = db.Column(db.String(100), nullable=True)     # الحي/القرية
    qualification = db.Column(db.String(100), nullable=True) # المؤهل العلمي
    marital_status = db.Column(db.String(100), nullable=True) # الحالة الاجتماعية
    job = db.Column(db.String(100), nullable=True)         # العمل
    agency = db.Column(db.String(100), nullable=True)      # الإدارة
    work_place = db.Column(db.String(100), nullable=True)  # مكان العمل
    national_number = db.Column(db.String(20), nullable=True) # الرقم الوطني
    military_number = db.Column(db.String(20), nullable=True) # الرقم العسكري
    phone = db.Column(db.String(20), nullable=True)        # رقم التلفون

    def __repr__(self):
        return f"SimplePersonalData('{self.full_name}')"

# البيانات الشخصية
class PersonalData(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)

    # البيانات الشخصية
    full_name = db.Column(db.String(100), nullable=False)
    nickname = db.Column(db.String(50), nullable=True)
    triple_number = db.Column(db.String(20), nullable=True)
    national_number = db.Column(db.String(20), nullable=True)
    birth_date = db.Column(db.Date, nullable=True)
    age = db.Column(db.Integer, nullable=True)
    issue_date = db.Column(db.Date, nullable=True)
    issuing_authority_id = db.Column(db.Integer, db.ForeignKey('issuing_authority.id'), nullable=True)
    children_count = db.Column(db.Integer, nullable=True)
    marital_status_id = db.Column(db.Integer, db.ForeignKey('marital_status.id'), nullable=True)

    # حقول جديدة للتوافق مع نموذج الإكسل
    work_number = db.Column(db.String(20), nullable=True)  # الرقم الوظيفي
    work_rank = db.Column(db.String(100), nullable=True)  # الرتبة الوظيفية
    work_place = db.Column(db.String(100), nullable=True)  # مكان العمل (قديم)
    work_place_text = db.Column(db.String(100), nullable=True)  # مكان العمل (نص)
    military_rank = db.Column(db.String(50), nullable=True)  # الرتبة العسكرية

    # بيانات النسب (تستخدم حقول نصية مباشرة)
    governorate = db.Column(db.String(100), nullable=True)  # المحافظة (نص)
    directorate = db.Column(db.String(100), nullable=True)  # المديرية (نص)
    village = db.Column(db.String(100), nullable=True)      # القرية (نص)
    uzla = db.Column(db.String(100), nullable=True)         # العزلة (نص)

    # حقول نصية مباشرة للمؤهل العلمي والحالة الاجتماعية
    qualification_text = db.Column(db.String(100), nullable=True)  # المؤهل العلمي (نص)
    marital_status_text = db.Column(db.String(100), nullable=True)  # الحالة الاجتماعية (نص)

    # بيانات السكن
    residence_governorate_id = db.Column(db.Integer, db.ForeignKey('governorate.id'), nullable=True)
    residence_directorate_id = db.Column(db.Integer, db.ForeignKey('directorate.id'), nullable=True)
    residence_village_id = db.Column(db.Integer, db.ForeignKey('village.id'), nullable=True)
    residence_house = db.Column(db.String(100), nullable=True)
    residence_type = db.Column(db.String(20), nullable=True)  # ملك/إيجار
    phone_yemen_mobile = db.Column(db.String(20), nullable=True)
    phone_you = db.Column(db.String(20), nullable=True)
    phone_sayyon = db.Column(db.String(20), nullable=True)
    phone_landline = db.Column(db.String(20), nullable=True)

    # المؤهلات العلمية
    qualification_type_id = db.Column(db.Integer, db.ForeignKey('qualification_type.id'), nullable=True)
    specialization_id = db.Column(db.Integer, db.ForeignKey('specialization.id'), nullable=True)
    qualification_place = db.Column(db.String(100), nullable=True)
    qualification_date = db.Column(db.Date, nullable=True)

    # العمل الحالي - استخدام جدول Agency بدلاً من WorkPlace
    work_place_id = db.Column(db.Integer, db.ForeignKey('agency.id'), nullable=True)
    agency_id = db.Column(db.Integer, db.ForeignKey('agency.id'), nullable=True)  # الإدارة
    job_title = db.Column(db.String(100), nullable=True)
    work_governorate_id = db.Column(db.Integer, db.ForeignKey('governorate.id'), nullable=True)
    work_directorate_id = db.Column(db.Integer, db.ForeignKey('directorate.id'), nullable=True)
    work_village_id = db.Column(db.Integer, db.ForeignKey('village.id'), nullable=True)
    work_unit = db.Column(db.String(100), nullable=True)
    assignment_type_id = db.Column(db.Integer, db.ForeignKey('assignment_type.id'), nullable=True)
    assignment_date = db.Column(db.Date, nullable=True)
    assignment_authority_id = db.Column(db.Integer, db.ForeignKey('issuing_authority.id'), nullable=True)

    # بيانات عسكرية
    military_number = db.Column(db.String(20), nullable=True)
    blood_type_id = db.Column(db.Integer, db.ForeignKey('blood_type.id'), nullable=True)
    is_fighter = db.Column(db.Boolean, nullable=True)  # مقاتل سلاح (نعم/لا)
    weapon_ownership = db.Column(db.String(20), nullable=True)  # شخصي/عهدة
    military_rank_id = db.Column(db.Integer, db.ForeignKey('military_rank.id'), nullable=True)
    rank_date = db.Column(db.Date, nullable=True)

    # بيانات صحية
    health_status = db.Column(db.String(50), nullable=True)
    injury_type_id = db.Column(db.Integer, db.ForeignKey('injury_type.id'), nullable=True)
    injury_cause_id = db.Column(db.Integer, db.ForeignKey('injury_cause.id'), nullable=True)
    injury_place = db.Column(db.String(100), nullable=True)
    injury_date = db.Column(db.Date, nullable=True)
    injury_body_location = db.Column(db.String(100), nullable=True)
    injury_disability = db.Column(db.Boolean, nullable=True)  # عجز أثناء الإصابة (نعم/لا)
    injury_hinders_work = db.Column(db.Boolean, nullable=True)  # تعيق عن العمل الحالي (نعم/لا)
    injury_authority = db.Column(db.String(100), nullable=True)  # الجهة التابع لها أثناء الإصابة

    # حذف الحقول الزائدة
    # work_number = db.Column(db.String(20), nullable=True)  # تم حذف الرقم الوظيفي
    # work_rank = db.Column(db.String(100), nullable=True)  # تم حذف الرتبة الوظيفية

    # العلاقات
    issuing_authority = db.relationship('IssuingAuthority', foreign_keys=[issuing_authority_id], backref='personal_data_issued')
    marital_status = db.relationship('MaritalStatus', backref='personal_data')

    # علاقات النسب - تم إزالتها لاستخدام الحقول النصية المباشرة

    # علاقات السكن
    residence_governorate = db.relationship('Governorate', foreign_keys=[residence_governorate_id], backref='residents')
    residence_directorate = db.relationship('Directorate', foreign_keys=[residence_directorate_id], backref='residents')
    residence_village = db.relationship('Village', foreign_keys=[residence_village_id], backref='residents')

    # علاقات العمل
    qualification_type = db.relationship('QualificationType', backref='personal_data')
    specialization = db.relationship('Specialization', backref='personal_data')
    # استخدام جدول Agency بدلاً من WorkPlace
    work_place = db.relationship('Agency', foreign_keys=[work_place_id], backref='employees')
    agency = db.relationship('Agency', foreign_keys=[agency_id], backref='agency_personal_data')
    work_governorate = db.relationship('Governorate', foreign_keys=[work_governorate_id], backref='employees')
    work_directorate = db.relationship('Directorate', foreign_keys=[work_directorate_id], backref='employees')
    work_village = db.relationship('Village', foreign_keys=[work_village_id], backref='employees')

    # علاقات أخرى
    assignment_type = db.relationship('AssignmentType', backref='personal_data')
    assignment_authority = db.relationship('IssuingAuthority', foreign_keys=[assignment_authority_id], backref='personal_data_assigned')
    blood_type = db.relationship('BloodType', backref='personal_data')
    military_rank = db.relationship('MilitaryRank', backref='personal_data')
    injury_type = db.relationship('InjuryType', backref='personal_data')
    injury_cause = db.relationship('InjuryCause', backref='personal_data')

    # الدورات السابقة
    previous_courses = db.relationship('PreviousCourse', backref='personal_data', lazy=True, cascade="all, delete-orphan")

    # ملاحظات
    notes = db.Column(db.Text, nullable=True)

    def __repr__(self):
        return f"PersonalData('{self.full_name}')"

# سيتم تهيئة مسارات بيانات الأشخاص بعد إنشاء db

# الدورات السابقة
class PreviousCourse(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    personal_data_id = db.Column(db.Integer, db.ForeignKey('personal_data.id'), nullable=False)
    course_type_id = db.Column(db.Integer, db.ForeignKey('course_type.id'), nullable=False)
    course_name = db.Column(db.String(100), nullable=False)
    course_place = db.Column(db.String(100), nullable=True)
    course_date = db.Column(db.Date, nullable=True)
    course_duration = db.Column(db.String(50), nullable=True)

    # العلاقات
    course_type = db.relationship('CourseType', backref='previous_courses')

    def __repr__(self):
        return f"PreviousCourse('{self.course_name}', Type: {self.course_type_id})"

# جدول الدفعات
class Batch(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    code = db.Column(db.String(20), nullable=True)
    start_date = db.Column(db.Date, nullable=False)
    end_date = db.Column(db.Date, nullable=False)
    course_id = db.Column(db.Integer, db.ForeignKey('course.id'), nullable=False)
    max_participants = db.Column(db.Integer, nullable=True)
    notes = db.Column(db.Text, nullable=True)

    # العلاقات
    course = db.relationship('Course', backref='batches')
    participants = db.relationship('BatchParticipant', backref='batch', lazy=True, cascade="all, delete-orphan")

    def __repr__(self):
        return f"Batch('{self.name}', Course: {self.course_id})"

# جدول مشاركي الدفعة
class BatchParticipant(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    batch_id = db.Column(db.Integer, db.ForeignKey('batch.id'), nullable=False)
    personal_data_id = db.Column(db.Integer, db.ForeignKey('personal_data.id'), nullable=False)
    registration_date = db.Column(db.DateTime, nullable=False, default=lambda: datetime.now(timezone.utc))
    status = db.Column(db.String(20), nullable=False, default='active')  # active, completed, dropped
    notes = db.Column(db.Text, nullable=True)

    # العلاقات
    personal_data = db.relationship('PersonalData', backref='batch_participations')

    def __repr__(self):
        return f"BatchParticipant(Batch ID: {self.batch_id}, Participant: {self.personal_data.full_name if self.personal_data else 'Unknown'}, Status: {self.status})"

# تعريف نماذج النماذج
class LoginForm(FlaskForm):
    email = StringField('البريد الإلكتروني', validators=[DataRequired(), Email()])
    password = PasswordField('كلمة المرور', validators=[DataRequired()])
    remember = BooleanField('تذكرني')
    submit = SubmitField('تسجيل الدخول')

class CoursePathForm(FlaskForm):
    name = StringField('اسم المسار', validators=[DataRequired(), Length(min=2, max=100)])
    description = TextAreaField('الوصف', validators=[Optional(), Length(max=500)])
    code = StringField('الرمز', validators=[Optional(), Length(max=20)])
    submit = SubmitField('حفظ')

class CoursePathLevelForm(FlaskForm):
    name = StringField('اسم المستوى', validators=[DataRequired(), Length(min=2, max=100)])
    description = TextAreaField('الوصف', validators=[Optional(), Length(max=500)])
    code = StringField('الرمز', validators=[Optional(), Length(max=20)])
    order = IntegerField('الترتيب', validators=[Optional()])
    path_id = SelectField('المسار', coerce=int, validators=[DataRequired()])
    submit = SubmitField('حفظ')

class ForceClassificationForm(FlaskForm):
    name = StringField('تصنيف القوة', validators=[DataRequired(), Length(min=2, max=100)])
    description = TextAreaField('الوصف', validators=[Optional(), Length(max=500)])
    submit = SubmitField('حفظ')

class CourseForm(FlaskForm):
    # رقم الدورة (مدخل من المستخدم)
    course_number = StringField('رقم الدورة العام', validators=[DataRequired(), Length(min=3, max=20)])
    agency_course_number = StringField('رقم الدورة في الجهة', validators=[Optional(), Length(max=20)])
    place_course_number = StringField('رقم الدورة في المكان', validators=[Optional(), Length(max=20)])

    title = StringField('عنوان الدورة', validators=[DataRequired(), Length(min=3, max=100)])
    description = TextAreaField('وصف الدورة', validators=[Optional()])

    # التصنيف والمستوى
    category_id = SelectField('التصنيف', coerce=int, validators=[Optional()])
    level_id = SelectField('المستوى', coerce=int, validators=[Optional()])

    # الصورة والتواريخ
    image = FileField('صورة الدورة', validators=[Optional(), FileAllowed(['jpg', 'png', 'jpeg', 'gif', 'bmp', 'webp'])])
    start_date = DateField('تاريخ البدء (ميلادي)', format='%Y-%m-%d', validators=[Optional()], render_kw={"type": "date"})
    end_date = DateField('تاريخ النهاية (ميلادي)', format='%Y-%m-%d', validators=[Optional()], render_kw={"type": "date"})

    # التاريخ الهجري - بداية الدورة
    start_date_hijri_day = SelectField('اليوم', choices=[(str(i), str(i)) for i in range(1, 31)], validators=[Optional()])
    start_date_hijri_month = SelectField('الشهر', choices=[
        ('01', 'محرم'), ('02', 'صفر'), ('03', 'ربيع الأول'), ('04', 'ربيع الثاني'),
        ('05', 'جمادى الأولى'), ('06', 'جمادى الآخرة'), ('07', 'رجب'), ('08', 'شعبان'),
        ('09', 'رمضان'), ('10', 'شوال'), ('11', 'ذو القعدة'), ('12', 'ذو الحجة')
    ], validators=[Optional()])
    start_date_hijri_year = SelectField('السنة', choices=[(str(i), str(i)) for i in range(1445, 1451)], validators=[Optional()])

    # التاريخ الهجري - نهاية الدورة
    end_date_hijri_day = SelectField('اليوم', choices=[(str(i), str(i)) for i in range(1, 31)], validators=[Optional()])
    end_date_hijri_month = SelectField('الشهر', choices=[
        ('01', 'محرم'), ('02', 'صفر'), ('03', 'ربيع الأول'), ('04', 'ربيع الثاني'),
        ('05', 'جمادى الأولى'), ('06', 'جمادى الآخرة'), ('07', 'رجب'), ('08', 'شعبان'),
        ('09', 'رمضان'), ('10', 'شوال'), ('11', 'ذو القعدة'), ('12', 'ذو الحجة')
    ], validators=[Optional()])
    end_date_hijri_year = SelectField('السنة', choices=[(str(i), str(i)) for i in range(1445, 1451)], validators=[Optional()])

    duration_days = IntegerField('مدة الدورة (بالأيام)', validators=[Optional()], default=1)

    # المسار والمستوى
    path_id = SelectField('المسار', coerce=int, validators=[Optional()])
    path_level_id = SelectField('المستوى في المسار', coerce=int, validators=[Optional()], default=0)

    # حقول إضافية حسب النموذج المرفق
    agency_id = SelectField('الجهة', coerce=int, validators=[Optional()], default=0)
    center_id = SelectField('اسم المركز', coerce=int, validators=[Optional()], default=0)
    place_name = StringField('اسم المكان', validators=[Optional(), Length(max=100)])
    participant_type_id = SelectField('نوع المشاركين', coerce=int, validators=[Optional()], default=0)
    status_id = SelectField('المستوى', coerce=int, validators=[Optional()], default=0)
    entry_date = DateField('تاريخ الدخول (ميلادي)', format='%Y-%m-%d', validators=[Optional()])
    exit_date = DateField('تاريخ الخروج (ميلادي)', format='%Y-%m-%d', validators=[Optional()])

    # التاريخ الهجري - دخول
    entry_date_hijri_day = SelectField('اليوم', choices=[(str(i), str(i)) for i in range(1, 31)], validators=[Optional()])
    entry_date_hijri_month = SelectField('الشهر', choices=[
        ('01', 'محرم'), ('02', 'صفر'), ('03', 'ربيع الأول'), ('04', 'ربيع الثاني'),
        ('05', 'جمادى الأولى'), ('06', 'جمادى الآخرة'), ('07', 'رجب'), ('08', 'شعبان'),
        ('09', 'رمضان'), ('10', 'شوال'), ('11', 'ذو القعدة'), ('12', 'ذو الحجة')
    ], validators=[Optional()])
    entry_date_hijri_year = SelectField('السنة', choices=[(str(i), str(i)) for i in range(1445, 1451)], validators=[Optional()])

    # التاريخ الهجري - خروج
    exit_date_hijri_day = SelectField('اليوم', choices=[(str(i), str(i)) for i in range(1, 31)], validators=[Optional()])
    exit_date_hijri_month = SelectField('الشهر', choices=[
        ('01', 'محرم'), ('02', 'صفر'), ('03', 'ربيع الأول'), ('04', 'ربيع الثاني'),
        ('05', 'جمادى الأولى'), ('06', 'جمادى الآخرة'), ('07', 'رجب'), ('08', 'شعبان'),
        ('09', 'رمضان'), ('10', 'شوال'), ('11', 'ذو القعدة'), ('12', 'ذو الحجة')
    ], validators=[Optional()])
    exit_date_hijri_year = SelectField('السنة', choices=[(str(i), str(i)) for i in range(1445, 1451)], validators=[Optional()])

    force_classification_id = SelectField('تصنيف القوة', coerce=int, validators=[Optional()], default=0)
    target_agency_id = SelectField('الجهة التابعة للمستهدفين', coerce=int, validators=[Optional()], default=0)
    target_count = IntegerField('عدد المستهدفين', validators=[Optional(), NumberRange(min=1)])
    card_type = StringField('نوع البطاقة', validators=[Optional(), Length(max=50)])

    # ملاحظات
    notes = TextAreaField('ملاحظات')

    submit = SubmitField('إضافة الدورة')

class MaterialForm(FlaskForm):
    title = StringField('عنوان المادة', validators=[DataRequired(), Length(min=3, max=100)])
    description = TextAreaField('وصف المادة')
    file = FileField('ملف المادة', validators=[
        FileAllowed(['pdf', 'ppt', 'pptx', 'doc', 'docx', 'xls', 'xlsx', 'jpg', 'png', 'mp4', 'zip']),
        FileSize(max_size=MAX_FILE_SIZE, message=f'حجم الملف يجب أن لا يتجاوز {MAX_FILE_SIZE_READABLE}')
    ])
    day_number = SelectField('اليوم', validators=[DataRequired()])
    submit = SubmitField('إضافة المادة')

class CourseScheduleForm(FlaskForm):
    day_number = SelectField('اليوم', validators=[DataRequired()])
    start_time = StringField('وقت البدء', validators=[DataRequired(), Length(min=5, max=5)], render_kw={"placeholder": "08:00"})
    end_time = StringField('وقت الانتهاء', validators=[DataRequired(), Length(min=5, max=5)], render_kw={"placeholder": "09:00"})
    title = StringField('عنوان الفترة', validators=[DataRequired(), Length(min=3, max=100)])
    description = TextAreaField('وصف الفترة')
    is_break = BooleanField('استراحة')
    material_id = SelectField('المادة المرتبطة', coerce=int, validators=[Optional()])
    submit = SubmitField('إضافة فترة')

# نماذج الجداول الترميزية
class GovernorateForm(FlaskForm):
    name = StringField('اسم المحافظة', validators=[DataRequired(), Length(min=2, max=100)])
    code = StringField('الرمز', validators=[DataRequired(), Length(min=1, max=10)])
    submit = SubmitField('حفظ')

class DirectorateForm(FlaskForm):
    name = StringField('اسم المديرية', validators=[DataRequired(), Length(min=2, max=100)])
    code = StringField('الرمز', validators=[DataRequired(), Length(min=1, max=10)])
    governorate_id = SelectField('المحافظة', coerce=int, validators=[DataRequired()])
    submit = SubmitField('حفظ')

class VillageForm(FlaskForm):
    name = StringField('اسم القرية/الحي', validators=[DataRequired(), Length(min=2, max=100)])
    code = StringField('الرمز', validators=[DataRequired(), Length(min=1, max=10)])
    directorate_id = SelectField('المديرية', coerce=int, validators=[DataRequired()])
    submit = SubmitField('حفظ')

class MaritalStatusForm(FlaskForm):
    name = StringField('اسم الحالة الاجتماعية', validators=[DataRequired(), Length(min=2, max=50)])
    submit = SubmitField('حفظ')

class BloodTypeForm(FlaskForm):
    name = StringField('فصيلة الدم', validators=[DataRequired(), Length(min=1, max=10)])
    submit = SubmitField('حفظ')

class AgencyForm(FlaskForm):
    name = StringField('اسم الجهة', validators=[DataRequired(), Length(min=2, max=100)])
    code = StringField('الرمز', validators=[DataRequired(), Length(min=1, max=10)])
    parent_id = SelectField('الجهة الأم', coerce=int, validators=[Optional()])
    submit = SubmitField('حفظ')

class TrainingCenterTypeForm(FlaskForm):
    name = StringField('نوع المركز', validators=[DataRequired(), Length(min=2, max=100)])
    description = TextAreaField('الوصف', validators=[Optional(), Length(max=500)])
    submit = SubmitField('حفظ')

class LocationForm(FlaskForm):
    name = StringField('اسم الموقع', validators=[DataRequired(), Length(min=2, max=100)])
    address = StringField('العنوان', validators=[Optional(), Length(max=200)])
    governorate_id = SelectField('المحافظة', coerce=int, validators=[Optional()])
    directorate_id = SelectField('المديرية', coerce=int, validators=[Optional()])
    submit = SubmitField('حفظ')

class TrainingCenterForm(FlaskForm):
    name = StringField('اسم المركز التدريبي', validators=[DataRequired(), Length(min=2, max=100)])
    center_type_id = SelectField('نوع المركز', coerce=int, validators=[Optional()])
    location_id = SelectField('الموقع', coerce=int, validators=[Optional()])
    governorate_id = SelectField('المحافظة', coerce=int, validators=[Optional()])
    directorate_id = SelectField('المديرية', coerce=int, validators=[Optional()])
    agency_id = SelectField('الجهة التابع لها', coerce=int, validators=[Optional()])
    capacity = IntegerField('السعة الاستيعابية', validators=[Optional(), NumberRange(min=1, max=1000)])
    is_ready = BooleanField('جاهز للاستخدام')
    not_ready_reason = TextAreaField('سبب عدم الجهوزية', validators=[Optional(), Length(max=500)])
    notes = TextAreaField('ملاحظات', validators=[Optional(), Length(max=500)])
    submit = SubmitField('حفظ')

class ParticipantTypeForm(FlaskForm):
    name = StringField('نوع المشاركين', validators=[DataRequired(), Length(min=2, max=100)])
    submit = SubmitField('حفظ')

class CardTypeForm(FlaskForm):
    name = StringField('نوع البطاقة', validators=[DataRequired(), Length(min=2, max=100)])
    submit = SubmitField('حفظ')

class CourseLevelForm(FlaskForm):
    name = StringField('مستوى الدورة', validators=[DataRequired(), Length(min=2, max=100)])
    submit = SubmitField('حفظ')

class EditMaterialForm(FlaskForm):
    title = StringField('عنوان المادة', validators=[DataRequired(), Length(min=3, max=100)])
    description = TextAreaField('وصف المادة')
    file = FileField('ملف المادة (اختياري - اترك فارغاً للاحتفاظ بالملف الحالي)', validators=[
        FileAllowed(['pdf', 'ppt', 'pptx', 'doc', 'docx', 'xls', 'xlsx', 'jpg', 'png', 'mp4', 'zip']),
        FileSize(max_size=MAX_FILE_SIZE, message=f'حجم الملف يجب أن لا يتجاوز {MAX_FILE_SIZE_READABLE}')
    ])
    day_number = SelectField('اليوم', validators=[DataRequired()])
    submit = SubmitField('تحديث المادة')

# نماذج البيانات الشخصية
class PersonalDataExcelForm(FlaskForm):
    # البيانات الشخصية
    full_name = StringField('الاسم الشخصي', validators=[DataRequired(), Length(min=3, max=100)])
    nickname = StringField('الاسم المستعار', validators=[Length(max=50)])
    age = IntegerField('العمر', validators=[Optional()])
    national_number = StringField('الرقم الوطني', validators=[Length(max=20)])
    military_number = StringField('الرقم العسكري', validators=[Length(max=20)])

    # بيانات النسب
    governorate_id = SelectField('المحافظة', coerce=int, validators=[Optional()])
    directorate_id = SelectField('المديرية', coerce=int, validators=[Optional()])
    uzla = StringField('العزلة', validators=[Length(max=100)])
    village_id = SelectField('الحي/القرية', coerce=int, validators=[Optional()])

    # المؤهل العلمي
    qualification_type_id = SelectField('المؤهل العلمي', coerce=int, validators=[Optional()])
    marital_status_id = SelectField('الحالة الاجتماعية', coerce=int, validators=[Optional()])

    # العمل
    job = StringField('العمل', validators=[Optional(), Length(max=100)])
    agency_id = SelectField('الإدارة', coerce=int, validators=[Optional()])
    work_place_id = SelectField('مكان العمل', coerce=int, validators=[Optional()])

    # أرقام الهواتف
    phone = StringField('رقم الهاتف', validators=[Optional(), Length(max=20)])

    submit = SubmitField('حفظ')

# نموذج البيانات الشخصية الأصلي (للتوافق مع الكود الحالي)
class PersonalDataForm(FlaskForm):
    # البيانات الشخصية
    full_name = StringField('الاسم', validators=[DataRequired(), Length(min=3, max=100)])
    nickname = StringField('الاسم المستعار', validators=[Length(max=50)])
    triple_number = StringField('الرقم الثلاثي', validators=[Length(max=20)])
    national_number = StringField('الرقم الوطني', validators=[Length(max=20)])
    birth_date = DateField('تاريخ الميلاد', format='%Y-%m-%d', validators=[Optional()])
    age = IntegerField('العمر')
    issue_date = DateField('تاريخ الإصدار', format='%Y-%m-%d', validators=[Optional()])
    issuing_authority_id = SelectField('جهة الإصدار', coerce=int)
    children_count = IntegerField('عدد الأولاد')
    marital_status_id = SelectField('الحالة الاجتماعية', coerce=int)

    # حقول جديدة للتوافق مع نموذج الإكسل
    work_place = StringField('مكان العمل', validators=[Length(max=100)])

    # بيانات النسب
    governorate_id = SelectField('المحافظة', coerce=int, validators=[Optional()])
    directorate_id = SelectField('المديرية', coerce=int, validators=[Optional()])
    village_id = SelectField('القرية', coerce=int, validators=[Optional()])

    # بيانات السكن
    residence_governorate_id = SelectField('محافظة السكن', coerce=int, validators=[Optional()])
    residence_directorate_id = SelectField('مديرية السكن', coerce=int, validators=[Optional()])
    residence_village_id = SelectField('قرية السكن', coerce=int, validators=[Optional()])
    residence_house = StringField('المنزل', validators=[Length(max=100)])
    residence_type = StringField('نوع السكن', validators=[Length(max=20)])

    # بيانات الاتصال
    phone_yemen_mobile = StringField('رقم الهاتف (يمن موبايل)', validators=[Length(max=20)])
    phone_you = StringField('رقم الهاتف (يو)', validators=[Length(max=20)])
    phone_sayyon = StringField('رقم الهاتف (سيئون)', validators=[Length(max=20)])
    phone_landline = StringField('رقم الهاتف (أرضي)', validators=[Length(max=20)])

    # العمل الحالي
    work_place_id = SelectField('جهة العمل', coerce=int)
    job_title = StringField('مسمى العمل', validators=[Length(max=100)])
    work_governorate_id = SelectField('محافظة العمل', coerce=int, validators=[Optional()])
    work_directorate_id = SelectField('مديرية العمل', coerce=int, validators=[Optional()])
    work_village_id = SelectField('قرية العمل', coerce=int, validators=[Optional()])
    work_unit = StringField('وحدة العمل', validators=[Length(max=100)])

    # بيانات التكليف
    assignment_type_id = SelectField('نوع التكليف', coerce=int, validators=[Optional()])
    assignment_date = DateField('تاريخ التكليف', format='%Y-%m-%d', validators=[Optional()], default=None)
    assignment_authority_id = SelectField('جهة التكليف', coerce=int, validators=[Optional()])

    # البيانات العسكرية
    military_number = StringField('الرقم العسكري', validators=[Length(max=20)])
    blood_type_id = SelectField('فصيلة الدم', coerce=int, validators=[Optional()])
    is_fighter = BooleanField('مقاتل')
    weapon_ownership = StringField('ملكية السلاح', validators=[Length(max=100)])
    military_rank_id = SelectField('الرتبة العسكرية', coerce=int, validators=[Optional()])
    rank_date = DateField('تاريخ الرتبة', format='%Y-%m-%d', validators=[Optional()], default=None)

    # الحالة الصحية
    health_status = StringField('الحالة الصحية', validators=[Length(max=50)])
    injury_type_id = SelectField('نوع الإصابة', coerce=int, validators=[Optional()])
    injury_cause_id = SelectField('سبب الإصابة', coerce=int, validators=[Optional()])
    injury_place = StringField('مكان الإصابة', validators=[Length(max=100)])
    injury_date = DateField('تاريخ الإصابة', format='%Y-%m-%d', validators=[Optional()], default=None)
    injury_body_location = StringField('موقع الإصابة في الجسم', validators=[Length(max=100)])
    injury_disability = IntegerField('نسبة العجز', validators=[Optional()])
    injury_hinders_work = BooleanField('تعيق العمل')
    injury_authority = StringField('جهة الإصابة', validators=[Length(max=100)])

    # المؤهلات العلمية
    qualification_type_id = SelectField('المؤهل العلمي', coerce=int, validators=[Optional()])
    specialization_id = SelectField('التخصص', coerce=int, validators=[Optional()])
    qualification_place = StringField('مكان المؤهل', validators=[Length(max=100)])
    qualification_date = DateField('تاريخ المؤهل', format='%Y-%m-%d', validators=[Optional()], default=None)

    # ملاحظات
    notes = TextAreaField('ملاحظات', validators=[Length(max=500)])

    # زر الحفظ
    submit = SubmitField('حفظ')

# نموذج إضافة دورة سابقة
class PreviousCourseForm(FlaskForm):
    course_type_id = SelectField('نوع الدورة', coerce=int)
    course_name = StringField('اسم الدورة', validators=[DataRequired(), Length(max=100)])
    course_place = StringField('مكان الدورة', validators=[Length(max=100)])
    course_date = DateField('تاريخ الدورة', format='%Y-%m-%d')
    course_duration = StringField('مدة الدورة', validators=[Length(max=50)])
    submit = SubmitField('إضافة الدورة')

# نماذج الجداول الترميزية
class GovernorateForm(FlaskForm):
    name = StringField('اسم المحافظة', validators=[DataRequired(), Length(max=100)])
    code = StringField('الرمز', validators=[Length(max=10)])
    submit = SubmitField('حفظ')

class DirectorateForm(FlaskForm):
    name = StringField('اسم المديرية', validators=[DataRequired(), Length(max=100)])
    code = StringField('الرمز', validators=[Length(max=10)])
    governorate_id = SelectField('المحافظة', coerce=int, validators=[DataRequired()])
    submit = SubmitField('حفظ')

class VillageForm(FlaskForm):
    name = StringField('اسم الحي/القرية', validators=[DataRequired(), Length(max=100)])
    code = StringField('الرمز', validators=[Length(max=10)])
    directorate_id = SelectField('المديرية', coerce=int, validators=[DataRequired()])
    submit = SubmitField('حفظ')

class MaritalStatusForm(FlaskForm):
    name = StringField('الحالة الاجتماعية', validators=[DataRequired(), Length(max=50)])
    submit = SubmitField('حفظ')

class BloodTypeForm(FlaskForm):
    name = StringField('فصيلة الدم', validators=[DataRequired(), Length(max=10)])
    submit = SubmitField('حفظ')

class IssuingAuthorityForm(FlaskForm):
    name = StringField('اسم الجهة', validators=[DataRequired(), Length(max=100)])
    parent_id = SelectField('الجهة الأم', coerce=int, validators=[Optional()])
    submit = SubmitField('حفظ')

class WorkPlaceForm(FlaskForm):
    name = StringField('اسم جهة العمل', validators=[DataRequired(), Length(max=100)])
    parent_id = SelectField('الجهة الأم', coerce=int, validators=[Optional()])
    submit = SubmitField('حفظ')

class QualificationTypeForm(FlaskForm):
    name = StringField('نوع المؤهل', validators=[DataRequired(), Length(max=100)])
    description = TextAreaField('الوصف', validators=[Optional(), Length(max=500)])
    level = SelectField('المستوى', choices=[
        ('', 'اختر المستوى'),
        ('primary', 'ابتدائي'),
        ('middle', 'متوسط'),
        ('secondary', 'ثانوي'),
        ('diploma', 'دبلوم'),
        ('bachelor', 'بكالوريوس'),
        ('master', 'ماجستير'),
        ('phd', 'دكتوراه'),
        ('other', 'أخرى')
    ], validators=[Optional()])
    code = StringField('الرمز', validators=[Optional(), Length(max=20)])
    submit = SubmitField('حفظ')

class SpecializationForm(FlaskForm):
    name = StringField('التخصص', validators=[DataRequired(), Length(max=100)])
    description = TextAreaField('الوصف', validators=[Optional(), Length(max=500)])
    field = SelectField('المجال', choices=[
        ('', 'اختر المجال'),
        ('medical', 'طبي'),
        ('engineering', 'هندسي'),
    ], validators=[Optional()])
    code = StringField('الرمز', validators=[Optional(), Length(max=20)])
    submit = SubmitField('حفظ')

class AssignmentTypeForm(FlaskForm):
    name = StringField('نوع التكليف', validators=[DataRequired(), Length(max=100)])
    submit = SubmitField('حفظ')

class MilitaryRankForm(FlaskForm):
    name = StringField('الرتبة العسكرية', validators=[DataRequired(), Length(max=100)])
    code = StringField('الرمز', validators=[Optional(), Length(max=10)])
    submit = SubmitField('حفظ')

class InjuryTypeForm(FlaskForm):
    name = StringField('نوع الإصابة', validators=[DataRequired(), Length(max=100)])
    submit = SubmitField('حفظ')

class InjuryCauseForm(FlaskForm):
    name = StringField('سبب الإصابة', validators=[DataRequired(), Length(max=100)])
    submit = SubmitField('حفظ')

class CourseTypeForm(FlaskForm):
    name = StringField('نوع الدورة', validators=[DataRequired(), Length(max=100)])
    category = SelectField('التصنيف', choices=[
        ('administrative', 'إداري'),
        ('educational', 'تعليمي'),
        ('technical', 'تقني'),
        ('security', 'أمني'),
        ('military', 'عسكري'),
        ('other', 'أخرى')
    ], validators=[Optional()])
    code = StringField('الرمز', validators=[Optional(), Length(max=20)])
    submit = SubmitField('حفظ')

class AssignmentTypeForm(FlaskForm):
    name = StringField('نوع التكليف', validators=[DataRequired(), Length(max=100)])
    submit = SubmitField('حفظ')

class MilitaryRankForm(FlaskForm):
    name = StringField('الرتبة', validators=[DataRequired(), Length(max=100)])
    code = StringField('الرمز', validators=[Length(max=10)])
    submit = SubmitField('حفظ')

class InjuryTypeForm(FlaskForm):
    name = StringField('نوع الإصابة', validators=[DataRequired(), Length(max=100)])
    submit = SubmitField('حفظ')

class InjuryCauseForm(FlaskForm):
    name = StringField('سبب الإصابة', validators=[DataRequired(), Length(max=100)])
    submit = SubmitField('حفظ')

class BackupForm(FlaskForm):
    backup_path = StringField('مسار الحفظ', validators=[Optional(), Length(max=255)])
    backup_name = StringField('اسم الملف', validators=[Optional(), Length(max=100)])
    include_personal_data = BooleanField('تضمين البيانات الشخصية', default=True)
    include_courses = BooleanField('تضمين بيانات الدورات', default=True)
    include_uploads = BooleanField('تضمين الملفات المرفقة', default=True)
    submit = SubmitField('إنشاء نسخة احتياطية')

class NameCorrectionForm(FlaskForm):
    wrong_name = StringField('الاسم الخطأ', validators=[DataRequired(), Length(max=200)],
                            render_kw={"placeholder": "مثال: عيسي عبداللة"})
    correct_name = StringField('الاسم الصحيح', validators=[DataRequired(), Length(max=200)],
                              render_kw={"placeholder": "مثال: عيسى عبدالله"})
    correction_type = SelectField('نوع التصحيح', choices=[
        ('', 'اختر نوع التصحيح'),
        ('hamza', 'تصحيح الهمزات'),
        ('alif_maqsura', 'تصحيح الألف المقصورة'),
        ('compound_names', 'الأسماء المركبة'),
        ('titles', 'الألقاب والكنى'),
        ('symbols', 'إزالة الرموز والأرقام'),
        ('other', 'أخرى')
    ], validators=[DataRequired()])
    submit = SubmitField('إضافة التصحيح')

class CourseTypeForm(FlaskForm):
    name = StringField('نوع الدورة', validators=[DataRequired(), Length(max=100)])
    category = SelectField('التصنيف', choices=[
        ('فنية', 'فنية'),
        ('قيادية', 'قيادية'),
        ('أمنية', 'أمنية'),
        ('أخرى', 'أخرى')
    ], validators=[DataRequired()])
    submit = SubmitField('حفظ')

# نموذج المشاركين في الدورة
class CourseParticipantForm(FlaskForm):
    personal_data_id = SelectField('المشارك', coerce=int, validators=[DataRequired()])
    entry_date = DateField('تاريخ الدخول', format='%Y-%m-%d')
    status = SelectField('الحالة', choices=[
        ('active', 'نشط'),
        ('completed', 'أكمل الدورة'),
        ('dropped', 'انسحب')
    ], validators=[DataRequired()])

    # ملاحظات
    notes = TextAreaField('ملاحظات')

    submit = SubmitField('حفظ')

# نماذج الجداول الترميزية الجديدة

class AgencyForm(FlaskForm):
    name = StringField('اسم الجهة', validators=[DataRequired(), Length(max=100)])
    code = StringField('الرمز', validators=[Length(max=20)])
    parent_id = SelectField('الجهة الأم', coerce=int, validators=[Optional()])
    submit = SubmitField('حفظ')

class ParticipantTypeForm(FlaskForm):
    name = StringField('نوع المشاركين', validators=[DataRequired(), Length(max=100)])
    submit = SubmitField('حفظ')

# نموذج الدفعة
class BatchForm(FlaskForm):
    name = StringField('اسم الدفعة', validators=[DataRequired(), Length(min=2, max=100)])
    code = StringField('رمز الدفعة', validators=[Length(max=20)])
    start_date = DateField('تاريخ البداية', validators=[DataRequired()])
    end_date = DateField('تاريخ النهاية', validators=[DataRequired()])
    course_id = SelectField('الدورة', coerce=int, validators=[DataRequired()])
    max_participants = IntegerField('الحد الأقصى للمشاركين', validators=[Optional(), NumberRange(min=1)])
    notes = TextAreaField('ملاحظات', validators=[Optional(), Length(max=500)])
    submit = SubmitField('حفظ')

class CardTypeForm(FlaskForm):
    name = StringField('نوع البطاقة', validators=[DataRequired(), Length(max=100)])
    submit = SubmitField('حفظ')

class CourseLevelForm(FlaskForm):
    name = StringField('المستوى', validators=[DataRequired(), Length(max=100)])
    submit = SubmitField('حفظ')

class CourseCategoryForm(FlaskForm):
    name = StringField('التصنيف', validators=[DataRequired(), Length(max=100)])
    code = StringField('الرمز', validators=[Optional(), Length(max=20)])
    submit = SubmitField('حفظ')

class DepartmentForm(FlaskForm):
    name = StringField('اسم القسم', validators=[DataRequired(), Length(max=100)])
    code = StringField('الرمز', validators=[Length(max=20)])
    parent_id = SelectField('القسم الأب', coerce=int, validators=[Optional()])
    agency_id = SelectField('الجهة', coerce=int, validators=[Optional()])
    submit = SubmitField('حفظ')

class PaymentMethodForm(FlaskForm):
    name = StringField('طريقة الدفع', validators=[DataRequired(), Length(max=100)])
    submit = SubmitField('حفظ')

class CourseSearchForm(FlaskForm):
    title = StringField('عنوان الدورة', validators=[Optional()])
    category_id = SelectField('التصنيف', coerce=int, validators=[Optional()])
    level_id = SelectField('المستوى', coerce=int, validators=[Optional()])

    # خيار استخدام التاريخ الهجري أو الميلادي
    use_hijri_dates = BooleanField('استخدام التاريخ الهجري', default=False)

    # التاريخ الميلادي
    start_date = DateField('تاريخ البدء (ميلادي)', format='%Y-%m-%d', validators=[Optional()], render_kw={"type": "date"})
    end_date = DateField('تاريخ النهاية (ميلادي)', format='%Y-%m-%d', validators=[Optional()], render_kw={"type": "date"})

    # التاريخ الهجري - بداية البحث
    start_date_hijri_day = SelectField('اليوم', choices=[('', 'اليوم')] + [(str(i), str(i)) for i in range(1, 31)], validators=[Optional()])
    start_date_hijri_month = SelectField('الشهر', choices=[
        ('', 'الشهر'),
        ('01', 'محرم'), ('02', 'صفر'), ('03', 'ربيع الأول'), ('04', 'ربيع الثاني'),
        ('05', 'جمادى الأولى'), ('06', 'جمادى الآخرة'), ('07', 'رجب'), ('08', 'شعبان'),
        ('09', 'رمضان'), ('10', 'شوال'), ('11', 'ذو القعدة'), ('12', 'ذو الحجة')
    ], validators=[Optional()])
    start_date_hijri_year = SelectField('السنة', choices=[('', 'السنة')] + [(str(i), str(i)) for i in range(1445, 1451)], validators=[Optional()])

    # التاريخ الهجري - نهاية البحث
    end_date_hijri_day = SelectField('اليوم', choices=[('', 'اليوم')] + [(str(i), str(i)) for i in range(1, 31)], validators=[Optional()])
    end_date_hijri_month = SelectField('الشهر', choices=[
        ('', 'الشهر'),
        ('01', 'محرم'), ('02', 'صفر'), ('03', 'ربيع الأول'), ('04', 'ربيع الثاني'),
        ('05', 'جمادى الأولى'), ('06', 'جمادى الآخرة'), ('07', 'رجب'), ('08', 'شعبان'),
        ('09', 'رمضان'), ('10', 'شوال'), ('11', 'ذو القعدة'), ('12', 'ذو الحجة')
    ], validators=[Optional()])
    end_date_hijri_year = SelectField('السنة', choices=[('', 'السنة')] + [(str(i), str(i)) for i in range(1445, 1451)], validators=[Optional()])

    trainer_id = SelectField('المدرب', coerce=int, validators=[Optional()])
    submit = SubmitField('بحث')

@login_manager.user_loader
def load_user(user_id):
    # استخدام Session.get بدلاً من Query.get (للتوافق مع SQLAlchemy 2.0)
    return db.session.get(User, int(user_id))

# تهيئة مسارات بيانات الأشخاص بعد إنشاء db
with app.app_context():
    # إنشاء الجداول إذا لم تكن موجودة
    db.create_all()
    # تمرير النماذج مباشرة لتجنب الاستيراد الدائري
    init_person_data_routes(db, PersonData, app, Course, CourseParticipant)

# تعريف المسارات
@app.route('/')
@app.route('/home')
def home():
    if current_user.is_authenticated:
        return render_template('home_new.html', title='الصفحة الرئيسية')
    return render_template('index.html', title='الصفحة الرئيسية')

@app.route('/login', methods=['GET', 'POST'])
def login():
    if current_user.is_authenticated:
        return redirect(url_for('dashboard'))
    form = LoginForm()
    if form.validate_on_submit():
        user = User.query.filter_by(email=form.email.data).first()
        if user and check_password_hash(user.password, form.password.data):
            login_user(user, remember=form.remember.data)
            next_page = request.args.get('next')
            return redirect(next_page) if next_page else redirect(url_for('dashboard'))
        else:
            flash('فشل تسجيل الدخول. يرجى التحقق من البريد الإلكتروني وكلمة المرور', 'danger')
    return render_template('login.html', title='تسجيل الدخول', form=form)

@app.route('/logout')
def logout():
    logout_user()
    return redirect(url_for('home'))

@app.route('/dashboard')
@login_required
def dashboard():
    # جلب إحصائيات سريعة
    try:
        courses_count = Course.query.count()
        persons_count = PersonData.query.count()
        participants_count = CourseParticipant.query.count()

        print(f"📊 إحصائيات لوحة التحكم:")
        print(f"   - الدورات: {courses_count}")
        print(f"   - الأشخاص: {persons_count}")
        print(f"   - المشاركين: {participants_count}")

        return render_template('dashboard.html',
                             title='لوحة التحكم',
                             courses_count=courses_count,
                             persons_count=persons_count,
                             participants_count=participants_count)
    except Exception as e:
        print(f"❌ خطأ في جلب إحصائيات لوحة التحكم: {e}")
        return render_template('dashboard.html', title='لوحة التحكم')

@app.route('/reference_tables')
@login_required
def reference_tables():
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    return render_template('reference_tables/index.html', title='الجداول الترميزية')

# مسارات المسارات التدريبية
@app.route('/course_paths')
@login_required
def course_paths():
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # جلب جميع المسارات
    paths = CoursePath.query.all()
    return render_template('reference_tables/course_paths.html', title='مسارات التدريب', paths=paths)

@app.route('/course_paths/add', methods=['GET', 'POST'])
@login_required
def add_course_path():
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    form = CoursePathForm()

    if form.validate_on_submit():
        # التحقق من عدم وجود مسار بنفس الاسم
        existing_path = CoursePath.query.filter_by(name=form.name.data).first()
        if existing_path:
            flash(f'المسار "{form.name.data}" موجود بالفعل!', 'danger')
            return redirect(url_for('course_paths'))

        # إنشاء مسار جديد
        path = CoursePath(
            name=form.name.data,
            description=form.description.data,
            code=form.code.data
        )

        db.session.add(path)
        db.session.commit()

        flash('تم إضافة المسار بنجاح!', 'success')
        return redirect(url_for('course_paths'))

    return render_template('reference_tables/add_course_path.html', title='إضافة مسار', form=form)

@app.route('/course_paths/<int:path_id>/edit', methods=['GET', 'POST'])
@login_required
def edit_course_path(path_id):
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # جلب المسار
    path = CoursePath.query.get_or_404(path_id)

    form = CoursePathForm()

    if form.validate_on_submit():
        # تحديث المسار
        path.name = form.name.data
        path.description = form.description.data
        path.code = form.code.data

        db.session.commit()

        flash('تم تحديث المسار بنجاح!', 'success')
        return redirect(url_for('course_paths'))

    # ملء النموذج ببيانات المسار الحالي
    elif request.method == 'GET':
        form.name.data = path.name
        form.description.data = path.description
        form.code.data = path.code

    return render_template('reference_tables/edit_course_path.html', title='تعديل مسار', form=form, path=path)

@app.route('/course_paths/<int:path_id>/delete')
@login_required
def delete_course_path(path_id):
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # جلب المسار
    path = CoursePath.query.get_or_404(path_id)

    # التحقق من عدم وجود مستويات مرتبطة بالمسار
    if CoursePathLevel.query.filter_by(path_id=path_id).first():
        flash('لا يمكن حذف المسار لأنه يحتوي على مستويات!', 'danger')
        return redirect(url_for('course_paths'))

    # التحقق من عدم وجود دورات مرتبطة بالمسار
    if Course.query.filter_by(path_id=path_id).first():
        flash('لا يمكن حذف المسار لأنه مرتبط بدورات!', 'danger')
        return redirect(url_for('course_paths'))

    # حذف المسار
    db.session.delete(path)
    db.session.commit()

    flash('تم حذف المسار بنجاح!', 'success')
    return redirect(url_for('course_paths'))

# مسارات مستويات المسارات
@app.route('/course_path_levels')
@login_required
def course_path_levels():
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # جلب جميع المستويات
    levels = CoursePathLevel.query.all()
    return render_template('reference_tables/course_path_levels.html', title='مستويات المسارات', levels=levels)

@app.route('/course_path_levels/add', methods=['GET', 'POST'])
@login_required
def add_course_path_level():
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    form = CoursePathLevelForm()

    # تحديث خيارات المسارات
    form.path_id.choices = [(p.id, p.name) for p in CoursePath.query.all()]

    # تحديد المسار المحدد من معلمات URL إذا كانت موجودة
    if request.method == 'GET' and request.args.get('path_id'):
        form.path_id.data = int(request.args.get('path_id'))

    # تحديد صفحة إعادة التوجيه
    redirect_to = request.args.get('redirect_to', 'course_path_levels')

    if form.validate_on_submit():
        # التحقق من عدم وجود مستوى بنفس الاسم في نفس المسار
        existing_level = CoursePathLevel.query.filter_by(name=form.name.data, path_id=form.path_id.data).first()
        if existing_level:
            flash(f'المستوى "{form.name.data}" موجود بالفعل في هذا المسار!', 'danger')
            if redirect_to == 'tree':
                return redirect(url_for('course_paths_tree'))
            else:
                return redirect(url_for('course_path_levels'))

        # إنشاء مستوى جديد
        level = CoursePathLevel(
            name=form.name.data,
            description=form.description.data,
            code=form.code.data,
            order=form.order.data,
            path_id=form.path_id.data
        )

        db.session.add(level)
        db.session.commit()

        flash('تم إضافة المستوى بنجاح!', 'success')

        # إعادة التوجيه بناءً على معلمة redirect_to
        if redirect_to == 'tree':
            return redirect(url_for('course_paths_tree'))
        else:
            return redirect(url_for('course_path_levels'))

    return render_template('reference_tables/add_course_path_level.html', title='إضافة مستوى', form=form, redirect_to=redirect_to)

@app.route('/course_path_levels/<int:level_id>/edit', methods=['GET', 'POST'])
@login_required
def edit_course_path_level(level_id):
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # جلب المستوى
    level = CoursePathLevel.query.get_or_404(level_id)

    form = CoursePathLevelForm()

    # تحديث خيارات المسارات
    form.path_id.choices = [(p.id, p.name) for p in CoursePath.query.all()]

    # تحديد صفحة إعادة التوجيه
    redirect_to = request.args.get('redirect_to', 'course_path_levels')

    if form.validate_on_submit():
        # تحديث المستوى
        level.name = form.name.data
        level.description = form.description.data
        level.code = form.code.data
        level.order = form.order.data
        level.path_id = form.path_id.data

        db.session.commit()

        flash('تم تحديث المستوى بنجاح!', 'success')

        # إعادة التوجيه بناءً على معلمة redirect_to
        if redirect_to == 'tree':
            return redirect(url_for('course_paths_tree'))
        else:
            return redirect(url_for('course_path_levels'))

    # ملء النموذج ببيانات المستوى الحالي
    elif request.method == 'GET':
        form.name.data = level.name
        form.description.data = level.description
        form.code.data = level.code
        form.order.data = level.order
        form.path_id.data = level.path_id

    return render_template('reference_tables/edit_course_path_level.html', title='تعديل مستوى', form=form, level=level, redirect_to=redirect_to)

@app.route('/course_path_levels/<int:level_id>/delete')
@login_required
def delete_course_path_level(level_id):
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # جلب المستوى
    level = CoursePathLevel.query.get_or_404(level_id)

    # التحقق من عدم وجود دورات مرتبطة بالمستوى
    if Course.query.filter_by(path_level_id=level_id).first():
        flash('لا يمكن حذف المستوى لأنه مرتبط بدورات!', 'danger')
        return redirect(url_for('course_path_levels'))

    # حذف المستوى
    db.session.delete(level)
    db.session.commit()

    flash('تم حذف المستوى بنجاح!', 'success')
    return redirect(url_for('course_path_levels'))

# مسارات أنواع المراكز التدريبية
@app.route('/training_center_types')
@login_required
def training_center_types():
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # جلب جميع أنواع المراكز التدريبية
    types = TrainingCenterType.query.all()
    return render_template('reference_tables/training_center_types.html', title='أنواع المراكز التدريبية', types=types)

@app.route('/training_center_types/add', methods=['GET', 'POST'])
@login_required
def add_training_center_type():
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    form = TrainingCenterTypeForm()

    if form.validate_on_submit():
        # إنشاء نوع مركز تدريبي جديد
        center_type = TrainingCenterType(
            name=form.name.data,
            description=form.description.data
        )

        db.session.add(center_type)
        db.session.commit()

        flash('تم إضافة نوع المركز التدريبي بنجاح!', 'success')
        return redirect(url_for('training_center_types'))

    return render_template('reference_tables/add_training_center_type.html', title='إضافة نوع مركز تدريبي', form=form)

@app.route('/training_center_types/<int:type_id>/edit', methods=['GET', 'POST'])
@login_required
def edit_training_center_type(type_id):
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # جلب نوع المركز التدريبي
    center_type = TrainingCenterType.query.get_or_404(type_id)

    form = TrainingCenterTypeForm()

    if form.validate_on_submit():
        # تحديث بيانات نوع المركز التدريبي
        center_type.name = form.name.data
        center_type.description = form.description.data

        db.session.commit()

        flash('تم تحديث نوع المركز التدريبي بنجاح!', 'success')
        return redirect(url_for('training_center_types'))

    # ملء النموذج ببيانات نوع المركز التدريبي الحالي
    elif request.method == 'GET':
        form.name.data = center_type.name
        form.description.data = center_type.description

    return render_template('reference_tables/edit_training_center_type.html', title='تعديل نوع مركز تدريبي', form=form, center_type=center_type)

@app.route('/training_center_types/<int:type_id>/delete')
@login_required
def delete_training_center_type(type_id):
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # جلب نوع المركز التدريبي
    center_type = TrainingCenterType.query.get_or_404(type_id)

    # التحقق من عدم وجود مراكز تدريبية مرتبطة بهذا النوع
    if TrainingCenter.query.filter_by(center_type_id=type_id).first():
        flash('لا يمكن حذف نوع المركز التدريبي لأنه مرتبط بمراكز تدريبية!', 'danger')
        return redirect(url_for('training_center_types'))

    # حذف نوع المركز التدريبي
    db.session.delete(center_type)
    db.session.commit()

    flash('تم حذف نوع المركز التدريبي بنجاح!', 'success')
    return redirect(url_for('training_center_types'))

# مسارات المواقع
@app.route('/locations')
@login_required
def locations():
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # جلب جميع المواقع
    locations = Location.query.all()
    return render_template('reference_tables/locations.html', title='المواقع', locations=locations)

@app.route('/locations/add', methods=['GET', 'POST'])
@login_required
def add_location():
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    form = LocationForm()

    # تحديث خيارات القوائم المنسدلة
    form.governorate_id.choices = [(0, 'اختر المحافظة')] + [(g.id, g.name) for g in Governorate.query.all()]
    form.directorate_id.choices = [(0, 'اختر المديرية')] + [(d.id, d.name) for d in Directorate.query.all()]

    if form.validate_on_submit():
        # إنشاء موقع جديد
        location = Location(
            name=form.name.data,
            address=form.address.data,
            governorate_id=form.governorate_id.data if form.governorate_id.data != 0 else None,
            directorate_id=form.directorate_id.data if form.directorate_id.data != 0 else None
        )

        db.session.add(location)
        db.session.commit()

        flash('تم إضافة الموقع بنجاح!', 'success')
        return redirect(url_for('locations'))

    return render_template('reference_tables/add_location.html', title='إضافة موقع', form=form)

@app.route('/locations/<int:location_id>/edit', methods=['GET', 'POST'])
@login_required
def edit_location(location_id):
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # جلب الموقع
    location = Location.query.get_or_404(location_id)

    form = LocationForm()

    # تحديث خيارات القوائم المنسدلة
    form.governorate_id.choices = [(0, 'اختر المحافظة')] + [(g.id, g.name) for g in Governorate.query.all()]
    form.directorate_id.choices = [(0, 'اختر المديرية')] + [(d.id, d.name) for d in Directorate.query.all()]

    if form.validate_on_submit():
        # تحديث بيانات الموقع
        location.name = form.name.data
        location.address = form.address.data
        location.governorate_id = form.governorate_id.data if form.governorate_id.data != 0 else None
        location.directorate_id = form.directorate_id.data if form.directorate_id.data != 0 else None

        db.session.commit()

        flash('تم تحديث الموقع بنجاح!', 'success')
        return redirect(url_for('locations'))

    # ملء النموذج ببيانات الموقع الحالي
    elif request.method == 'GET':
        form.name.data = location.name
        form.address.data = location.address
        form.governorate_id.data = location.governorate_id if location.governorate_id else 0
        form.directorate_id.data = location.directorate_id if location.directorate_id else 0

    return render_template('reference_tables/edit_location.html', title='تعديل موقع', form=form, location=location)

@app.route('/locations/<int:location_id>/delete')
@login_required
def delete_location(location_id):
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # جلب الموقع
    location = Location.query.get_or_404(location_id)

    # التحقق من عدم وجود مراكز تدريبية مرتبطة بهذا الموقع
    if TrainingCenter.query.filter_by(location_id=location_id).first():
        flash('لا يمكن حذف الموقع لأنه مرتبط بمراكز تدريبية!', 'danger')
        return redirect(url_for('locations'))

    # حذف الموقع
    db.session.delete(location)
    db.session.commit()

    flash('تم حذف الموقع بنجاح!', 'success')
    return redirect(url_for('locations'))

@app.route('/course_paths/tree')
@login_required
def course_paths_tree():
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # جلب جميع مسارات التدريب مع مستوياتها
    paths = CoursePath.query.all()

    # تنظيم البيانات لعرضها في الشجرة
    paths_data = []
    for path in paths:
        # جلب مستويات المسار مرتبة حسب الترتيب
        levels = CoursePathLevel.query.filter_by(path_id=path.id).order_by(CoursePathLevel.order).all()

        # جلب عدد الدورات وعدد المستهدفين لكل مستوى
        levels_data = []
        for level in levels:
            # جلب الدورات المرتبطة بهذا المستوى
            courses = Course.query.filter_by(path_level_id=level.id).all()
            courses_count = len(courses)

            # حساب إجمالي عدد المستهدفين
            total_target_count = 0
            for course in courses:
                if course.target_count:
                    total_target_count += course.target_count

            levels_data.append({
                'id': level.id,
                'name': level.name,
                'code': level.code,
                'order': level.order,
                'description': level.description,
                'courses_count': courses_count,
                'target_count': total_target_count
            })

        # إضافة المسار مع مستوياته
        paths_data.append({
            'id': path.id,
            'name': path.name,
            'code': path.code,
            'description': path.description,
            'levels': levels_data,
            'levels_count': len(levels_data),
            'total_courses': sum(level['courses_count'] for level in levels_data),
            'total_targets': sum(level['target_count'] for level in levels_data)
        })

    return render_template('reference_tables/course_paths_tree.html', title='شجرة مسارات التدريب', paths=paths_data)

# مسارات تصنيفات القوة
@app.route('/force_classifications')
@login_required
def force_classifications():
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # جلب جميع تصنيفات القوة
    classifications = ForceClassification.query.all()
    return render_template('reference_tables/force_classifications.html', title='تصنيفات القوة', classifications=classifications)

@app.route('/force_classifications/add', methods=['GET', 'POST'])
@login_required
def add_force_classification():
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    form = ForceClassificationForm()

    if form.validate_on_submit():
        # التحقق من عدم وجود تصنيف بنفس الاسم
        existing_classification = ForceClassification.query.filter_by(name=form.name.data).first()
        if existing_classification:
            flash(f'تصنيف القوة "{form.name.data}" موجود بالفعل!', 'danger')
            return redirect(url_for('force_classifications'))

        # إنشاء تصنيف جديد
        classification = ForceClassification(
            name=form.name.data,
            description=form.description.data
        )

        db.session.add(classification)
        db.session.commit()

        flash('تم إضافة تصنيف القوة بنجاح!', 'success')
        return redirect(url_for('force_classifications'))

    return render_template('reference_tables/add_force_classification.html', title='إضافة تصنيف قوة', form=form)

@app.route('/force_classifications/<int:classification_id>/edit', methods=['GET', 'POST'])
@login_required
def edit_force_classification(classification_id):
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # جلب التصنيف
    classification = ForceClassification.query.get_or_404(classification_id)

    form = ForceClassificationForm()

    if form.validate_on_submit():
        # تحديث التصنيف
        classification.name = form.name.data
        classification.description = form.description.data

        db.session.commit()

        flash('تم تحديث تصنيف القوة بنجاح!', 'success')
        return redirect(url_for('force_classifications'))

    # ملء النموذج ببيانات التصنيف الحالي
    elif request.method == 'GET':
        form.name.data = classification.name
        form.description.data = classification.description

    return render_template('reference_tables/edit_force_classification.html', title='تعديل تصنيف قوة', form=form, classification=classification)

@app.route('/force_classifications/<int:classification_id>/delete')
@login_required
def delete_force_classification(classification_id):
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # جلب التصنيف
    classification = ForceClassification.query.get_or_404(classification_id)

    # التحقق من عدم وجود دورات مرتبطة بالتصنيف
    if Course.query.filter_by(force_classification_id=classification_id).first():
        flash('لا يمكن حذف تصنيف القوة لأنه مرتبط بدورات!', 'danger')
        return redirect(url_for('force_classifications'))

    # حذف التصنيف
    db.session.delete(classification)
    db.session.commit()

    flash('تم حذف تصنيف القوة بنجاح!', 'success')
    return redirect(url_for('force_classifications'))

@app.route('/qualification_types')
@login_required
def qualification_types():
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # جلب جميع أنواع المؤهلات
    qualification_types = QualificationType.query.all()
    return render_template('reference_tables/qualification_types.html', title='أنواع المؤهلات', qualification_types=qualification_types)

@app.route('/qualification_types/add', methods=['GET', 'POST'])
@login_required
def add_qualification_type():
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    form = QualificationTypeForm()

    if form.validate_on_submit():
        # التحقق من عدم وجود نوع مؤهل بنفس الاسم
        existing_type = QualificationType.query.filter_by(name=form.name.data).first()
        if existing_type:
            flash(f'نوع المؤهل "{form.name.data}" موجود بالفعل!', 'danger')
            return redirect(url_for('qualification_types'))

        # إنشاء نوع مؤهل جديد
        qualification_type = QualificationType(
            name=form.name.data,
            description=form.description.data,
            level=form.level.data,
            code=form.code.data
        )

        db.session.add(qualification_type)
        db.session.commit()

        flash('تم إضافة نوع المؤهل بنجاح!', 'success')
        return redirect(url_for('qualification_types'))

    return render_template('reference_tables/add_qualification_type.html', title='إضافة نوع مؤهل', form=form)

@app.route('/qualification_types/<int:qualification_type_id>/edit', methods=['GET', 'POST'])
@login_required
def edit_qualification_type(qualification_type_id):
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # جلب نوع المؤهل
    qualification_type = QualificationType.query.get_or_404(qualification_type_id)

    form = QualificationTypeForm()

    if form.validate_on_submit():
        # تحديث نوع المؤهل
        qualification_type.name = form.name.data
        qualification_type.description = form.description.data
        qualification_type.level = form.level.data
        qualification_type.code = form.code.data

        db.session.commit()

        flash('تم تحديث نوع المؤهل بنجاح!', 'success')
        return redirect(url_for('qualification_types'))

    # ملء النموذج ببيانات نوع المؤهل الحالي
    elif request.method == 'GET':
        form.name.data = qualification_type.name
        form.description.data = qualification_type.description
        form.level.data = qualification_type.level
        form.code.data = qualification_type.code

    return render_template('reference_tables/edit_qualification_type.html', title='تعديل نوع مؤهل', form=form, qualification_type=qualification_type)

@app.route('/qualification_types/<int:qualification_type_id>/delete')
@login_required
def delete_qualification_type(qualification_type_id):
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # جلب نوع المؤهل
    qualification_type = QualificationType.query.get_or_404(qualification_type_id)

    # التحقق من عدم وجود بيانات شخصية مرتبطة بنوع المؤهل
    if PersonalData.query.filter_by(qualification_type_id=qualification_type_id).first():
        flash('لا يمكن حذف نوع المؤهل لأنه مرتبط ببيانات شخصية!', 'danger')
        return redirect(url_for('qualification_types'))

    # حذف نوع المؤهل
    db.session.delete(qualification_type)
    db.session.commit()

    flash('تم حذف نوع المؤهل بنجاح!', 'success')
    return redirect(url_for('qualification_types'))

@app.route('/specializations')
@login_required
def specializations():
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # جلب جميع التخصصات
    specializations = Specialization.query.all()
    return render_template('reference_tables/specializations.html', title='التخصصات', specializations=specializations)

@app.route('/specializations/add', methods=['GET', 'POST'])
@login_required
def add_specialization():
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    form = SpecializationForm()

    if form.validate_on_submit():
        # التحقق من عدم وجود تخصص بنفس الاسم
        existing_specialization = Specialization.query.filter_by(name=form.name.data).first()
        if existing_specialization:
            flash(f'التخصص "{form.name.data}" موجود بالفعل!', 'danger')
            return redirect(url_for('specializations'))

        # إنشاء تخصص جديد
        specialization = Specialization(
            name=form.name.data,
            description=form.description.data,
            field=form.field.data,
            code=form.code.data
        )

        db.session.add(specialization)
        db.session.commit()

        flash('تم إضافة التخصص بنجاح!', 'success')
        return redirect(url_for('specializations'))

    return render_template('reference_tables/add_specialization.html', title='إضافة تخصص', form=form)

@app.route('/specializations/<int:specialization_id>/edit', methods=['GET', 'POST'])
@login_required
def edit_specialization(specialization_id):
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # جلب التخصص
    specialization = Specialization.query.get_or_404(specialization_id)

    form = SpecializationForm()

    if form.validate_on_submit():
        # تحديث التخصص
        specialization.name = form.name.data
        specialization.description = form.description.data
        specialization.field = form.field.data
        specialization.code = form.code.data

        db.session.commit()

        flash('تم تحديث التخصص بنجاح!', 'success')
        return redirect(url_for('specializations'))

    # ملء النموذج ببيانات التخصص الحالي
    elif request.method == 'GET':
        form.name.data = specialization.name
        form.description.data = specialization.description
        form.field.data = specialization.field
        form.code.data = specialization.code

    return render_template('reference_tables/edit_specialization.html', title='تعديل تخصص', form=form, specialization=specialization)

@app.route('/specializations/<int:specialization_id>/delete')
@login_required
def delete_specialization(specialization_id):
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # جلب التخصص
    specialization = Specialization.query.get_or_404(specialization_id)

    # التحقق من عدم وجود بيانات شخصية مرتبطة بالتخصص
    if PersonalData.query.filter_by(specialization_id=specialization_id).first():
        flash('لا يمكن حذف التخصص لأنه مرتبط ببيانات شخصية!', 'danger')
        return redirect(url_for('specializations'))

    # حذف التخصص
    db.session.delete(specialization)
    db.session.commit()

    flash('تم حذف التخصص بنجاح!', 'success')
    return redirect(url_for('specializations'))

@app.route('/courses')
@login_required
def courses():
    # جلب جميع الدورات التدريبية
    all_courses = Course.query.all()

    # إضافة تشخيص للمساعدة في حل المشكلة
    print(f"🔍 عدد الدورات المجلبة: {len(all_courses)}")
    for course in all_courses[:5]:  # عرض أول 5 دورات فقط
        print(f"   - {course.course_number}: {course.title}")

    return render_template('courses.html', title='الدورات التدريبية', courses=all_courses)

@app.route('/persons')
@login_required
def persons():
    """عرض قائمة الأشخاص من جدول person_data"""
    try:
        # معاملات البحث والتصفية
        search = request.args.get('search', '')
        page = request.args.get('page', 1, type=int)
        per_page = 50

        # بناء الاستعلام
        query = PersonData.query

        if search:
            query = query.filter(
                db.or_(
                    PersonData.full_name.contains(search),
                    PersonData.phone.contains(search),
                    PersonData.national_number.contains(search),
                    PersonData.governorate.contains(search)
                )
            )

        # تطبيق التصفح
        persons = query.paginate(
            page=page,
            per_page=per_page,
            error_out=False
        )

        print(f"🔍 تم جلب {persons.total} شخص من جدول person_data")
        if persons.items:
            print(f"   - أول شخص: {persons.items[0].full_name}")

        return render_template('person_data_table_new.html',
                             title='قائمة الأشخاص',
                             persons=persons,
                             search=search)
    except Exception as e:
        print(f"❌ خطأ في جلب بيانات الأشخاص: {str(e)}")
        flash(f'حدث خطأ في جلب البيانات: {str(e)}', 'danger')
        return redirect(url_for('dashboard'))

@app.route('/search_courses', methods=['GET', 'POST'])
@login_required
def search_courses():
    form = CourseSearchForm()

    # تهيئة قائمة المدربين
    trainers = User.query.filter_by(role='trainer').all()
    form.trainer_id.choices = [(0, 'جميع المدربين')] + [(t.id, t.username) for t in trainers]

    # تحديث خيارات القوائم المنسدلة
    course_categories = CourseCategory.query.all()
    form.category_id.choices = [(0, 'جميع التصنيفات')] + [(c.id, c.name) for c in course_categories]

    course_levels = CourseLevel.query.all()
    form.level_id.choices = [(0, 'جميع المستويات')] + [(c.id, c.name) for c in course_levels]

    if form.validate_on_submit():
        # بناء شروط البحث
        query = Course.query

        # البحث بعنوان الدورة
        if form.title.data:
            query = query.filter(Course.title.like(f'%{form.title.data}%'))

        # البحث بالتصنيف
        if form.category_id.data and form.category_id.data != 0:
            category = CourseCategory.query.get(form.category_id.data)
            if category:
                query = query.filter(Course.category == category.name)

        # البحث بالمستوى
        if form.level_id.data and form.level_id.data != 0:
            level = CourseLevel.query.get(form.level_id.data)
            if level:
                query = query.filter(Course.level == level.name)

        # البحث بالمدرب
        if form.trainer_id.data != 0:
            query = query.filter(Course.trainer_id == form.trainer_id.data)

        # البحث بالتاريخ
        if form.use_hijri_dates.data:
            # البحث بالتاريخ الهجري
            start_date_hijri = None
            end_date_hijri = None

            if form.start_date_hijri_day.data and form.start_date_hijri_month.data and form.start_date_hijri_year.data:
                start_date_hijri = f"{form.start_date_hijri_day.data}/{form.start_date_hijri_month.data}/{form.start_date_hijri_year.data}"
                query = query.filter(Course.start_date_hijri >= start_date_hijri)

            if form.end_date_hijri_day.data and form.end_date_hijri_month.data and form.end_date_hijri_year.data:
                end_date_hijri = f"{form.end_date_hijri_day.data}/{form.end_date_hijri_month.data}/{form.end_date_hijri_year.data}"
                query = query.filter(Course.end_date_hijri <= end_date_hijri)
        else:
            # البحث بالتاريخ الميلادي
            if form.start_date.data:
                query = query.filter(Course.start_date >= form.start_date.data)
            if form.end_date.data:
                query = query.filter(Course.end_date <= form.end_date.data)

        # تنفيذ البحث
        courses = query.all()
        return render_template('search_results.html', title='نتائج البحث', courses=courses, form=form)

    return render_template('search_courses.html', title='البحث عن الدورات', form=form)

@app.route('/course/<int:course_id>')
@login_required
def course_details(course_id):
    # جلب تفاصيل الدورة التدريبية
    course = Course.query.get_or_404(course_id)

    # جلب المواد التعليمية مرتبة حسب رقم اليوم
    materials = Material.query.filter_by(course_id=course_id).order_by(Material.day_number).all()

    # تنظيم المواد حسب اليوم
    materials_by_day = {}
    for day in range(1, course.duration_days + 1):
        materials_by_day[day] = [m for m in materials if m.day_number == day]

    # جلب الجدول الزمني للدورة مرتب حسب اليوم والوقت
    schedule_items = CourseSchedule.query.filter_by(course_id=course_id).order_by(
        CourseSchedule.day_number, CourseSchedule.start_time).all()

    # تنظيم الجدول الزمني حسب اليوم
    schedule_by_day = {}
    for day in range(1, course.duration_days + 1):
        schedule_by_day[day] = [s for s in schedule_items if s.day_number == day]

    # التحقق مما إذا كان المستخدم مسجل في الدورة
    is_enrolled = False
    if current_user.role == 'trainee':
        enrollment = Enrollment.query.filter_by(user_id=current_user.id, course_id=course_id).first()
        is_enrolled = enrollment is not None

    return render_template('course_details.html', title=course.title, course=course,
                           materials_by_day=materials_by_day, schedule_by_day=schedule_by_day,
                           is_enrolled=is_enrolled)

@app.route('/course/add', methods=['GET', 'POST'])
@login_required
def add_course():
    # التحقق من أن المستخدم هو مدرب أو مدير
    if current_user.role not in ['trainer', 'admin']:
        flash('ليس لديك صلاحية لإضافة دورة تدريبية', 'danger')
        return redirect(url_for('courses'))

    form = CourseForm()

    # إضافة خيارات فارغة للقوائم المنسدلة
    empty_choice = [(0, 'اختر...')]

    # تحديث خيارات القوائم المنسدلة
    agencies = Agency.query.all()
    form.agency_id.choices = empty_choice + [(a.id, a.name) for a in agencies]
    form.target_agency_id.choices = empty_choice + [(a.id, a.name) for a in agencies]

    centers = TrainingCenter.query.all()
    form.center_id.choices = empty_choice + [(c.id, c.name) for c in centers]

    participant_types = ParticipantType.query.all()
    form.participant_type_id.choices = empty_choice + [(p.id, p.name) for p in participant_types]

    course_levels = CourseLevel.query.all()
    form.status_id.choices = empty_choice + [(c.id, c.name) for c in course_levels]
    form.level_id.choices = empty_choice + [(c.id, c.name) for c in course_levels]

    course_categories = CourseCategory.query.all()
    form.category_id.choices = empty_choice + [(c.id, c.name) for c in course_categories]

    # المسارات ومستويات المسارات
    course_paths = CoursePath.query.all()
    form.path_id.choices = empty_choice + [(p.id, p.name) for p in course_paths]

    # تصنيفات القوة
    force_classifications = ForceClassification.query.all()
    form.force_classification_id.choices = empty_choice + [(f.id, f.name) for f in force_classifications]

    # تحديث مستويات المسار بناءً على المسار المحدد
    if request.method == 'POST' and form.path_id.data:
        path_id = form.path_id.data
        path_levels = CoursePathLevel.query.filter_by(path_id=path_id).all()
        form.path_level_id.choices = empty_choice + [(l.id, l.name) for l in path_levels]
    elif request.method == 'GET' and request.args.get('path_id'):
        path_id = int(request.args.get('path_id'))
        path_levels = CoursePathLevel.query.filter_by(path_id=path_id).all()
        form.path_level_id.choices = empty_choice + [(l.id, l.name) for l in path_levels]
    else:
        form.path_level_id.choices = empty_choice

    # التحقق من أن النموذج تم إرساله بطريقة POST
    if request.method == 'POST':
        # طباعة بيانات النموذج للتشخيص
        print(f"Form data: {request.form}")
        print(f"Form errors: {form.errors}")

        # التحقق من صحة حقل العنوان
        if not form.title.data:
            print("عنوان الدورة غير محدد")
            if 'title' not in form.errors:
                form.errors['title'] = ['يرجى تحديد عنوان الدورة']
        else:
            print(f"عنوان الدورة: {form.title.data}")

        # التحقق من صحة حقل الوصف
        if not form.description.data:
            print("وصف الدورة غير محدد")
            if 'description' not in form.errors:
                form.errors['description'] = ['يرجى إدخال وصف الدورة']
        else:
            print(f"وصف الدورة: {form.description.data}")

        # التحقق من صحة حقل التصنيف
        if not form.category_id.data or form.category_id.data == 0:
            print("تصنيف الدورة غير محدد")
            if 'category_id' not in form.errors:
                form.errors['category_id'] = ['يرجى اختيار تصنيف الدورة']
        else:
            print(f"تصنيف الدورة: {form.category_id.data}")

        # التحقق من صحة حقل المستوى
        if not form.level_id.data or form.level_id.data == 0:
            print("مستوى الدورة غير محدد")
            if 'level_id' not in form.errors:
                form.errors['level_id'] = ['يرجى اختيار مستوى الدورة']
        else:
            print(f"مستوى الدورة: {form.level_id.data}")

        # التحقق من صحة حقل تاريخ البدء
        if form.start_date.data is None:
            print("تاريخ البدء غير محدد")
            if 'start_date' not in form.errors:
                form.errors['start_date'] = ['يرجى تحديد تاريخ البدء']
        else:
            print(f"تاريخ البدء: {form.start_date.data}")

        # التحقق من صحة حقل مدة الدورة
        if form.duration_days.data is None:
            print("مدة الدورة غير محددة")
            if 'duration_days' not in form.errors:
                form.errors['duration_days'] = ['يرجى تحديد مدة الدورة']
        else:
            print(f"مدة الدورة: {form.duration_days.data}")

        # طباعة بيانات النموذج للتشخيص
        print(f"Form data: {request.form}")
        print(f"Form errors: {form.errors}")

        # التحقق من صحة النموذج
        if form.validate_on_submit():
            try:
                print("النموذج صالح، جاري إنشاء الدورة...")
                # حساب تاريخ الانتهاء
                if form.start_date.data:
                    start_date = form.start_date.data
                else:
                    # استخدام تاريخ اليوم إذا لم يتم إدخال تاريخ البدء
                    start_date = datetime.now().date()

                duration_days = int(form.duration_days.data) if form.duration_days.data else 1
                end_date = start_date + timedelta(days=duration_days - 1)

                # معالجة الصورة إذا تم تحميلها
                image_filename = 'default_course.jpg'
                if form.image.data and form.image.data.filename:
                    try:
                        image_filename = secure_filename(form.image.data.filename)
                        # التحقق من امتداد الملف
                        file_ext = os.path.splitext(image_filename)[1].lower()
                        print(f"امتداد الملف: {file_ext}")

                        # قائمة الامتدادات المسموح بها
                        allowed_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp']

                        if file_ext not in allowed_extensions:
                            flash(f'امتداد الملف {file_ext} غير مسموح به. يجب أن تكون الصورة بامتداد jpg أو png أو jpeg أو gif', 'danger')
                            # استخدام الصورة الافتراضية في حالة امتداد غير صالح
                            image_filename = 'default_course.jpg'
                        else:
                            # حفظ الصورة
                            image_path = os.path.join(app.config['UPLOAD_FOLDER'], 'courses', image_filename)
                            os.makedirs(os.path.dirname(image_path), exist_ok=True)
                            form.image.data.save(image_path)
                            print(f"تم حفظ الصورة بنجاح: {image_path}")
                    except Exception as e:
                        print(f"خطأ في حفظ الصورة: {str(e)}")
                        # استخدام الصورة الافتراضية في حالة حدوث خطأ
                        image_filename = 'default_course.jpg'

                # التحقق من وجود رقم الدورة
                if not form.course_number.data:
                    flash('يرجى إدخال رقم الدورة العام', 'danger')
                    return render_template('add_course.html', title='إضافة دورة تدريبية', form=form)

                # التحقق من عدم وجود دورة بنفس الرقم
                existing_course = Course.query.filter_by(course_number=form.course_number.data).first()
                if existing_course:
                    flash(f'يوجد دورة بالفعل برقم {form.course_number.data}، يرجى استخدام رقم آخر', 'danger')
                    return render_template('add_course.html', title='إضافة دورة تدريبية', form=form)

                # تجهيز التواريخ الهجرية (اختياري)
                start_date_hijri = None
                end_date_hijri = None

                # إذا تم إدخال جميع حقول تاريخ البدء الهجري
                if form.start_date_hijri_day.data and form.start_date_hijri_month.data and form.start_date_hijri_year.data:
                    start_date_hijri = f"{form.start_date_hijri_day.data}/{form.start_date_hijri_month.data}/{form.start_date_hijri_year.data}"
                else:
                    # استخدام تاريخ افتراضي إذا لم يتم إدخال التاريخ الهجري
                    start_date_hijri = "01/01/1445"

                # إذا تم إدخال جميع حقول تاريخ النهاية الهجري
                if form.end_date_hijri_day.data and form.end_date_hijri_month.data and form.end_date_hijri_year.data:
                    end_date_hijri = f"{form.end_date_hijri_day.data}/{form.end_date_hijri_month.data}/{form.end_date_hijri_year.data}"
                else:
                    # استخدام تاريخ افتراضي إذا لم يتم إدخال التاريخ الهجري
                    end_date_hijri = "01/01/1445"

                # تجميع تاريخ الدخول والخروج الهجري
                entry_date_hijri = None
                exit_date_hijri = None

                if form.entry_date_hijri_day.data and form.entry_date_hijri_month.data and form.entry_date_hijri_year.data:
                    entry_date_hijri = f"{form.entry_date_hijri_day.data}/{form.entry_date_hijri_month.data}/{form.entry_date_hijri_year.data}"

                if form.exit_date_hijri_day.data and form.exit_date_hijri_month.data and form.exit_date_hijri_year.data:
                    exit_date_hijri = f"{form.exit_date_hijri_day.data}/{form.exit_date_hijri_month.data}/{form.exit_date_hijri_year.data}"

                # الحصول على قيم التصنيف والمستوى من الجداول الترميزية
                category_name = 'أخرى'  # قيمة افتراضية
                if form.category_id.data and form.category_id.data != 0:
                    category = CourseCategory.query.get(form.category_id.data)
                    if category:
                        category_name = category.name

                level_name = 'مبتدئ'  # قيمة افتراضية
                if form.level_id.data and form.level_id.data != 0:
                    level = CourseLevel.query.get(form.level_id.data)
                    if level:
                        level_name = level.name

                # إنشاء دورة جديدة بجميع الحقول
                course = Course(
                    course_number=form.course_number.data,  # استخدام الرقم المدخل من المستخدم
                    agency_course_number=form.agency_course_number.data,
                    place_course_number=form.place_course_number.data,
                    title=form.title.data,
                    description=form.description.data,
                    category=category_name,
                    level=level_name,
                    image=image_filename,
                    start_date=start_date,
                    end_date=end_date,
                    start_date_hijri=start_date_hijri,
                    end_date_hijri=end_date_hijri,
                    duration_days=duration_days,
                    trainer_id=current_user.id,
                    path_id=form.path_id.data if form.path_id.data != 0 else None,
                    path_level_id=form.path_level_id.data if form.path_level_id.data != 0 else None,
                    agency_id=form.agency_id.data if form.agency_id.data != 0 else None,
                    center_id=form.center_id.data if form.center_id.data != 0 else None,
                    place_name=form.place_name.data,
                    participant_type_id=form.participant_type_id.data if form.participant_type_id.data != 0 else None,
                    status_id=form.status_id.data if form.status_id.data != 0 else None,
                    entry_date=form.entry_date.data,
                    exit_date=form.exit_date.data,
                    entry_date_hijri=entry_date_hijri,
                    exit_date_hijri=exit_date_hijri,
                    force_classification_id=form.force_classification_id.data if form.force_classification_id.data != 0 else None,
                    target_agency_id=form.target_agency_id.data if form.target_agency_id.data != 0 else None,
                    target_count=form.target_count.data,
                    card_type=form.card_type.data,
                    notes=form.notes.data
                )

                # حفظ الدورة في قاعدة البيانات
                db.session.add(course)
                db.session.commit()

                # طباعة معلومات الدورة للتشخيص
                print(f"تم إنشاء الدورة بنجاح: {course.id} - {course.title}")

                flash(f'تم إضافة الدورة التدريبية بنجاح! رقم الدورة: {course.course_number}', 'success')
                return redirect(url_for('course_details', course_id=course.id))
            except Exception as e:
                db.session.rollback()
                flash(f'حدث خطأ أثناء إضافة الدورة: {str(e)}', 'danger')
                print(f"Error: {str(e)}")
        else:
            # طباعة أخطاء النموذج للتشخيص
            print(f"Form validation failed. Errors: {form.errors}")

            # عرض رسائل الخطأ المحددة للمستخدم
            for field, errors in form.errors.items():
                for error in errors:
                    field_name = getattr(form, field).label.text
                    flash(f'خطأ في حقل {field_name}: {error}', 'danger')

    return render_template('add_course.html', title='إضافة دورة تدريبية', form=form)

@app.route('/course/<int:course_id>/add_material', methods=['GET', 'POST'])
@login_required
def add_material(course_id):
    # جلب الدورة
    course = Course.query.get_or_404(course_id)

    # التحقق من أن المستخدم هو مدرب الدورة أو مدير
    if current_user.id != course.trainer_id and current_user.role != 'admin':
        flash('ليس لديك صلاحية لإضافة مواد تعليمية لهذه الدورة', 'danger')
        return redirect(url_for('course_details', course_id=course_id))

    form = MaterialForm()

    # تحديث خيارات أيام الدورة
    form.day_number.choices = [(str(i), f'اليوم {i}') for i in range(1, course.duration_days + 1)]

    if form.validate_on_submit():
        # معالجة الملف إذا تم تحميله
        file_path = None
        file_type = None

        if form.file.data:
            try:
                filename = secure_filename(form.file.data.filename)
                file_extension = filename.rsplit('.', 1)[1].lower()

                # تحديد نوع الملف
                if file_extension in ['pdf']:
                    file_type = 'pdf'
                elif file_extension in ['ppt', 'pptx']:
                    file_type = 'presentation'
                elif file_extension in ['doc', 'docx']:
                    file_type = 'document'
                elif file_extension in ['xls', 'xlsx']:
                    file_type = 'spreadsheet'
                elif file_extension in ['jpg', 'png', 'jpeg']:
                    file_type = 'image'
                elif file_extension in ['mp4']:
                    file_type = 'video'
                else:
                    file_type = 'other'

                # حفظ الملف
                material_folder = os.path.join(app.config['UPLOAD_FOLDER'], 'materials', str(course_id))
                os.makedirs(material_folder, exist_ok=True)
                file_path = os.path.join('materials', str(course_id), filename)
                # استخدام الشرطة المائلة العادية في مسار الملف
                file_path = file_path.replace('\\', '/')
                form.file.data.save(os.path.join(app.config['UPLOAD_FOLDER'], file_path))
            except Exception as e:
                flash(f'حدث خطأ أثناء تحميل الملف: {str(e)}', 'danger')
                return render_template('add_material.html', title='إضافة مادة تعليمية', form=form, course=course, max_file_size=MAX_FILE_SIZE_READABLE)

        # إنشاء مادة تعليمية جديدة
        material = Material(
            title=form.title.data,
            description=form.description.data,
            file_path=file_path,
            file_type=file_type,
            day_number=int(form.day_number.data),
            course_id=course_id
        )

        db.session.add(material)
        db.session.commit()

        flash('تم إضافة المادة التعليمية بنجاح!', 'success')
        return redirect(url_for('course_details', course_id=course_id))

    return render_template('add_material.html', title='إضافة مادة تعليمية', form=form, course=course, max_file_size=MAX_FILE_SIZE_READABLE)

@app.route('/download/<path:filename>')
@login_required
def download_file(filename):
    # تحويل مسار الملف من مسار نسبي إلى مسار مطلق
    # استبدال الشرطة المائلة العكسية بالشرطة المائلة العادية
    filename = filename.replace('\\', '/')
    return send_from_directory(app.config['UPLOAD_FOLDER'], filename, as_attachment=True)

@app.route('/view/<path:filename>')
@login_required
def view_file(filename):
    # تحويل مسار الملف من مسار نسبي إلى مسار مطلق
    # استبدال الشرطة المائلة العكسية بالشرطة المائلة العادية
    filename = filename.replace('\\', '/')
    return send_from_directory(app.config['UPLOAD_FOLDER'], filename, as_attachment=False)

@app.route('/course/<int:course_id>/enroll')
@login_required
def enroll_course(course_id):
    # التحقق من أن المستخدم هو متدرب
    if current_user.role != 'trainee':
        flash('فقط المتدربين يمكنهم التسجيل في الدورات', 'danger')
        return redirect(url_for('course_details', course_id=course_id))

    # التحقق مما إذا كان المستخدم مسجل بالفعل
    enrollment = Enrollment.query.filter_by(user_id=current_user.id, course_id=course_id).first()
    if enrollment:
        flash('أنت مسجل بالفعل في هذه الدورة', 'info')
        return redirect(url_for('course_details', course_id=course_id))

    # إنشاء تسجيل جديد
    enrollment = Enrollment(user_id=current_user.id, course_id=course_id)
    db.session.add(enrollment)
    db.session.commit()

    flash('تم تسجيلك في الدورة بنجاح!', 'success')
    return redirect(url_for('course_details', course_id=course_id))

@app.route('/course/<int:course_id>/participants')
@login_required
def course_participants(course_id):
    # التحقق من أن المستخدم هو مدرب الدورة أو مدير
    course = Course.query.get_or_404(course_id)
    if current_user.id != course.trainer_id and current_user.role != 'admin':
        flash('ليس لديك صلاحية لعرض المشاركين في هذه الدورة', 'danger')
        return redirect(url_for('course_details', course_id=course_id))

    # جلب المشاركين في الدورة
    participants = CourseParticipant.query.filter_by(course_id=course_id).all()

    return render_template('course_participants_with_evaluation.html', title='المشاركين في الدورة', course=course, participants=participants)

@app.route('/course/<int:course_id>/add_participant', methods=['GET', 'POST'])
@login_required
def add_course_participant(course_id):
    # التحقق من أن المستخدم هو مدرب الدورة أو مدير
    course = Course.query.get_or_404(course_id)
    if current_user.id != course.trainer_id and current_user.role != 'admin':
        flash('ليس لديك صلاحية لإضافة مشاركين في هذه الدورة', 'danger')
        return redirect(url_for('course_details', course_id=course_id))

    form = CourseParticipantForm()

    # تحديث خيارات القوائم المنسدلة - استخدام جدول person_data مع استثناء المضافين مسبقاً
    # جلب معرفات الأشخاص المضافين مسبقاً في هذه الدورة
    existing_participant_ids = [p.personal_data_id for p in CourseParticipant.query.filter_by(course_id=course_id).all()]

    # جلب الأشخاص المتاحين للإضافة (غير المضافين مسبقاً)
    available_people = PersonData.query.filter(~PersonData.id.in_(existing_participant_ids)).all()

    form.personal_data_id.choices = [(0, 'اختر مشارك')] + [(p.id, p.full_name) for p in available_people]

    if form.validate_on_submit():
        # التحقق من أن المستخدم اختار مشارك صحيح
        if form.personal_data_id.data == 0:
            flash('يرجى اختيار مشارك من القائمة', 'warning')
            return render_template('add_course_participant.html', title='إضافة مشارك', form=form, course=course)

        # التحقق مما إذا كان المشارك مسجل بالفعل (احتياط إضافي)
        existing_participant = CourseParticipant.query.filter_by(
            course_id=course_id,
            personal_data_id=form.personal_data_id.data
        ).first()

        if existing_participant:
            flash('هذا المشارك مسجل بالفعل في هذه الدورة', 'warning')
            return redirect(url_for('course_participants', course_id=course_id))

        # إنشاء مشارك جديد
        participant = CourseParticipant(
            course_id=course_id,
            personal_data_id=form.personal_data_id.data,
            entry_date=form.entry_date.data,
            status=form.status.data,
            notes=form.notes.data
        )

        db.session.add(participant)

        # تحديث إجمالي عدد المشاركين في الدورة
        course.total_participants = CourseParticipant.query.filter_by(course_id=course_id).count() + 1

        # تحديث إجمالي عدد الخريجين والمنسحبين
        course.total_graduates = CourseParticipant.query.filter_by(course_id=course_id, status='completed').count()
        if form.status.data == 'completed':
            course.total_graduates += 1

        course.total_dropouts = CourseParticipant.query.filter_by(course_id=course_id, status='dropped').count()
        if form.status.data == 'dropped':
            course.total_dropouts += 1

        db.session.commit()

        flash('تم إضافة المشارك بنجاح!', 'success')
        return redirect(url_for('course_participants', course_id=course_id))

    return render_template('add_course_participant.html', title='إضافة مشارك', form=form, course=course)

@app.route('/course/participant/<int:participant_id>/edit', methods=['GET', 'POST'])
@login_required
def edit_course_participant(participant_id):
    # جلب المشارك
    participant = CourseParticipant.query.get_or_404(participant_id)
    course_id = participant.course_id
    course = Course.query.get_or_404(course_id)

    # التحقق من أن المستخدم هو مدرب الدورة أو مدير
    if current_user.id != course.trainer_id and current_user.role != 'admin':
        flash('ليس لديك صلاحية لتعديل هذا المشارك', 'danger')
        return redirect(url_for('course_participants', course_id=course_id))

    form = CourseParticipantForm()

    # تحديث خيارات القوائم المنسدلة - استخدام جدول person_data مع استثناء المضافين مسبقاً
    # جلب معرفات الأشخاص المضافين مسبقاً في هذه الدورة (عدا المشارك الحالي)
    existing_participant_ids = [p.personal_data_id for p in CourseParticipant.query.filter_by(course_id=course_id).filter(CourseParticipant.id != participant_id).all()]

    # جلب الأشخاص المتاحين للإضافة (غير المضافين مسبقاً) + المشارك الحالي
    available_people = PersonData.query.filter(~PersonData.id.in_(existing_participant_ids)).all()

    form.personal_data_id.choices = [(p.id, p.full_name) for p in available_people]

    if form.validate_on_submit():
        # حفظ الحالة السابقة للمشارك
        previous_status = participant.status

        # تحديث بيانات المشارك
        participant.personal_data_id = form.personal_data_id.data
        participant.entry_date = form.entry_date.data
        participant.status = form.status.data
        participant.notes = form.notes.data

        # تحديث إجمالي عدد الخريجين والمنسحبين
        if previous_status != form.status.data:
            # إذا تغيرت الحالة من مكتمل إلى غير مكتمل
            if previous_status == 'completed' and form.status.data != 'completed':
                course.total_graduates -= 1
            # إذا تغيرت الحالة من غير مكتمل إلى مكتمل
            elif previous_status != 'completed' and form.status.data == 'completed':
                course.total_graduates += 1

            # إذا تغيرت الحالة من منسحب إلى غير منسحب
            if previous_status == 'dropped' and form.status.data != 'dropped':
                course.total_dropouts -= 1
            # إذا تغيرت الحالة من غير منسحب إلى منسحب
            elif previous_status != 'dropped' and form.status.data == 'dropped':
                course.total_dropouts += 1

        db.session.commit()

        flash('تم تحديث بيانات المشارك بنجاح!', 'success')
        return redirect(url_for('course_participants', course_id=course_id))

    # ملء النموذج ببيانات المشارك الحالية
    elif request.method == 'GET':
        form.personal_data_id.data = participant.personal_data_id
        form.entry_date.data = participant.entry_date
        form.status.data = participant.status
        form.notes.data = participant.notes

    return render_template('edit_course_participant.html', title='تعديل مشارك', form=form, participant=participant, course=course)

@app.route('/course/participant/<int:participant_id>/delete')
@login_required
def delete_course_participant(participant_id):
    # جلب المشارك
    participant = CourseParticipant.query.get_or_404(participant_id)
    course_id = participant.course_id
    course = Course.query.get_or_404(course_id)

    # التحقق من أن المستخدم هو مدرب الدورة أو مدير
    if current_user.id != course.trainer_id and current_user.role != 'admin':
        flash('ليس لديك صلاحية لحذف هذا المشارك', 'danger')
        return redirect(url_for('course_participants', course_id=course_id))

    # تحديث إجمالي عدد المشاركين والخريجين والمنسحبين
    course.total_participants -= 1

    if participant.status == 'completed':
        course.total_graduates -= 1
    elif participant.status == 'dropped':
        course.total_dropouts -= 1

    # حذف المشارك
    db.session.delete(participant)
    db.session.commit()

    flash('تم حذف المشارك بنجاح!', 'success')
    return redirect(url_for('course_participants', course_id=course_id))

# ===== مسارات نظام التقييم =====

@app.route('/course/<int:course_id>/evaluation/form', methods=['POST'])
@login_required
@csrf.exempt
def evaluation_form(course_id):
    """عرض نموذج التقييم الديناميكي"""
    try:
        course = Course.query.get_or_404(course_id)

        # التحقق من الصلاحيات
        if current_user.id != course.trainer_id and current_user.role != 'admin':
            return jsonify({'error': 'ليس لديك صلاحية للتقييم'}), 403

        data = request.get_json()
        if not data:
            return jsonify({'error': 'لا توجد بيانات في الطلب'}), 400

        participant_ids = data.get('participant_ids', [])
        if not participant_ids:
            return jsonify({'error': 'لم يتم تحديد مشاركين'}), 400

        # جلب المشاركين
        participants = CourseParticipant.query.filter(
            CourseParticipant.id.in_(participant_ids),
            CourseParticipant.course_id == course_id
        ).all()

        if not participants:
            return jsonify({'error': 'لم يتم العثور على المشاركين المحددين'}), 404

        # جلب معايير التقييم للدورة
        criteria_data = []
        total_max_score = 0

        try:
            # جلب المعايير المرتبطة بالدورة
            criteria_results = db.engine.execute("""
                SELECT DISTINCT ec.id, ec.name, ec.description, ec.order_index
                FROM evaluation_criteria ec
                JOIN course_evaluation_criteria cec ON ec.id = cec.criteria_id
                WHERE cec.course_id = ? AND cec.is_active = 1 AND ec.is_active = 1
                ORDER BY ec.order_index
            """, (course_id,)).fetchall()

            for criteria in criteria_results:
                # جلب البنود لكل معيار
                items_results = db.engine.execute("""
                    SELECT id, name, description, max_score, order_index
                    FROM evaluation_items
                    WHERE criteria_id = ? AND is_active = 1
                    ORDER BY order_index
                """, (criteria['id'],)).fetchall()

                items = []
                for item in items_results:
                    items.append({
                        'id': item['id'],
                        'name': item['name'],
                        'description': item['description'],
                        'max_score': item['max_score'],
                        'order_index': item['order_index']
                    })
                    total_max_score += item['max_score']

                if items:  # إضافة المعيار فقط إذا كان له بنود
                    criteria_data.append({
                        'id': criteria['id'],
                        'name': criteria['name'],
                        'description': criteria['description'],
                        'order_index': criteria['order_index'],
                        'items': items
                    })

        except Exception as e:
            print(f"خطأ في جلب معايير التقييم: {str(e)}")
            return jsonify({'error': f'خطأ في جلب معايير التقييم: {str(e)}'}), 500

        # للاختبار: استخدام النموذج المبسط أولاً
        return render_template('simple_evaluation_form.html',
                             course=course,
                             participants=participants,
                             criteria_data=criteria_data,
                             total_max_score=total_max_score)

    except Exception as e:
        print(f"خطأ عام في evaluation_form: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({'error': f'خطأ في الخادم: {str(e)}'}), 500

@app.route('/course/<int:course_id>/evaluation/save', methods=['POST'])
@login_required
@csrf.exempt
def save_evaluation(course_id):
    """حفظ التقييم"""
    course = Course.query.get_or_404(course_id)

    # التحقق من الصلاحيات
    if current_user.id != course.trainer_id and current_user.role != 'admin':
        return jsonify({'success': False, 'message': 'ليس لديك صلاحية للتقييم'}), 403

    try:
        data = request.get_json()

        # معالجة participant_ids
        participant_ids = data.get('participant_ids', [])
        if isinstance(participant_ids, str):
            participant_ids = [participant_ids]
        elif not isinstance(participant_ids, list):
            participant_ids = [participant_ids]

        # إنشاء تقييم لكل مشارك
        evaluation_ids = []
        for participant_id in participant_ids:
            # حساب الدرجة الإجمالية
            total_score = (
                float(data.get('score_attendance', 0)) +
                float(data.get('score_participation', 0)) +
                float(data.get('score_theory', 0)) +
                float(data.get('score_practical', 0)) +
                float(data.get('score_personal', 0))
            )

            percentage = (total_score / 115) * 100

            # إنشاء التقييم
            evaluation_data = {
                'course_id': course_id,
                'participant_id': participant_id,
                'evaluator_id': current_user.id,
                'total_score': total_score,
                'percentage': percentage,
                'grade': data.get('final_grade', ''),
                'notes': data.get('general_notes', ''),
                'is_final': 1 if data.get('evaluation_status') == 'final' else 0
            }

            # حفظ في قاعدة البيانات
            db.engine.execute("""
                INSERT INTO participant_evaluations
                (course_id, participant_id, evaluator_id, total_score, percentage, grade, notes, is_final)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                evaluation_data['course_id'],
                evaluation_data['participant_id'],
                evaluation_data['evaluator_id'],
                evaluation_data['total_score'],
                evaluation_data['percentage'],
                evaluation_data['grade'],
                evaluation_data['notes'],
                evaluation_data['is_final']
            ))

            # الحصول على معرف التقييم المحفوظ
            result = db.engine.execute("SELECT last_insert_rowid()").fetchone()
            evaluation_id = result[0]
            evaluation_ids.append(evaluation_id)

            # حفظ تفاصيل التقييم
            criteria_scores = [
                (evaluation_id, 1, float(data.get('score_attendance', 0)), data.get('notes_attendance', '')),
                (evaluation_id, 2, float(data.get('score_participation', 0)), data.get('notes_participation', '')),
                (evaluation_id, 3, float(data.get('score_theory', 0)), data.get('notes_theory', '')),
                (evaluation_id, 4, float(data.get('score_practical', 0)), data.get('notes_practical', '')),
                (evaluation_id, 6, float(data.get('score_personal', 0)), data.get('notes_personal', ''))
            ]

            for eval_id, criteria_id, score, notes in criteria_scores:
                db.engine.execute("""
                    INSERT INTO evaluation_details (evaluation_id, criteria_id, score, notes)
                    VALUES (?, ?, ?, ?)
                """, (eval_id, criteria_id, score, notes))

        return jsonify({
            'success': True,
            'message': 'تم حفظ التقييم بنجاح',
            'evaluation_id': evaluation_ids[0] if evaluation_ids else None
        })

    except Exception as e:
        return jsonify({'success': False, 'message': f'حدث خطأ: {str(e)}'})

@app.route('/course/<int:course_id>/evaluation/save-dynamic', methods=['POST'])
@login_required
@csrf.exempt
def save_dynamic_evaluation(course_id):
    """حفظ التقييم الديناميكي"""
    course = Course.query.get_or_404(course_id)

    # التحقق من الصلاحيات
    if current_user.id != course.trainer_id and current_user.role != 'admin':
        return jsonify({'success': False, 'message': 'ليس لديك صلاحية للتقييم'}), 403

    try:
        data = request.get_json()

        # معالجة participant_ids
        participant_ids = data.get('participant_ids', [])
        if isinstance(participant_ids, str):
            participant_ids = [participant_ids]
        elif not isinstance(participant_ids, list):
            participant_ids = [participant_ids]

        # إنشاء تقييم لكل مشارك
        evaluation_ids = []
        for participant_id in participant_ids:
            if not participant_id:
                continue

            # إنشاء التقييم الرئيسي
            evaluation_data = {
                'course_id': course_id,
                'participant_id': participant_id,
                'evaluator_id': current_user.id,
                'total_score': float(data.get('total_score', 0)),
                'percentage': float(data.get('percentage', 0)),
                'grade': data.get('final_grade', ''),
                'notes': data.get('general_notes', ''),
                'is_final': 1 if data.get('evaluation_status') == 'final' else 0
            }

            # حفظ التقييم الرئيسي
            db.engine.execute("""
                INSERT INTO participant_evaluations
                (course_id, participant_id, evaluator_id, total_score, percentage, grade, notes, is_final)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                evaluation_data['course_id'],
                evaluation_data['participant_id'],
                evaluation_data['evaluator_id'],
                evaluation_data['total_score'],
                evaluation_data['percentage'],
                evaluation_data['grade'],
                evaluation_data['notes'],
                evaluation_data['is_final']
            ))

            # الحصول على معرف التقييم
            result = db.engine.execute("SELECT last_insert_rowid()").fetchone()
            evaluation_id = result[0]
            evaluation_ids.append(evaluation_id)

            # حفظ درجات البنود
            for key, value in data.items():
                if key.startswith('score_item_') and value:
                    item_id = key.replace('score_item_', '')
                    score = float(value)
                    notes_key = f'notes_item_{item_id}'
                    notes = data.get(notes_key, '')

                    db.engine.execute("""
                        INSERT INTO evaluation_item_scores (evaluation_id, item_id, score, notes)
                        VALUES (?, ?, ?, ?)
                    """, (evaluation_id, item_id, score, notes))

        return jsonify({
            'success': True,
            'message': 'تم حفظ التقييم بنجاح',
            'evaluation_id': evaluation_ids[0] if evaluation_ids else None
        })

    except Exception as e:
        return jsonify({'success': False, 'message': f'حدث خطأ: {str(e)}'})

@app.route('/course/<int:course_id>/evaluation/print')
@login_required
def print_evaluations(course_id):
    """طباعة التقييمات"""
    course = Course.query.get_or_404(course_id)
    participant_ids = request.args.get('participants', '').split(',')

    # جلب التقييمات
    evaluations = []
    for participant_id in participant_ids:
        if participant_id:
            # جلب آخر تقييم للمشارك مع تفاصيل التقييم
            result = db.engine.execute("""
                SELECT pe.*, cp.*, pd.full_name, pd.national_number, pd.military_number,
                       ed1.score as attendance_score, ed1.notes as attendance_notes,
                       ed2.score as participation_score, ed2.notes as participation_notes,
                       ed3.score as theory_score, ed3.notes as theory_notes,
                       ed4.score as practical_score, ed4.notes as practical_notes,
                       ed5.score as personal_score, ed5.notes as personal_notes,
                       u.username as evaluator_name
                FROM participant_evaluations pe
                JOIN course_participant cp ON pe.participant_id = cp.id
                JOIN person_data pd ON cp.personal_data_id = pd.id
                LEFT JOIN evaluation_details ed1 ON pe.id = ed1.evaluation_id AND ed1.criteria_id = 1
                LEFT JOIN evaluation_details ed2 ON pe.id = ed2.evaluation_id AND ed2.criteria_id = 2
                LEFT JOIN evaluation_details ed3 ON pe.id = ed3.evaluation_id AND ed3.criteria_id = 3
                LEFT JOIN evaluation_details ed4 ON pe.id = ed4.evaluation_id AND ed4.criteria_id = 4
                LEFT JOIN evaluation_details ed5 ON pe.id = ed5.evaluation_id AND ed5.criteria_id = 6
                LEFT JOIN user u ON pe.evaluator_id = u.id
                WHERE pe.course_id = ? AND pe.participant_id = ?
                ORDER BY pe.created_at DESC
                LIMIT 1
            """, (course_id, participant_id)).fetchone()

            if result:
                evaluation_dict = dict(result)
                # تحويل التواريخ إلى كائنات datetime إذا كانت نصوص
                if evaluation_dict.get('evaluation_date') and isinstance(evaluation_dict['evaluation_date'], str):
                    try:
                        evaluation_dict['evaluation_date'] = datetime.strptime(evaluation_dict['evaluation_date'], '%Y-%m-%d %H:%M:%S')
                    except:
                        evaluation_dict['evaluation_date'] = None

                evaluations.append(evaluation_dict)

    # إذا لم توجد تقييمات، إنشاء تقييم فارغ للطباعة
    if not evaluations:
        for participant_id in participant_ids:
            if participant_id:
                participant = CourseParticipant.query.get(participant_id)
                if participant and participant.personal_data:
                    evaluations.append({
                        'full_name': participant.personal_data.full_name,
                        'national_number': participant.personal_data.national_number,
                        'military_number': participant.personal_data.military_number,
                        'attendance_score': 0,
                        'participation_score': 0,
                        'theory_score': 0,
                        'practical_score': 0,
                        'personal_score': 0,
                        'total_score': 0,
                        'percentage': 0,
                        'grade': 'غير مقيم',
                        'notes': '',
                        'attendance_notes': '',
                        'participation_notes': '',
                        'theory_notes': '',
                        'practical_notes': '',
                        'personal_notes': '',
                        'evaluation_date': None,
                        'evaluator_name': ''
                    })

    return render_template('evaluation_print.html',
                         course=course,
                         evaluations=evaluations)

@app.route('/course/<int:course_id>/evaluation/export/excel')
@login_required
def export_evaluations_excel(course_id):
    """تصدير التقييمات إلى Excel"""
    course = Course.query.get_or_404(course_id)
    participant_ids = request.args.get('participants', '').split(',')

    # إنشاء ملف Excel
    from io import BytesIO
    import xlsxwriter

    output = BytesIO()
    workbook = xlsxwriter.Workbook(output)
    worksheet = workbook.add_worksheet('تقييمات المشاركين')

    # تنسيق الخلايا
    header_format = workbook.add_format({
        'bold': True,
        'bg_color': '#4472C4',
        'font_color': 'white',
        'border': 1
    })

    # كتابة العناوين
    headers = ['الاسم', 'الرقم الوطني', 'الحضور', 'المشاركة', 'النظري', 'العملي', 'المهارات', 'الإجمالي', 'النسبة', 'التقدير']
    for col, header in enumerate(headers):
        worksheet.write(0, col, header, header_format)

    # كتابة البيانات
    row = 1
    for participant_id in participant_ids:
        if participant_id:
            # جلب بيانات التقييم (النظام الجديد)
            result = db.engine.execute("""
                SELECT pe.*, pd.full_name, pd.national_number
                FROM participant_evaluations pe
                JOIN course_participant cp ON pe.participant_id = cp.id
                JOIN person_data pd ON cp.personal_data_id = pd.id
                WHERE pe.course_id = ? AND pe.participant_id = ?
                ORDER BY pe.created_at DESC
                LIMIT 1
            """, (course_id, participant_id)).fetchone()

            if result:
                data = [
                    result['full_name'],
                    result['national_number'],
                    result['attendance_score'] or 0,
                    result['participation_score'] or 0,
                    result['theory_score'] or 0,
                    result['practical_score'] or 0,
                    result['personal_score'] or 0,
                    result['total_score'],
                    f"{result['percentage']:.1f}%",
                    result['grade']
                ]

                for col, value in enumerate(data):
                    worksheet.write(row, col, value)
                row += 1

    workbook.close()
    output.seek(0)

    return send_file(
        output,
        as_attachment=True,
        download_name=f'تقييمات_الدورة_{course.title}.xlsx',
        mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    )

@app.route('/course/<int:course_id>/evaluation/export/pdf')
@login_required
def export_evaluations_pdf(course_id):
    """تصدير التقييمات إلى PDF"""
    course = Course.query.get_or_404(course_id)
    participant_ids = request.args.get('participants', '').split(',')

    # جلب التقييمات
    evaluations = []
    for participant_id in participant_ids:
        if participant_id:
            # جلب آخر تقييم للمشارك
            result = db.engine.execute("""
                SELECT pe.*, cp.*, pd.full_name, pd.national_number, pd.military_number,
                       ed1.score as attendance_score, ed1.notes as attendance_notes,
                       ed2.score as participation_score, ed2.notes as participation_notes,
                       ed3.score as theory_score, ed3.notes as theory_notes,
                       ed4.score as practical_score, ed4.notes as practical_notes,
                       ed5.score as personal_score, ed5.notes as personal_notes
                FROM participant_evaluations pe
                JOIN course_participant cp ON pe.participant_id = cp.id
                JOIN person_data pd ON cp.personal_data_id = pd.id
                LEFT JOIN evaluation_details ed1 ON pe.id = ed1.evaluation_id AND ed1.criteria_id = 1
                LEFT JOIN evaluation_details ed2 ON pe.id = ed2.evaluation_id AND ed2.criteria_id = 2
                LEFT JOIN evaluation_details ed3 ON pe.id = ed3.evaluation_id AND ed3.criteria_id = 3
                LEFT JOIN evaluation_details ed4 ON pe.id = ed4.evaluation_id AND ed4.criteria_id = 4
                LEFT JOIN evaluation_details ed5 ON pe.id = ed5.evaluation_id AND ed5.criteria_id = 6
                WHERE pe.course_id = ? AND pe.participant_id = ?
                ORDER BY pe.created_at DESC
                LIMIT 1
            """, (course_id, participant_id)).fetchone()

            if result:
                evaluations.append(dict(result))

    # إنشاء HTML للطباعة
    html_content = render_template('evaluation_print.html',
                                 course=course,
                                 evaluations=evaluations)

    # تحويل إلى PDF (يتطلب مكتبة wkhtmltopdf أو weasyprint)
    try:
        import pdfkit

        options = {
            'page-size': 'A4',
            'margin-top': '0.75in',
            'margin-right': '0.75in',
            'margin-bottom': '0.75in',
            'margin-left': '0.75in',
            'encoding': "UTF-8",
            'no-outline': None,
            'enable-local-file-access': None
        }

        pdf = pdfkit.from_string(html_content, False, options=options)

        response = make_response(pdf)
        response.headers['Content-Type'] = 'application/pdf'
        response.headers['Content-Disposition'] = f'attachment; filename=تقييمات_الدورة_{course.title}.pdf'

        return response

    except ImportError:
        # إذا لم تكن مكتبة PDF متوفرة، إرجاع HTML للطباعة
        flash('مكتبة PDF غير متوفرة، سيتم فتح صفحة للطباعة', 'info')
        return html_content

@app.route('/users')
@login_required
def users():
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # جلب جميع المستخدمين
    users = User.query.all()

    return render_template('users.html', title='إدارة المستخدمين', users=users)

@app.route('/material/<int:material_id>/edit', methods=['GET', 'POST'])
@login_required
def edit_material(material_id):
    # جلب المادة التعليمية
    material = Material.query.get_or_404(material_id)
    course_id = material.course_id
    course = Course.query.get_or_404(course_id)

    # التحقق من أن المستخدم هو مدرب الدورة أو مدير
    if current_user.id != material.course.trainer_id and current_user.role != 'admin':
        flash('ليس لديك صلاحية لتعديل هذه المادة التعليمية', 'danger')
        return redirect(url_for('course_details', course_id=course_id))

    form = EditMaterialForm()

    # تحديث خيارات أيام الدورة
    form.day_number.choices = [(str(i), f'اليوم {i}') for i in range(1, course.duration_days + 1)]

    if form.validate_on_submit():
        # تحديث بيانات المادة التعليمية
        material.title = form.title.data
        material.description = form.description.data
        material.day_number = int(form.day_number.data)

        # معالجة الملف إذا تم تحميله
        if form.file.data:
            try:
                # حذف الملف القديم إذا كان موجوداً
                if material.file_path:
                    old_file_path = os.path.join(app.config['UPLOAD_FOLDER'], material.file_path)
                    if os.path.exists(old_file_path):
                        os.remove(old_file_path)

                # حفظ الملف الجديد
                filename = secure_filename(form.file.data.filename)
                file_extension = filename.rsplit('.', 1)[1].lower()

                # تحديد نوع الملف
                if file_extension in ['pdf']:
                    material.file_type = 'pdf'
                elif file_extension in ['ppt', 'pptx']:
                    material.file_type = 'presentation'
                elif file_extension in ['doc', 'docx']:
                    material.file_type = 'document'
                elif file_extension in ['xls', 'xlsx']:
                    material.file_type = 'spreadsheet'
                elif file_extension in ['jpg', 'png', 'jpeg']:
                    material.file_type = 'image'
                elif file_extension in ['mp4']:
                    material.file_type = 'video'
                else:
                    material.file_type = 'other'

                # حفظ الملف
                material_folder = os.path.join(app.config['UPLOAD_FOLDER'], 'materials', str(course_id))
                os.makedirs(material_folder, exist_ok=True)
                material.file_path = os.path.join('materials', str(course_id), filename)
                # استخدام الشرطة المائلة العادية في مسار الملف
                material.file_path = material.file_path.replace('\\', '/')
                form.file.data.save(os.path.join(app.config['UPLOAD_FOLDER'], material.file_path))
            except Exception as e:
                flash(f'حدث خطأ أثناء تحميل الملف: {str(e)}', 'danger')
                return render_template('edit_material.html', title='تعديل المادة التعليمية', form=form, material=material, course=course, max_file_size=MAX_FILE_SIZE_READABLE)

        db.session.commit()

        flash('تم تحديث المادة التعليمية بنجاح!', 'success')
        return redirect(url_for('course_details', course_id=course_id))

    # ملء النموذج ببيانات المادة الحالية
    elif request.method == 'GET':
        form.title.data = material.title
        form.description.data = material.description
        form.day_number.data = str(material.day_number)

    return render_template('edit_material.html', title='تعديل المادة التعليمية', form=form, material=material, course=course, max_file_size=MAX_FILE_SIZE_READABLE)

@app.route('/material/<int:material_id>/delete')
@login_required
def delete_material(material_id):
    # جلب المادة التعليمية
    material = Material.query.get_or_404(material_id)
    course_id = material.course_id

    # التحقق من أن المستخدم هو مدرب الدورة أو مدير
    if current_user.id != material.course.trainer_id and current_user.role != 'admin':
        flash('ليس لديك صلاحية لحذف هذه المادة التعليمية', 'danger')
        return redirect(url_for('course_details', course_id=course_id))

    # حذف الملف إذا كان موجوداً
    if material.file_path:
        file_path = os.path.join(app.config['UPLOAD_FOLDER'], material.file_path)
        if os.path.exists(file_path):
            os.remove(file_path)

    # حذف المادة من قاعدة البيانات
    db.session.delete(material)
    db.session.commit()

    flash('تم حذف المادة التعليمية بنجاح!', 'success')
    return redirect(url_for('course_details', course_id=course_id))

@app.route('/course/<int:course_id>/schedule', methods=['GET', 'POST'])
@login_required
def course_schedule(course_id):
    # جلب الدورة
    course = Course.query.get_or_404(course_id)

    # التحقق من أن المستخدم هو مدرب الدورة أو مدير
    if current_user.id != course.trainer_id and current_user.role != 'admin':
        flash('ليس لديك صلاحية لإدارة الجدول الزمني لهذه الدورة', 'danger')
        return redirect(url_for('course_details', course_id=course_id))

    # جلب الجدول الزمني للدورة مرتب حسب اليوم والوقت
    schedule_items = CourseSchedule.query.filter_by(course_id=course_id).order_by(
        CourseSchedule.day_number, CourseSchedule.start_time).all()

    # تنظيم الجدول الزمني حسب اليوم
    schedule_by_day = {}
    for day in range(1, course.duration_days + 1):
        schedule_by_day[day] = [s for s in schedule_items if s.day_number == day]

    form = CourseScheduleForm()

    # تحديث خيارات القوائم المنسدلة
    form.day_number.choices = [(str(i), f'اليوم {i}') for i in range(1, course.duration_days + 1)]

    # جلب المواد التعليمية للدورة
    materials = Material.query.filter_by(course_id=course_id).all()
    form.material_id.choices = [(0, 'بدون مادة')] + [(m.id, m.title) for m in materials]

    if form.validate_on_submit():
        # التحقق من صحة الوقت
        try:
            start_time = form.start_time.data
            end_time = form.end_time.data

            # التحقق من تنسيق الوقت (HH:MM)
            if not re.match(r'^([01]\d|2[0-3]):([0-5]\d)$', start_time) or not re.match(r'^([01]\d|2[0-3]):([0-5]\d)$', end_time):
                flash('تنسيق الوقت غير صحيح. يجب أن يكون بتنسيق HH:MM', 'danger')
                return render_template('course_schedule.html', title='الجدول الزمني للدورة',
                                      form=form, course=course, schedule_by_day=schedule_by_day)

            # التحقق من أن وقت البدء قبل وقت الانتهاء
            start_hour, start_minute = map(int, start_time.split(':'))
            end_hour, end_minute = map(int, end_time.split(':'))

            start_minutes = start_hour * 60 + start_minute
            end_minutes = end_hour * 60 + end_minute

            if start_minutes >= end_minutes:
                flash('يجب أن يكون وقت البدء قبل وقت الانتهاء', 'danger')
                return render_template('course_schedule.html', title='الجدول الزمني للدورة',
                                      form=form, course=course, schedule_by_day=schedule_by_day)

            # إنشاء فترة جديدة في الجدول الزمني
            schedule_item = CourseSchedule(
                course_id=course_id,
                day_number=int(form.day_number.data),
                start_time=start_time,
                end_time=end_time,
                title=form.title.data,
                description=form.description.data,
                is_break=form.is_break.data,
                material_id=form.material_id.data if form.material_id.data != 0 else None
            )

            db.session.add(schedule_item)
            db.session.commit()

            flash('تم إضافة الفترة بنجاح!', 'success')
            return redirect(url_for('course_schedule', course_id=course_id))

        except Exception as e:
            flash(f'حدث خطأ أثناء إضافة الفترة: {str(e)}', 'danger')

    return render_template('course_schedule.html', title='الجدول الزمني للدورة',
                          form=form, course=course, schedule_by_day=schedule_by_day)

@app.route('/schedule/<int:schedule_id>/edit', methods=['GET', 'POST'])
@login_required
def edit_schedule_item(schedule_id):
    # جلب الفترة
    schedule_item = CourseSchedule.query.get_or_404(schedule_id)
    course_id = schedule_item.course_id
    course = Course.query.get_or_404(course_id)

    # التحقق من أن المستخدم هو مدرب الدورة أو مدير
    if current_user.id != course.trainer_id and current_user.role != 'admin':
        flash('ليس لديك صلاحية لتعديل هذه الفترة', 'danger')
        return redirect(url_for('course_schedule', course_id=course_id))

    form = CourseScheduleForm()

    # تحديث خيارات القوائم المنسدلة
    form.day_number.choices = [(str(i), f'اليوم {i}') for i in range(1, course.duration_days + 1)]

    # جلب المواد التعليمية للدورة
    materials = Material.query.filter_by(course_id=course_id).all()
    form.material_id.choices = [(0, 'بدون مادة')] + [(m.id, m.title) for m in materials]

    if form.validate_on_submit():
        # التحقق من صحة الوقت
        try:
            start_time = form.start_time.data
            end_time = form.end_time.data

            # التحقق من تنسيق الوقت (HH:MM)
            if not re.match(r'^([01]\d|2[0-3]):([0-5]\d)$', start_time) or not re.match(r'^([01]\d|2[0-3]):([0-5]\d)$', end_time):
                flash('تنسيق الوقت غير صحيح. يجب أن يكون بتنسيق HH:MM', 'danger')
                return render_template('edit_schedule_item.html', title='تعديل الفترة',
                                      form=form, schedule_item=schedule_item, course=course)

            # التحقق من أن وقت البدء قبل وقت الانتهاء
            start_hour, start_minute = map(int, start_time.split(':'))
            end_hour, end_minute = map(int, end_time.split(':'))

            start_minutes = start_hour * 60 + start_minute
            end_minutes = end_hour * 60 + end_minute

            if start_minutes >= end_minutes:
                flash('يجب أن يكون وقت البدء قبل وقت الانتهاء', 'danger')
                return render_template('edit_schedule_item.html', title='تعديل الفترة',
                                      form=form, schedule_item=schedule_item, course=course)

            # تحديث بيانات الفترة
            schedule_item.day_number = int(form.day_number.data)
            schedule_item.start_time = start_time
            schedule_item.end_time = end_time
            schedule_item.title = form.title.data
            schedule_item.description = form.description.data
            schedule_item.is_break = form.is_break.data
            schedule_item.material_id = form.material_id.data if form.material_id.data != 0 else None

            db.session.commit()

            flash('تم تحديث الفترة بنجاح!', 'success')
            return redirect(url_for('course_schedule', course_id=course_id))

        except Exception as e:
            flash(f'حدث خطأ أثناء تحديث الفترة: {str(e)}', 'danger')

    # ملء النموذج ببيانات الفترة الحالية
    elif request.method == 'GET':
        form.day_number.data = str(schedule_item.day_number)
        form.start_time.data = schedule_item.start_time
        form.end_time.data = schedule_item.end_time
        form.title.data = schedule_item.title
        form.description.data = schedule_item.description
        form.is_break.data = schedule_item.is_break
        form.material_id.data = schedule_item.material_id if schedule_item.material_id else 0

    return render_template('edit_schedule_item.html', title='تعديل الفترة',
                          form=form, schedule_item=schedule_item, course=course)

@app.route('/schedule/<int:schedule_id>/delete')
@login_required
def delete_schedule_item(schedule_id):
    # جلب الفترة
    schedule_item = CourseSchedule.query.get_or_404(schedule_id)
    course_id = schedule_item.course_id
    course = Course.query.get_or_404(course_id)

    # التحقق من أن المستخدم هو مدرب الدورة أو مدير
    if current_user.id != course.trainer_id and current_user.role != 'admin':
        flash('ليس لديك صلاحية لحذف هذه الفترة', 'danger')
        return redirect(url_for('course_schedule', course_id=course_id))

    # حذف الفترة
    db.session.delete(schedule_item)
    db.session.commit()

    flash('تم حذف الفترة بنجاح!', 'success')
    return redirect(url_for('course_schedule', course_id=course_id))

@app.route('/material/<int:material_id>/view')
@login_required
def view_material(material_id):
    # جلب المادة التعليمية
    material = Material.query.get_or_404(material_id)

    # التحقق من نوع الملف
    if material.file_type == 'video':
        return render_template('video_player.html', title=material.title, material=material)
    elif material.file_type in ['pdf', 'image']:
        return redirect(url_for('view_file', filename=material.file_path))
    else:
        flash('لا يمكن عرض هذا النوع من الملفات مباشرة', 'warning')
        return redirect(url_for('course_details', course_id=material.course_id))

# مسارات البيانات الشخصية
@app.route('/personal_data')
@login_required
def personal_data_list():
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # جلب البيانات من جدول person_data الصحيح
    try:
        # إحصائيات عامة
        total_persons = PersonData.query.count()

        # توزيع الدورات حسب مسارات التدريب
        course_path_stats = db.session.query(
            CoursePath.name,
            db.func.count(Course.id).label('count')
        ).outerjoin(Course, Course.path_id == CoursePath.id).group_by(CoursePath.name).all()

        # توزيع المراكز حسب الجهات
        center_agency_stats = db.session.query(
            Agency.name,
            db.func.count(TrainingCenter.id).label('count')
        ).outerjoin(TrainingCenter, TrainingCenter.agency_id == Agency.id).group_by(Agency.name).all()

        # توزيع الدورات حسب المواقع (مراكز التدريب)
        course_location_stats = db.session.query(
            TrainingCenter.name,
            db.func.count(Course.id).label('count')
        ).outerjoin(Course, Course.center_id == TrainingCenter.id).group_by(TrainingCenter.name).all()

        # توزيع المشاركين حسب أنواع المشاركين (بناءً على حقل job في PersonData)
        participant_type_stats = db.session.query(
            PersonData.job,
            db.func.count(CourseParticipant.id).label('count')
        ).join(CourseParticipant, CourseParticipant.personal_data_id == PersonData.id)\
         .filter(PersonData.job.isnot(None))\
         .group_by(PersonData.job).all()

        # توزيع المشاركين حسب تصنيفات القوة (بناءً على حقل agency في PersonData)
        force_classification_stats = db.session.query(
            PersonData.agency,
            db.func.count(CourseParticipant.id).label('count')
        ).join(CourseParticipant, CourseParticipant.personal_data_id == PersonData.id)\
         .filter(PersonData.agency.isnot(None))\
         .group_by(PersonData.agency).all()

        # أحدث الأشخاص المضافين
        recent_persons = PersonData.query.order_by(PersonData.id.desc()).limit(10).all()

        return render_template('person_data_dashboard.html',
                             title='الملتحقين',
                             total_persons=total_persons,
                             course_path_stats=course_path_stats,
                             center_agency_stats=center_agency_stats,
                             course_location_stats=course_location_stats,
                             participant_type_stats=participant_type_stats,
                             force_classification_stats=force_classification_stats,
                             recent_persons=recent_persons)
    except Exception as e:
        flash(f'حدث خطأ في جلب البيانات: {str(e)}', 'danger')
        return redirect(url_for('dashboard'))

@app.route('/personal_data/table')
@login_required
def personal_data_table():
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # جلب جميع البيانات من جدول person_data
    try:
        # معاملات البحث والتصفية
        search = request.args.get('search', '')
        page = request.args.get('page', 1, type=int)
        per_page = 20

        # بناء الاستعلام - استخدام PersonData (جدول person_data)
        query = PersonData.query

        if search:
            query = query.filter(
                db.or_(
                    PersonData.full_name.contains(search),
                    PersonData.phone.contains(search),
                    PersonData.national_number.contains(search),
                    PersonData.governorate.contains(search)
                )
            )

        # تطبيق التصفح
        persons = query.paginate(
            page=page,
            per_page=per_page,
            error_out=False
        )

        print(f"🔍 تم جلب {persons.total} شخص من جدول person_data")

        return render_template('person_data_table_new.html',
                             title='جدول الملتحقين',
                             persons=persons,
                             search=search)
    except Exception as e:
        print(f"❌ خطأ في جلب البيانات: {str(e)}")
        flash(f'حدث خطأ في جلب البيانات: {str(e)}', 'danger')
        return redirect(url_for('dashboard'))

@app.route('/personal_data/add', methods=['GET', 'POST'])
@login_required
def add_personal_data():
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    form = PersonalDataForm()

    # تحديث خيارات القوائم المنسدلة
    form.marital_status_id.choices = [(m.id, m.name) for m in MaritalStatus.query.all()]
    form.residence_governorate_id.choices = [(g.id, g.name) for g in Governorate.query.all()]
    form.residence_directorate_id.choices = [(0, 'اختر المديرية')]
    form.residence_village_id.choices = [(0, 'اختر الحي/القرية')]
    form.qualification_type_id.choices = [(q.id, q.name) for q in QualificationType.query.all()]
    form.specialization_id.choices = [(s.id, s.name) for s in Specialization.query.all()]

    # إضافة الجهات من جدول Agency بدلاً من WorkPlace
    agencies = Agency.query.all()
    form.work_place_id.choices = [(a.id, a.name) for a in agencies]

    form.work_governorate_id.choices = [(g.id, g.name) for g in Governorate.query.all()]
    form.work_directorate_id.choices = [(0, 'اختر المديرية')]
    form.work_village_id.choices = [(0, 'اختر الحي/القرية')]
    form.assignment_type_id.choices = [(a.id, a.name) for a in AssignmentType.query.all()]
    form.assignment_authority_id.choices = [(a.id, a.name) for a in IssuingAuthority.query.all()]
    form.blood_type_id.choices = [(b.id, b.name) for b in BloodType.query.all()]
    form.military_rank_id.choices = [(m.id, m.name) for m in MilitaryRank.query.all()]
    form.injury_type_id.choices = [(i.id, i.name) for i in InjuryType.query.all()]
    form.injury_cause_id.choices = [(i.id, i.name) for i in InjuryCause.query.all()]
    form.issuing_authority_id.choices = [(a.id, a.name) for a in IssuingAuthority.query.all()]

    if form.validate_on_submit():
        # إنشاء بيانات شخصية جديدة
        personal_data = PersonalData(
            user_id=current_user.id,
            full_name=form.full_name.data,
            nickname=form.nickname.data,
            triple_number=form.triple_number.data,
            national_number=form.national_number.data,
            birth_date=form.birth_date.data,
            age=form.age.data,
            issue_date=form.issue_date.data,
            issuing_authority_id=form.issuing_authority_id.data,
            children_count=form.children_count.data,
            marital_status_id=form.marital_status_id.data,
            residence_governorate_id=form.residence_governorate_id.data,
            residence_directorate_id=form.residence_directorate_id.data,
            residence_village_id=form.residence_village_id.data,
            residence_house=form.residence_house.data,
            residence_type=form.residence_type.data,
            phone_yemen_mobile=form.phone_yemen_mobile.data,
            phone_you=form.phone_you.data,
            phone_sayyon=form.phone_sayyon.data,
            phone_landline=form.phone_landline.data,
            qualification_type_id=form.qualification_type_id.data,
            specialization_id=form.specialization_id.data,
            qualification_place=form.qualification_place.data,
            qualification_date=form.qualification_date.data,
            work_place_id=form.work_place_id.data,
            job_title=form.job_title.data,
            work_governorate_id=form.work_governorate_id.data,
            work_directorate_id=form.work_directorate_id.data,
            work_village_id=form.work_village_id.data,
            work_unit=form.work_unit.data,
            assignment_type_id=form.assignment_type_id.data,
            assignment_date=form.assignment_date.data,
            assignment_authority_id=form.assignment_authority_id.data,
            military_number=form.military_number.data,
            blood_type_id=form.blood_type_id.data,
            is_fighter=form.is_fighter.data,
            weapon_ownership=form.weapon_ownership.data,
            military_rank_id=form.military_rank_id.data,
            rank_date=form.rank_date.data,
            health_status=form.health_status.data,
            injury_type_id=form.injury_type_id.data,
            injury_cause_id=form.injury_cause_id.data,
            injury_place=form.injury_place.data,
            injury_date=form.injury_date.data,
            injury_body_location=form.injury_body_location.data,
            injury_disability=form.injury_disability.data,
            injury_hinders_work=form.injury_hinders_work.data,
            injury_authority=form.injury_authority.data,
            notes=form.notes.data
        )

        db.session.add(personal_data)
        db.session.commit()

        flash('تم إضافة البيانات الشخصية بنجاح!', 'success')
        return redirect(url_for('personal_data_list'))

    return render_template('personal_data/add.html', title='إضافة بيانات شخصية', form=form)

@app.route('/personal_data/new', methods=['GET', 'POST'])
@login_required
def add_personal_data_new():
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # استخدام نموذج مبسط للبيانات الشخصية
    class SimplePersonalDataForm(FlaskForm):
        full_name = StringField('الاسم الكامل', validators=[DataRequired(), Length(min=2, max=100)])
        national_number = StringField('الرقم الوطني', validators=[DataRequired(), Length(min=5, max=20)])
        birth_date = DateField('تاريخ الميلاد', format='%Y-%m-%d', validators=[Optional()])
        marital_status_id = SelectField('الحالة الاجتماعية', coerce=int, validators=[Optional()])
        blood_type_id = SelectField('فصيلة الدم', coerce=int, validators=[Optional()])
        phone_yemen_mobile = StringField('رقم الهاتف', validators=[Optional(), Length(max=20)])
        work_place_id = SelectField('جهة العمل', coerce=int, validators=[Optional()])
        submit = SubmitField('حفظ')

    form = SimplePersonalDataForm()

    # تحديث خيارات القوائم المنسدلة
    form.marital_status_id.choices = [(m.id, m.name) for m in MaritalStatus.query.all()]
    form.blood_type_id.choices = [(b.id, b.name) for b in BloodType.query.all()]
    form.work_place_id.choices = [(a.id, a.name) for a in Agency.query.all()]

    if form.validate_on_submit():
        # إنشاء بيانات شخصية جديدة (مبسطة)
        personal_data = PersonalData(
            user_id=current_user.id,
            full_name=form.full_name.data,
            national_number=form.national_number.data,
            birth_date=form.birth_date.data,
            marital_status_id=form.marital_status_id.data,
            blood_type_id=form.blood_type_id.data,
            phone_yemen_mobile=form.phone_yemen_mobile.data,
            work_place_id=form.work_place_id.data
        )

        db.session.add(personal_data)
        db.session.commit()

        flash('تم إضافة البيانات الشخصية بنجاح!', 'success')
        return redirect(url_for('personal_data_list'))

    return render_template('personal_data/new_add.html', title='إضافة بيانات شخصية', form=form)

@app.route('/personal_data/excel', methods=['GET', 'POST'])
@login_required
def personal_data_excel():
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # إعادة توجيه إلى صفحة إدارة البيانات الجديدة
    return redirect(url_for('person_data.person_data_excel'))

    # إنشاء نموذج لاستيراد البيانات من ملف إكسل
    class ExcelImportForm(FlaskForm):
        excel_file = FileField('ملف الإكسل', validators=[
            FileAllowed(['xlsx', 'xls'], 'يجب أن يكون الملف بصيغة Excel (.xlsx أو .xls)'),
            FileSize(max_size=10 * 1024 * 1024)  # 10 ميجابايت كحد أقصى
        ])
        sheet_name = StringField('اسم الورقة', validators=[Optional(), Length(max=50)])
        submit = SubmitField('استيراد')

    form = ExcelImportForm()

    # جلب البيانات الشخصية
    personal_data = PersonalData.query.all()

    # جلب البيانات المرجعية للقوائم المنسدلة
    qualification_types = QualificationType.query.all()
    agencies = Agency.query.all()
    military_ranks = MilitaryRank.query.all()

    return render_template('personal_data/excel_view.html',
                          title='إدارة البيانات الشخصية',
                          form=form,
                          personal_data=personal_data,
                          qualification_types=qualification_types,
                          agencies=agencies,
                          military_ranks=military_ranks)

@app.route('/personal_data/excel/advanced', methods=['GET'])
@login_required
def personal_data_excel_advanced():
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # إنشاء نموذج لاستيراد البيانات من ملف إكسل
    class ExcelImportForm(FlaskForm):
        excel_file = FileField('ملف الإكسل', validators=[
            FileAllowed(['xlsx', 'xls'], 'يجب أن يكون الملف بصيغة Excel (.xlsx أو .xls)'),
            FileSize(max_size=10 * 1024 * 1024)  # 10 ميجابايت كحد أقصى
        ])
        sheet_name = StringField('اسم الورقة', validators=[Optional(), Length(max=50)])
        submit = SubmitField('استيراد')

    form = ExcelImportForm()

    # جلب البيانات المرجعية للقوائم المنسدلة
    qualification_types = QualificationType.query.all()
    agencies = Agency.query.all()
    marital_statuses = MaritalStatus.query.all()

    return render_template('personal_data/advanced_excel_view.html',
                          title='إدارة البيانات الشخصية المتقدمة',
                          form=form,
                          qualification_types=qualification_types,
                          agencies=agencies,
                          marital_statuses=marital_statuses)

@app.route('/personal_data/excel/add', methods=['POST'])
@login_required
# @csrf.exempt  # استثناء حماية CSRF لهذا المسار - تم تعطيله مؤقتاً
def add_personal_data_excel():
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        return jsonify({'success': False, 'message': 'ليس لديك صلاحية للوصول إلى هذه الصفحة'})

    # الحصول على البيانات من الطلب
    data = request.form

    try:
        # طباعة البيانات المستلمة للتشخيص
        print("البيانات المستلمة للإضافة:", data)

        # التحقق من وجود البيانات المطلوبة
        if not data:
            return jsonify({'success': False, 'message': 'لم يتم استلام أي بيانات'})

        if not data.get('full_name'):
            return jsonify({'success': False, 'message': 'الاسم الشخصي مطلوب'})

        # معالجة القيم النصية "None"
        military_number = data.get('military_number', '')
        if military_number == "None" or military_number == "none" or military_number == "null" or military_number == "":
            military_number = None

        work_place = data.get('work_place', '')
        if work_place == "None" or work_place == "none" or work_place == "null" or work_place == "":
            work_place = None

        # معالجة القيم الفارغة
        nickname = data.get('nickname', '')
        if nickname == "":
            nickname = None

        age = data.get('age', '')
        if age == "":
            age = None

        work_number = data.get('work_number', '')
        if work_number == "":
            work_number = None

        work_rank = data.get('work_rank', '')
        if work_rank == "":
            work_rank = None

        job_title = data.get('job', '')
        if job_title == "":
            job_title = None

        phone = data.get('phone', '')
        if phone == "":
            phone = None

        national_number = data.get('national_number', '')
        if national_number == "":
            national_number = None

        # معالجة الحقول المرتبطة بالجداول الترميزية كنصوص بسيطة
        governorate_text = data.get('governorate', '')
        directorate_text = data.get('directorate', '')
        uzla_text = data.get('uzla', '')  # إضافة حقل العزلة
        village_text = data.get('village', '')
        qualification_text = data.get('qualification', '')
        agency_text = data.get('agency', '')
        marital_status_text = data.get('marital_status', '')

        # البحث عن المعرفات المقابلة للنصوص (إذا وجدت)
        governorate_id = None
        if governorate_text:
            # البحث عن المحافظة بالاسم المطابق تماماً
            governorate = Governorate.query.filter(Governorate.name == governorate_text).first()

            # إذا لم يتم العثور على المحافظة، نحاول البحث بشكل جزئي
            if not governorate:
                governorate = Governorate.query.filter(Governorate.name.like(f'%{governorate_text}%')).first()

            # إذا لم يتم العثور على المحافظة، نقوم بإنشاء محافظة جديدة
            if not governorate:
                try:
                    print(f"إنشاء محافظة جديدة: '{governorate_text}'")
                    governorate = Governorate(name=governorate_text, code=governorate_text[:10] if len(governorate_text) > 10 else governorate_text)
                    db.session.add(governorate)
                    db.session.flush()  # للحصول على معرف المحافظة الجديدة
                    governorate_id = governorate.id
                    print(f"تم إنشاء محافظة جديدة: {governorate.name} (ID: {governorate.id})")
                except Exception as e:
                    print(f"خطأ في إنشاء المحافظة: {str(e)}")
            else:
                governorate_id = governorate.id
                print(f"تم العثور على المحافظة: {governorate.name} (ID: {governorate.id})")

        directorate_id = None
        if directorate_text:
            # البحث عن المديرية بالاسم المطابق تماماً
            directorate = Directorate.query.filter(Directorate.name == directorate_text).first()

            # إذا لم يتم العثور على المديرية، نحاول البحث بشكل جزئي
            if not directorate:
                directorate = Directorate.query.filter(Directorate.name.like(f'%{directorate_text}%')).first()

            # إذا لم يتم العثور على المديرية، نقوم بإنشاء مديرية جديدة
            if not directorate:
                try:
                    print(f"إنشاء مديرية جديدة: '{directorate_text}'")
                    directorate = Directorate(name=directorate_text, governorate_id=governorate_id, code=directorate_text[:10] if len(directorate_text) > 10 else directorate_text)
                    db.session.add(directorate)
                    db.session.flush()  # للحصول على معرف المديرية الجديدة
                    directorate_id = directorate.id
                    print(f"تم إنشاء مديرية جديدة: {directorate.name} (ID: {directorate.id})")
                except Exception as e:
                    print(f"خطأ في إنشاء المديرية: {str(e)}")
            else:
                directorate_id = directorate.id
                print(f"تم العثور على المديرية: {directorate.name} (ID: {directorate.id})")

        village_id = None
        if village_text:
            # البحث عن القرية بالاسم المطابق تماماً
            village = Village.query.filter(Village.name == village_text).first()

            # إذا لم يتم العثور على القرية، نحاول البحث بشكل جزئي
            if not village:
                village = Village.query.filter(Village.name.like(f'%{village_text}%')).first()

            # إذا لم يتم العثور على القرية، نقوم بإنشاء قرية جديدة
            if not village:
                try:
                    print(f"إنشاء قرية جديدة: '{village_text}'")
                    village = Village(name=village_text, directorate_id=directorate_id)
                    db.session.add(village)
                    db.session.flush()  # للحصول على معرف القرية الجديدة
                    village_id = village.id
                    print(f"تم إنشاء قرية جديدة: {village.name} (ID: {village.id})")
                except Exception as e:
                    print(f"خطأ في إنشاء القرية: {str(e)}")
            else:
                village_id = village.id
                print(f"تم العثور على القرية: {village.name} (ID: {village.id})")

        qualification_type_id = None
        if qualification_text:
            # البحث عن المؤهل العلمي بالاسم المطابق تماماً
            qualification_type = QualificationType.query.filter(QualificationType.name == qualification_text).first()

            # إذا لم يتم العثور على المؤهل العلمي، نحاول البحث بشكل جزئي
            if not qualification_type:
                qualification_type = QualificationType.query.filter(QualificationType.name.like(f'%{qualification_text}%')).first()

            # إذا لم يتم العثور على المؤهل العلمي، نقوم بإنشاء مؤهل علمي جديد
            if not qualification_type:
                try:
                    print(f"إنشاء مؤهل علمي جديد: '{qualification_text}'")
                    qualification_type = QualificationType(name=qualification_text)
                    db.session.add(qualification_type)
                    db.session.flush()  # للحصول على معرف المؤهل العلمي الجديد
                    qualification_type_id = qualification_type.id
                    print(f"تم إنشاء مؤهل علمي جديد: {qualification_type.name} (ID: {qualification_type.id})")
                except Exception as e:
                    print(f"خطأ في إنشاء المؤهل العلمي: {str(e)}")
            else:
                qualification_type_id = qualification_type.id
                print(f"تم العثور على المؤهل العلمي: {qualification_type.name} (ID: {qualification_type.id})")

        agency_id = None
        if agency_text:
            # طباعة النص المدخل للإدارة للتشخيص
            print(f"البحث عن الإدارة: '{agency_text}'")

            # البحث عن الإدارة بالاسم المطابق تماماً
            agency = Agency.query.filter(Agency.name == agency_text).first()

            # إذا لم يتم العثور على الإدارة، نحاول البحث بشكل جزئي
            if not agency:
                agency = Agency.query.filter(Agency.name.like(f'%{agency_text}%')).first()

            # إذا لم يتم العثور على الإدارة، نقوم بإنشاء إدارة جديدة
            if not agency:
                try:
                    print(f"إنشاء إدارة جديدة: '{agency_text}'")
                    new_agency = Agency(name=agency_text, code=agency_text[:10] if len(agency_text) > 10 else agency_text)
                    db.session.add(new_agency)
                    db.session.flush()  # للحصول على معرف الإدارة الجديدة
                    agency_id = new_agency.id
                    print(f"تم إنشاء إدارة جديدة: {new_agency.name} (ID: {new_agency.id})")
                except Exception as e:
                    print(f"خطأ في إنشاء الإدارة: {str(e)}")
            else:
                agency_id = agency.id
                print(f"تم العثور على الإدارة: {agency.name} (ID: {agency.id})")

        print(f"معرف الإدارة النهائي: {agency_id}")

        marital_status_id = None
        if marital_status_text:
            # البحث عن الحالة الاجتماعية بالاسم المطابق تماماً
            marital_status = MaritalStatus.query.filter(MaritalStatus.name == marital_status_text).first()

            # إذا لم يتم العثور على الحالة الاجتماعية، نحاول البحث بشكل جزئي
            if not marital_status:
                marital_status = MaritalStatus.query.filter(MaritalStatus.name.like(f'%{marital_status_text}%')).first()

            # إذا لم يتم العثور على الحالة الاجتماعية، نقوم بإنشاء حالة اجتماعية جديدة
            if not marital_status:
                try:
                    print(f"إنشاء حالة اجتماعية جديدة: '{marital_status_text}'")
                    marital_status = MaritalStatus(name=marital_status_text)
                    db.session.add(marital_status)
                    db.session.flush()  # للحصول على معرف الحالة الاجتماعية الجديدة
                    marital_status_id = marital_status.id
                    print(f"تم إنشاء حالة اجتماعية جديدة: {marital_status.name} (ID: {marital_status.id})")
                except Exception as e:
                    print(f"خطأ في إنشاء الحالة الاجتماعية: {str(e)}")
            else:
                marital_status_id = marital_status.id
                print(f"تم العثور على الحالة الاجتماعية: {marital_status.name} (ID: {marital_status.id})")

        # إنشاء بيانات شخصية جديدة
        personal_data = PersonalData(
            user_id=current_user.id,
            full_name=data.get('full_name'),
            national_number=national_number,
            military_number=military_number,
            # تعيين الحقول الجديدة
            nickname=nickname,
            age=age,
            work_number=work_number,
            job_title=job_title,
            # استخدام work_place_text كحقل نصي مباشر وليس كمفتاح أجنبي
            work_place_text=work_place,  # استخدام اسم مختلف لتجنب التعارض
            work_rank=work_rank,
            phone_yemen_mobile=phone,
            # استخدام الحقول النصية المباشرة بدلاً من المفاتيح الأجنبية
            governorate=governorate_text,
            directorate=directorate_text,
            uzla=uzla_text,
            village=village_text,
            # استخدام الحقول النصية المباشرة للمؤهل العلمي والحالة الاجتماعية
            #qualification_text=qualification_type_text, # type: ignore
            marital_status_text=marital_status_text,
            # الاحتفاظ بالمفاتيح الأجنبية للتوافق مع البيانات الموجودة
            qualification_type_id=qualification_type_id,
            agency_id=agency_id,
            marital_status_id=marital_status_id
        )

        # طباعة قيم الحقول بعد الإضافة للتأكد
        print(f"بعد الإضافة - الحالة الاجتماعية ID: {personal_data.marital_status_id}")
        print(f"بعد الإضافة - المؤهل العلمي ID: {personal_data.qualification_type_id}")
        print(f"بعد الإضافة - الحي/القرية ID: {personal_data.village_id}")
        print(f"بعد الإضافة - العزلة: {personal_data.uzla}")

        db.session.add(personal_data)
        db.session.commit()

        return jsonify({'success': True, 'id': personal_data.id})
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': str(e)})

@app.route('/lookup/governorate', methods=['POST'])
@login_required
# @csrf.exempt
def lookup_governorate():
    """البحث عن معرف المحافظة بناءً على الاسم"""
    name = request.json.get('name', '')
    if not name:
        return jsonify({'id': None})

    governorate = Governorate.query.filter(Governorate.name.like(f'%{name}%')).first()
    if governorate:
        return jsonify({'id': governorate.id})
    return jsonify({'id': None})

@app.route('/lookup/directorate', methods=['POST'])
@login_required
# @csrf.exempt
def lookup_directorate():
    """البحث عن معرف المديرية بناءً على الاسم"""
    name = request.json.get('name', '')
    if not name:
        return jsonify({'id': None})

    directorate = Directorate.query.filter(Directorate.name.like(f'%{name}%')).first()
    if directorate:
        return jsonify({'id': directorate.id})
    return jsonify({'id': None})

@app.route('/lookup/village', methods=['POST'])
@login_required
# @csrf.exempt
def lookup_village():
    """البحث عن معرف القرية بناءً على الاسم"""
    name = request.json.get('name', '')
    if not name:
        return jsonify({'id': None})

    village = Village.query.filter(Village.name.like(f'%{name}%')).first()
    if village:
        return jsonify({'id': village.id})
    return jsonify({'id': None})

@app.route('/lookup/qualification', methods=['POST'])
@login_required
# @csrf.exempt
def lookup_qualification():
    """البحث عن معرف المؤهل العلمي بناءً على الاسم"""
    name = request.json.get('name', '')
    if not name:
        return jsonify({'id': None})

    qualification = QualificationType.query.filter(QualificationType.name.like(f'%{name}%')).first()
    if qualification:
        return jsonify({'id': qualification.id})
    return jsonify({'id': None})

@app.route('/lookup/agency', methods=['POST'])
@login_required
# @csrf.exempt
def lookup_agency():
    """البحث عن معرف الإدارة بناءً على الاسم"""
    name = request.json.get('name', '')
    if not name:
        return jsonify({'id': None})

    agency = Agency.query.filter(Agency.name.like(f'%{name}%')).first()
    if agency:
        return jsonify({'id': agency.id})
    return jsonify({'id': None})

@app.route('/lookup/marital_status', methods=['POST'])
@login_required
# @csrf.exempt
def lookup_marital_status():
    """البحث عن معرف الحالة الاجتماعية بناءً على الاسم"""
    name = request.json.get('name', '')
    if not name:
        return jsonify({'id': None})

    # افتراض أن الحالة الاجتماعية هي قيمة نصية مباشرة وليست جدول ترميزي
    # يمكن تعديل هذا الجزء إذا كانت الحالة الاجتماعية جدول ترميزي
    return jsonify({'id': name})

@app.route('/personal_data/excel/<int:personal_data_id>/update', methods=['POST'])
@login_required
# @csrf.exempt  # استثناء حماية CSRF لهذا المسار - تم تعطيله مؤقتاً
def update_personal_data_excel(personal_data_id):
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        return jsonify({'success': False, 'message': 'ليس لديك صلاحية للوصول إلى هذه الصفحة'})

    # الحصول على البيانات من الطلب
    data = request.form

    try:
        # البحث عن البيانات الشخصية
        personal_data = PersonalData.query.get_or_404(personal_data_id)

        # طباعة البيانات المستلمة للتشخيص
        print("البيانات المستلمة للتحديث:", data)
        print("نوع البيانات المستلمة:", type(data))
        for key, value in data.items():
            print(f"الحقل: {key}, القيمة: {value}, النوع: {type(value)}")

        # التحقق من وجود البيانات المطلوبة
        if not data:
            return jsonify({'success': False, 'message': 'لم يتم استلام أي بيانات'})

        if not data.get('full_name'):
            return jsonify({'success': False, 'message': 'الاسم الشخصي مطلوب'})

        # معالجة القيم النصية "None"
        military_number = data.get('military_number', '')
        if military_number == "None" or military_number == "none" or military_number == "null" or military_number == "":
            military_number = None

        work_place = data.get('work_place', '')
        if work_place == "None" or work_place == "none" or work_place == "null" or work_place == "":
            work_place = None

        # معالجة القيم الفارغة
        nickname = data.get('nickname', '')
        if nickname == "":
            nickname = None

        age = data.get('age', '')
        if age == "":
            age = None

        work_number = data.get('work_number', '')
        if work_number == "":
            work_number = None

        work_rank = data.get('work_rank', '')
        if work_rank == "":
            work_rank = None

        job_title = data.get('job', '')
        if job_title == "":
            job_title = None

        phone = data.get('phone', '')
        if phone == "":
            phone = None

        national_number = data.get('national_number', '')
        if national_number == "":
            national_number = None

        # معالجة الحقول المرتبطة بالجداول الترميزية كنصوص بسيطة
        governorate_text = data.get('governorate', '')
        directorate_text = data.get('directorate', '')
        uzla_text = data.get('uzla', '')
        village_text = data.get('village', '')
        qualification_text = data.get('qualification', '')
        agency_text = data.get('agency', '')
        marital_status_text = data.get('marital_status', '')

        # طباعة قيم الحقول المشكلة للتشخيص
        print(f"الحالة الاجتماعية: '{marital_status_text}'")
        print(f"المؤهل العلمي: '{qualification_text}'")
        print(f"الحي/القرية: '{village_text}'")
        print(f"العزلة: '{uzla_text}'")

        # البحث عن المعرفات المقابلة للنصوص (إذا وجدت)
        governorate_id = personal_data.governorate_id
        if governorate_text:
            # البحث عن المحافظة بالاسم المطابق تماماً
            governorate = Governorate.query.filter(Governorate.name == governorate_text).first()

            # إذا لم يتم العثور على المحافظة، نحاول البحث بشكل جزئي
            if not governorate:
                governorate = Governorate.query.filter(Governorate.name.like(f'%{governorate_text}%')).first()

            # إذا لم يتم العثور على المحافظة، نقوم بإنشاء محافظة جديدة
            if not governorate:
                try:
                    print(f"إنشاء محافظة جديدة: '{governorate_text}'")
                    governorate = Governorate(name=governorate_text)
                    db.session.add(governorate)
                    db.session.flush()  # للحصول على معرف المحافظة الجديدة
                    governorate_id = governorate.id
                    print(f"تم إنشاء محافظة جديدة: {governorate.name} (ID: {governorate.id})")
                except Exception as e:
                    print(f"خطأ في إنشاء المحافظة: {str(e)}")
            else:
                governorate_id = governorate.id
                print(f"تم العثور على المحافظة: {governorate.name} (ID: {governorate.id})")

        directorate_id = personal_data.directorate_id
        if directorate_text:
            # البحث عن المديرية بالاسم المطابق تماماً
            directorate = Directorate.query.filter(Directorate.name == directorate_text).first()

            # إذا لم يتم العثور على المديرية، نحاول البحث بشكل جزئي
            if not directorate:
                directorate = Directorate.query.filter(Directorate.name.like(f'%{directorate_text}%')).first()

            # إذا لم يتم العثور على المديرية، نقوم بإنشاء مديرية جديدة
            if not directorate:
                try:
                    print(f"إنشاء مديرية جديدة: '{directorate_text}'")
                    directorate = Directorate(name=directorate_text, governorate_id=governorate_id)
                    db.session.add(directorate)
                    db.session.flush()  # للحصول على معرف المديرية الجديدة
                    directorate_id = directorate.id
                    print(f"تم إنشاء مديرية جديدة: {directorate.name} (ID: {directorate.id})")
                except Exception as e:
                    print(f"خطأ في إنشاء المديرية: {str(e)}")
            else:
                directorate_id = directorate.id
                print(f"تم العثور على المديرية: {directorate.name} (ID: {directorate.id})")

        village_id = personal_data.village_id
        if village_text:
            # البحث عن القرية بالاسم المطابق تماماً
            village = Village.query.filter(Village.name == village_text).first()

            # إذا لم يتم العثور على القرية، نحاول البحث بشكل جزئي
            if not village:
                village = Village.query.filter(Village.name.like(f'%{village_text}%')).first()

            # إذا لم يتم العثور على القرية، نقوم بإنشاء قرية جديدة
            if not village:
                try:
                    print(f"إنشاء قرية جديدة: '{village_text}'")
                    village = Village(name=village_text, directorate_id=directorate_id)
                    db.session.add(village)
                    db.session.flush()  # للحصول على معرف القرية الجديدة
                    village_id = village.id
                    print(f"تم إنشاء قرية جديدة: {village.name} (ID: {village.id})")
                except Exception as e:
                    print(f"خطأ في إنشاء القرية: {str(e)}")
            else:
                village_id = village.id
                print(f"تم العثور على القرية: {village.name} (ID: {village.id})")

        qualification_type_id = personal_data.qualification_type_id
        if qualification_text:
            # البحث عن المؤهل العلمي بالاسم المطابق تماماً
            qualification_type = QualificationType.query.filter(QualificationType.name == qualification_text).first()

            # إذا لم يتم العثور على المؤهل العلمي، نحاول البحث بشكل جزئي
            if not qualification_type:
                qualification_type = QualificationType.query.filter(QualificationType.name.like(f'%{qualification_text}%')).first()

            # إذا لم يتم العثور على المؤهل العلمي، نقوم بإنشاء مؤهل علمي جديد
            if not qualification_type:
                try:
                    print(f"إنشاء مؤهل علمي جديد: '{qualification_text}'")
                    qualification_type = QualificationType(name=qualification_text)
                    db.session.add(qualification_type)
                    db.session.flush()  # للحصول على معرف المؤهل العلمي الجديد
                    qualification_type_id = qualification_type.id
                    print(f"تم إنشاء مؤهل علمي جديد: {qualification_type.name} (ID: {qualification_type.id})")
                except Exception as e:
                    print(f"خطأ في إنشاء المؤهل العلمي: {str(e)}")
            else:
                qualification_type_id = qualification_type.id
                print(f"تم العثور على المؤهل العلمي: {qualification_type.name} (ID: {qualification_type.id})")

        agency_id = personal_data.agency_id
        if agency_text:
            # طباعة النص المدخل للإدارة للتشخيص
            print(f"البحث عن الإدارة: '{agency_text}'")

            # البحث عن الإدارة بالاسم المطابق تماماً
            agency = Agency.query.filter(Agency.name == agency_text).first()

            # إذا لم يتم العثور على الإدارة، نحاول البحث بشكل جزئي
            if not agency:
                agency = Agency.query.filter(Agency.name.like(f'%{agency_text}%')).first()

            # إذا لم يتم العثور على الإدارة، نقوم بإنشاء إدارة جديدة
            if not agency:
                try:
                    print(f"إنشاء إدارة جديدة: '{agency_text}'")
                    new_agency = Agency(name=agency_text, code=agency_text[:10] if len(agency_text) > 10 else agency_text)
                    db.session.add(new_agency)
                    db.session.flush()  # للحصول على معرف الإدارة الجديدة
                    agency_id = new_agency.id
                    print(f"تم إنشاء إدارة جديدة: {new_agency.name} (ID: {new_agency.id})")
                except Exception as e:
                    print(f"خطأ في إنشاء الإدارة: {str(e)}")
            else:
                agency_id = agency.id
                print(f"تم العثور على الإدارة: {agency.name} (ID: {agency.id})")

        print(f"معرف الإدارة النهائي: {agency_id}")

        marital_status_id = personal_data.marital_status_id
        if marital_status_text:
            # البحث عن الحالة الاجتماعية بالاسم المطابق تماماً
            marital_status = MaritalStatus.query.filter(MaritalStatus.name == marital_status_text).first()

            # إذا لم يتم العثور على الحالة الاجتماعية، نحاول البحث بشكل جزئي
            if not marital_status:
                marital_status = MaritalStatus.query.filter(MaritalStatus.name.like(f'%{marital_status_text}%')).first()

            # إذا لم يتم العثور على الحالة الاجتماعية، نقوم بإنشاء حالة اجتماعية جديدة
            if not marital_status:
                try:
                    print(f"إنشاء حالة اجتماعية جديدة: '{marital_status_text}'")
                    marital_status = MaritalStatus(name=marital_status_text)
                    db.session.add(marital_status)
                    db.session.flush()  # للحصول على معرف الحالة الاجتماعية الجديدة
                    marital_status_id = marital_status.id
                    print(f"تم إنشاء حالة اجتماعية جديدة: {marital_status.name} (ID: {marital_status.id})")
                except Exception as e:
                    print(f"خطأ في إنشاء الحالة الاجتماعية: {str(e)}")
            else:
                marital_status_id = marital_status.id
                print(f"تم العثور على الحالة الاجتماعية: {marital_status.name} (ID: {marital_status.id})")

        # تحديث البيانات
        personal_data.full_name = data.get('full_name')
        personal_data.national_number = national_number
        personal_data.military_number = military_number
        # تحديث الحقول الجديدة
        personal_data.work_number = work_number
        personal_data.nickname = nickname
        personal_data.age = age
        personal_data.job_title = job_title
        # استخدام work_place_text كحقل نصي مباشر وليس كمفتاح أجنبي
        personal_data.work_place_text = work_place
        personal_data.work_rank = work_rank
        personal_data.phone_yemen_mobile = phone
        # استخدام الحقول النصية المباشرة بدلاً من المفاتيح الأجنبية
        personal_data.governorate = governorate_text
        personal_data.directorate = directorate_text
        personal_data.uzla = uzla_text
        personal_data.village = village_text
        # استخدام الحقول النصية المباشرة للمؤهل العلمي والحالة الاجتماعية
        personal_data.qualification_text = qualification_text
        personal_data.marital_status_text = marital_status_text
        # الاحتفاظ بالمفاتيح الأجنبية للتوافق مع البيانات الموجودة
        personal_data.qualification_type_id = qualification_type_id
        personal_data.agency_id = agency_id
        personal_data.marital_status_id = marital_status_id

        # طباعة قيم الحقول بعد التحديث للتأكد
        print(f"بعد التحديث - الحالة الاجتماعية ID: {personal_data.marital_status_id}")
        print(f"بعد التحديث - المؤهل العلمي ID: {personal_data.qualification_type_id}")
        print(f"بعد التحديث - الحي/القرية ID: {personal_data.village_id}")
        print(f"بعد التحديث - العزلة: {personal_data.uzla}")

        db.session.commit()

        return jsonify({'success': True})
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': str(e)})

@app.route('/personal_data/excel/devextreme')
@login_required
def personal_data_excel_devextreme():
    """
    عرض واجهة متطورة لإدارة البيانات الشخصية باستخدام DevExtreme
    """
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    return render_template(
        'personal_data/devextreme_view.html',
        title='إدارة البيانات الشخصية - واجهة متطورة'
    )

@app.route('/personal_data/excel/<int:personal_data_id>/delete', methods=['POST'])
@login_required
# @csrf.exempt  # استثناء حماية CSRF لهذا المسار - تم تعطيله مؤقتاً
def delete_personal_data_excel(personal_data_id):
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        return jsonify({'success': False, 'message': 'ليس لديك صلاحية للوصول إلى هذه الصفحة'})

    try:
        # البحث عن البيانات الشخصية
        personal_data = PersonalData.query.get_or_404(personal_data_id)

        # حذف البيانات
        db.session.delete(personal_data)
        db.session.commit()

        return jsonify({'success': True})
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': str(e)})

@app.route('/personal_data/excel/save_all', methods=['POST'])
@login_required
# @csrf.exempt  # استثناء حماية CSRF لهذا المسار - تم تعطيله مؤقتاً
def save_all_personal_data_excel():
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        return jsonify({'success': False, 'message': 'ليس لديك صلاحية للوصول إلى هذه الصفحة'})

    # الحصول على البيانات من الطلب
    data = request.json.get('data', [])

    try:
        new_ids = []

        for item in data:
            id_value = item.get('id')

            if id_value == 'new':
                # معالجة القيم النصية "None"
                military_number = item.get('military_number')
                if military_number == "None" or military_number == "none" or military_number == "null":
                    military_number = None

                work_place = item.get('work_place')
                if work_place == "None" or work_place == "none" or work_place == "null":
                    work_place = None

                # معالجة القيم الفارغة
                nickname = item.get('nickname')
                if nickname == "":
                    nickname = None

                age = item.get('age')
                if age == "":
                    age = None

                work_number = item.get('work_number')
                if work_number == "":
                    work_number = None

                work_rank = item.get('work_rank')
                if work_rank == "":
                    work_rank = None

                # معالجة الحقول المرتبطة بالجداول الترميزية
                governorate_id = item.get('governorate_id', '')
                if governorate_id == "":
                    governorate_id = None
                else:
                    try:
                        governorate_id = int(governorate_id)
                    except:
                        governorate_id = None

                directorate_id = item.get('directorate_id', '')
                if directorate_id == "":
                    directorate_id = None
                else:
                    try:
                        directorate_id = int(directorate_id)
                    except:
                        directorate_id = None

                village_id = item.get('village_id', '')
                if village_id == "":
                    village_id = None
                else:
                    try:
                        village_id = int(village_id)
                    except:
                        village_id = None

                qualification_type_id = item.get('qualification_type_id', '')
                if qualification_type_id == "":
                    qualification_type_id = None
                else:
                    try:
                        qualification_type_id = int(qualification_type_id)
                    except:
                        qualification_type_id = None

                agency_id = item.get('agency_id', '')
                if agency_id == "":
                    agency_id = None
                else:
                    try:
                        agency_id = int(agency_id)
                    except:
                        agency_id = None

                marital_status_id = item.get('marital_status_id', '')
                if marital_status_id == "":
                    marital_status_id = None
                else:
                    try:
                        marital_status_id = int(marital_status_id)
                    except:
                        marital_status_id = None

                # إنشاء بيانات شخصية جديدة
                personal_data = PersonalData(
                    user_id=current_user.id,
                    full_name=item.get('full_name'),
                    national_number=item.get('national_number'),
                    military_number=military_number,
                    # تعيين الحقول الجديدة
                    nickname=nickname,
                    age=age,
                    work_number=work_number,
                    job_title=item.get('job'),
                    # استخدام work_place_text كحقل نصي مباشر وليس كمفتاح أجنبي
                    work_place_text=work_place,
                    work_rank=work_rank,
                    phone_yemen_mobile=item.get('phone'),
                    # استخدام الحقول النصية المباشرة بدلاً من المفاتيح الأجنبية
                    governorate=item.get('governorate', ''),
                    directorate=item.get('directorate', ''),
                    uzla=item.get('uzla', ''),
                    village=item.get('village', ''),
                    # الاحتفاظ بالمفاتيح الأجنبية للمؤهل العلمي والإدارة والحالة الاجتماعية
                    qualification_type_id=qualification_type_id,
                    agency_id=agency_id,
                    marital_status_id=marital_status_id
                )

                db.session.add(personal_data)
                db.session.flush()  # للحصول على معرف السجل الجديد

                new_ids.append({'old_id': 'new', 'new_id': personal_data.id})
            else:
                # البحث عن البيانات الشخصية
                personal_data = PersonalData.query.get(id_value)

                if personal_data:
                    # معالجة القيم النصية "None"
                    military_number = item.get('military_number')
                    if military_number == "None" or military_number == "none" or military_number == "null":
                        military_number = None

                    work_place = item.get('work_place')
                    if work_place == "None" or work_place == "none" or work_place == "null":
                        work_place = None

                    # معالجة القيم الفارغة
                    nickname = item.get('nickname')
                    if nickname == "":
                        nickname = None

                    age = item.get('age')
                    if age == "":
                        age = None

                    work_number = item.get('work_number')
                    if work_number == "":
                        work_number = None

                    work_rank = item.get('work_rank')
                    if work_rank == "":
                        work_rank = None

                    # معالجة الحقول المرتبطة بالجداول الترميزية
                    # إذا تم تقديم قيمة جديدة، استخدمها، وإلا احتفظ بالقيمة الحالية

                    # المحافظة
                    if 'governorate_id' in item and item['governorate_id']:
                        try:
                            governorate_id = int(item['governorate_id'])
                        except:
                            governorate_id = personal_data.governorate_id
                    else:
                        governorate_id = personal_data.governorate_id

                    # المديرية
                    if 'directorate_id' in item and item['directorate_id']:
                        try:
                            directorate_id = int(item['directorate_id'])
                        except:
                            directorate_id = personal_data.directorate_id
                    else:
                        directorate_id = personal_data.directorate_id

                    # القرية
                    if 'village_id' in item and item['village_id']:
                        try:
                            village_id = int(item['village_id'])
                        except:
                            village_id = personal_data.village_id
                    else:
                        village_id = personal_data.village_id

                    # المؤهل العلمي
                    if 'qualification_type_id' in item and item['qualification_type_id']:
                        try:
                            qualification_type_id = int(item['qualification_type_id'])
                        except:
                            qualification_type_id = personal_data.qualification_type_id
                    else:
                        qualification_type_id = personal_data.qualification_type_id

                    # الإدارة
                    if 'agency_id' in item and item['agency_id']:
                        try:
                            agency_id = int(item['agency_id'])
                        except:
                            agency_id = personal_data.agency_id
                    else:
                        agency_id = personal_data.agency_id

                    # الحالة الاجتماعية
                    if 'marital_status_id' in item and item['marital_status_id']:
                        try:
                            marital_status_id = int(item['marital_status_id'])
                        except:
                            marital_status_id = personal_data.marital_status_id
                    else:
                        marital_status_id = personal_data.marital_status_id

                    # تحديث البيانات
                    personal_data.full_name = item.get('full_name')
                    personal_data.national_number = item.get('national_number')
                    personal_data.military_number = military_number
                    # تحديث الحقول الجديدة
                    personal_data.nickname = nickname
                    personal_data.age = age
                    personal_data.work_number = work_number
                    personal_data.job_title = item.get('job')
                    # استخدام work_place_text كحقل نصي مباشر وليس كمفتاح أجنبي
                    personal_data.work_place_text = work_place
                    personal_data.work_rank = work_rank
                    personal_data.phone_yemen_mobile = item.get('phone')
                    # استخدام الحقول النصية المباشرة بدلاً من المفاتيح الأجنبية
                    personal_data.governorate = item.get('governorate', '')
                    personal_data.directorate = item.get('directorate', '')
                    personal_data.uzla = item.get('uzla', '')
                    personal_data.village = item.get('village', '')
                    # الاحتفاظ بالمفاتيح الأجنبية للمؤهل العلمي والإدارة والحالة الاجتماعية
                    personal_data.qualification_type_id = qualification_type_id
                    personal_data.agency_id = agency_id
                    personal_data.marital_status_id = marital_status_id

        db.session.commit()

        return jsonify({'success': True, 'new_ids': new_ids})
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': str(e)})

@app.route('/personal_data/import_excel', methods=['POST'])
@login_required
def import_personal_data_excel():
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # التحقق من وجود ملف
    if 'excel_file' not in request.files:
        flash('لم يتم تحديد ملف', 'danger')
        return redirect(url_for('personal_data_excel'))

    file = request.files['excel_file']

    # التحقق من أن الملف له اسم
    if file.filename == '':
        flash('لم يتم تحديد ملف', 'danger')
        return redirect(url_for('personal_data_excel'))

    # التحقق من أن الملف بصيغة إكسل
    if not file.filename.endswith(('.xlsx', '.xls')):
        flash('يجب أن يكون الملف بصيغة Excel (.xlsx أو .xls)', 'danger')
        return redirect(url_for('personal_data_excel'))

    try:
        # قراءة ملف الإكسل
        sheet_name = request.form.get('sheet_name', None)

        try:
            # محاولة قراءة الورقة بالاسم المحدد
            if sheet_name and sheet_name.strip():
                # التحقق من وجود الورقة بالاسم المحدد
                xls = pd.ExcelFile(file)
                if sheet_name not in xls.sheet_names:
                    flash(f'لم يتم العثور على ورقة باسم "{sheet_name}" في الملف. الأوراق المتاحة: {", ".join(xls.sheet_names)}', 'danger')
                    return redirect(url_for('personal_data_excel'))
                df = pd.read_excel(file, sheet_name=sheet_name)
            else:
                # استخدام الورقة الأولى إذا لم يتم تحديد اسم الورقة
                df = pd.read_excel(file, sheet_name=0)
        except Exception as e:
            flash(f'خطأ في قراءة ملف الإكسل: {str(e)}', 'danger')
            return redirect(url_for('personal_data_excel'))

        # التحقق من وجود الأعمدة المطلوبة
        required_columns = ['الاسم الشخصي', 'الرقم الوطني']

        # تحويل أسماء الأعمدة إلى قائمة
        file_columns = df.columns.tolist()

        # طباعة أسماء الأعمدة للتشخيص
        print("أعمدة الملف:", file_columns)

        # محاولة مطابقة الأعمدة المطلوبة مع الأعمدة الموجودة
        column_mapping = {}
        for req_col in required_columns:
            # البحث عن تطابق دقيق
            if req_col in file_columns:
                column_mapping[req_col] = req_col
                print(f"تم العثور على العمود: {req_col}")
            else:
                # البحث عن تطابق جزئي
                for file_col in file_columns:
                    if isinstance(file_col, str) and (req_col in file_col or file_col in req_col):
                        column_mapping[req_col] = file_col
                        print(f"تم العثور على تطابق جزئي: {req_col} -> {file_col}")
                        break
                    # محاولة البحث عن كلمات مشابهة
                    elif isinstance(file_col, str):
                        if 'اسم' in file_col and req_col == 'الاسم الشخصي':
                            column_mapping[req_col] = file_col
                            print(f"تم العثور على تطابق بالكلمات المفتاحية: {req_col} -> {file_col}")
                            break
                        elif 'رقم' in file_col and 'وطني' in file_col and req_col == 'الرقم الوطني':
                            column_mapping[req_col] = file_col
                            print(f"تم العثور على تطابق بالكلمات المفتاحية: {req_col} -> {file_col}")
                            break

        # التحقق من وجود جميع الأعمدة المطلوبة
        missing_columns = [col for col in required_columns if col not in column_mapping]

        if missing_columns:
            flash(f'الأعمدة التالية مفقودة في ملف الإكسل: {", ".join(missing_columns)}', 'danger')
            return redirect(url_for('personal_data_excel'))

        # التحقق من خيار فحص السجلات المكررة
        check_duplicates = request.form.get('check_duplicates') == 'on'

        # إضافة البيانات إلى قاعدة البيانات
        success_count = 0
        error_count = 0
        existing_records = []  # قائمة للسجلات الموجودة مسبقاً
        new_records = []  # قائمة للسجلات الجديدة

        for _, row in df.iterrows():
            try:
                # استخدام التعيين الجديد للأعمدة
                national_id_col = column_mapping.get('الرقم الوطني')
                name_col = column_mapping.get('الاسم الشخصي')

                if not national_id_col or not name_col:
                    error_count += 1
                    continue

                # التحقق من أن القيم غير فارغة
                if pd.isna(row[national_id_col]) or pd.isna(row[name_col]):
                    error_count += 1
                    print(f"قيم فارغة: {row[name_col] if not pd.isna(row[name_col]) else 'فارغ'}, {row[national_id_col] if not pd.isna(row[national_id_col]) else 'فارغ'}")
                    continue

                # تحويل الرقم الوطني إلى نص
                try:
                    national_number = str(row[national_id_col])
                    # إزالة الكسور العشرية إذا كانت موجودة
                    if national_number.endswith('.0'):
                        national_number = national_number[:-2]
                    # إزالة أي مسافات
                    national_number = national_number.strip()

                    # نطبع رسالة فقط للتوضيح، ولكن لا نستخدم الكائن بعد
                    print(f"جاري معالجة السجل: {row[name_col]}, الرقم الوطني: {national_number}")
                except Exception as e:
                    error_count += 1
                    print(f"خطأ في معالجة الرقم الوطني: {e}")
                    continue

                # التحقق من عدم وجود سجل بنفس الرقم الوطني
                existing_record = PersonalData.query.filter_by(national_number=national_number).first()
                if existing_record:
                    # إضافة السجل إلى قائمة السجلات الموجودة مسبقاً
                    existing_records.append({
                        'id': existing_record.id,
                        'full_name': row[name_col],
                        'national_number': national_number,
                        'existing_name': existing_record.full_name
                    })
                    error_count += 1
                    continue
                else:
                    # إضافة السجل إلى قائمة السجلات الجديدة
                    new_records.append({
                        'full_name': row[name_col],
                        'national_number': national_number
                    })

                # إنشاء سجل جديد
                personal_data = PersonalData(
                    user_id=current_user.id,
                    full_name=row[name_col],
                    national_number=national_number
                )

                # إضافة الحقول الجديدة بشكل آمن
                if 'الرقم الوظيفي' in df.columns and pd.notna(row['الرقم الوظيفي']):
                    personal_data.work_number = str(row['الرقم الوظيفي'])
                if 'الرتبة الوظيفي' in df.columns and pd.notna(row['الرتبة الوظيفي']):
                    personal_data.work_rank = str(row['الرتبة الوظيفي'])
                if 'مكان العمل' in df.columns and pd.notna(row['مكان العمل']):
                    personal_data.work_place_text = str(row['مكان العمل'])

                # إضافة الحقول الاختيارية إذا كانت موجودة
                # البحث عن الأعمدة المطابقة في الملف
                for col in df.columns:
                    # الرقم العسكري
                    if 'رقم عسكري' in col or 'عسكري' in col:
                        if pd.notna(row[col]):
                            military_number = str(row[col])
                            if military_number.endswith('.0'):
                                military_number = military_number[:-2]
                            personal_data.military_number = military_number

                    # الرقم الوظيفي
                    elif 'رقم وظيفي' in col or 'وظيفي' in col:
                        if pd.notna(row[col]):
                            work_number = str(row[col])
                            if work_number.endswith('.0'):
                                work_number = work_number[:-2]
                            personal_data.work_number = work_number

                    # العمل
                    elif 'عمل' in col or 'وظيفة' in col or 'مهنة' in col:
                        if pd.notna(row[col]):
                            personal_data.job_title = str(row[col])

                    # مكان العمل
                    elif 'مكان العمل' in col or 'مكان' in col:
                        if pd.notna(row[col]):
                            personal_data.work_place_text = str(row[col])

                    # الرتبة الوظيفية
                    elif 'رتبة' in col:
                        if pd.notna(row[col]):
                            personal_data.work_rank = str(row[col])

                    # رقم الهاتف
                    elif 'هاتف' in col or 'جوال' in col or 'موبايل' in col:
                        if pd.notna(row[col]):
                            phone = str(row[col])
                            if phone.endswith('.0'):
                                phone = phone[:-2]
                            personal_data.phone_yemen_mobile = phone

                # تأكد من أن personal_data هو كائن من نوع PersonalData وليس نصًا
                if not isinstance(personal_data, PersonalData):
                    raise TypeError(f"خطأ: personal_data ليس كائن PersonalData، بل هو {type(personal_data)}")

                db.session.add(personal_data)
                success_count += 1
                print(f"تم إضافة السجل بنجاح: {personal_data.full_name}, الرقم الوطني: {personal_data.national_number}")
            except Exception as e:
                error_count += 1
                print(f"خطأ في استيراد السجل: {str(e)}")

        db.session.commit()

        # حفظ معلومات الاستيراد في الجلسة
        import_summary = {
            'total_records': len(df),
            'success_count': success_count,
            'error_count': error_count,
            'existing_records': existing_records,
            'new_records': new_records
        }
        session['import_summary'] = import_summary

        # عرض نتائج الاستيراد
        if check_duplicates and existing_records:
            flash(f'تم استيراد {success_count} سجل بنجاح. تم العثور على {len(existing_records)} سجل موجود مسبقاً.', 'warning')
            return redirect(url_for('show_existing_records'))
        else:
            flash(f'تم استيراد {success_count} سجل بنجاح من أصل {len(df)} سجل.', 'success')
            return redirect(url_for('personal_data_excel'))

    except Exception as e:
        flash(f'حدث خطأ أثناء استيراد البيانات: {str(e)}', 'danger')
        return redirect(url_for('personal_data_excel'))

@app.route('/personal_data/existing_records')
@login_required
def show_existing_records():
    """
    عرض السجلات الموجودة مسبقاً بعد استيراد الإكسل
    """
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # التحقق من وجود سجلات في الجلسة
    existing_records = session.get('existing_records', [])
    if not existing_records:
        flash('لا توجد سجلات موجودة مسبقاً للعرض', 'info')
        return redirect(url_for('personal_data_excel'))

    return render_template('personal_data/existing_records.html',
                          title='السجلات الموجودة مسبقاً',
                          existing_records=existing_records)

@app.route('/personal_data/excel/export')
@login_required
def export_personal_data_excel():
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    try:
        # جلب البيانات الشخصية - طباعة عدد السجلات للتشخيص
        personal_data = PersonalData.query.all()
        print(f"عدد السجلات المراد تصديرها: {len(personal_data)}")

        # إنشاء DataFrame
        data = []
        print(f"بدء معالجة السجلات للتصدير...")
        for i, person in enumerate(personal_data):
            # طباعة تقدم المعالجة كل 5 سجلات
            if i % 5 == 0:
                print(f"معالجة السجل {i+1} من {len(personal_data)}")

            # الحصول على أسماء المحافظات والمديريات والقرى والمؤهلات والجهات والرتب
            governorate_name = person.governorate.name if person.governorate else ''
            directorate_name = person.directorate.name if person.directorate else ''
            village_name = person.village.name if person.village else ''
            qualification_name = person.qualification_type.name if person.qualification_type else ''
            agency_name = person.agency.name if person.agency else ''

            # تحضير البيانات مع التحقق من وجود الحقول (بالترتيب المطلوب)
            person_data = {
                'الاسم الشخصي': person.full_name,
                'الاسم المستعار': person.nickname if hasattr(person, 'nickname') else '',
                'العمر': person.age if hasattr(person, 'age') else '',
                'المحافظة': governorate_name,
                'المديرية': directorate_name,
                'العزلة': person.uzla if hasattr(person, 'uzla') else '', # استخدام حقل العزلة
                'الحي/القرية': village_name,
                'المؤهل العلمي': qualification_name,
                'الحالة الاجتماعية': person.marital_status.name if person.marital_status else '',
                'العمل': person.job_title if hasattr(person, 'job_title') else '',
                'الإدارة': agency_name,
                'مكان العمل': person.work_place_text if hasattr(person, 'work_place_text') and person.work_place_text else (person.work_place.name if person.work_place else ''),
                'الرقم الوطني': person.national_number,
                'الرقم العسكري': person.military_number if hasattr(person, 'military_number') else '',
                'رقم التلفون': person.phone_yemen_mobile if hasattr(person, 'phone_yemen_mobile') else ''
                # تم حذف الحقول الزائدة: الرقم الوظيفي والرتبة الوظيفية
            }

            data.append(person_data)

        df = pd.DataFrame(data)
        print(f"تم إنشاء DataFrame بنجاح. عدد الصفوف: {len(df)}")

        # معالجة القيم NaN و INF
        df = df.fillna('')  # استبدال القيم NaN بسلاسل فارغة
        print(f"تم معالجة القيم الفارغة بنجاح.")

        # إنشاء ملف إكسل في الذاكرة
        output = io.BytesIO()
        print(f"بدء إنشاء ملف إكسل...")

        with pd.ExcelWriter(output, engine='xlsxwriter') as writer:
            print(f"تصدير البيانات إلى ورقة إكسل...")
            df.to_excel(writer, sheet_name='البيانات الشخصية', index=False)

            # تنسيق الملف
            workbook = writer.book
            worksheet = writer.sheets['البيانات الشخصية']

            # تعيين اتجاه الورقة من اليمين إلى اليسار
            print(f"تعيين اتجاه الورقة من اليمين إلى اليسار...")
            worksheet.right_to_left()

            # تنسيق العناوين
            header_format = workbook.add_format({
                'bold': True,
                'text_wrap': True,
                'valign': 'center',
                'align': 'center',
                'fg_color': '#D7E4BC',
                'border': 1
            })

            # تنسيق البيانات
            data_format = workbook.add_format({
                'valign': 'center',
                'align': 'center',
                'border': 1
            })

            # تطبيق التنسيق على العناوين
            for col_num, value in enumerate(df.columns.values):
                worksheet.write(0, col_num, value, header_format)
                worksheet.set_column(col_num, col_num, 20)  # تعيين عرض العمود

            # تطبيق التنسيق على البيانات
            for row_num in range(1, len(df) + 1):
                for col_num in range(len(df.columns)):
                    worksheet.write(row_num, col_num, df.iloc[row_num-1, col_num], data_format)

        # إعادة المؤشر إلى بداية الملف
        output.seek(0)

        # إنشاء اسم الملف
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f'personal_data_{timestamp}.xlsx'

        # إرسال الملف كاستجابة
        return send_file(
            output,
            as_attachment=True,
            download_name=filename,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )

    except Exception as e:
        flash(f'حدث خطأ أثناء تصدير البيانات: {str(e)}', 'danger')
        return redirect(url_for('personal_data_excel'))

@app.route('/personal_data/excel/template')
@login_required
def download_personal_data_excel_template():
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    try:
        # إنشاء DataFrame فارغ بالأعمدة المطلوبة (حسب الترتيب الصحيح)
        columns = [
            'الاسم الشخصي', 'الاسم المستعار', 'العمر',
            'المحافظة', 'المديرية', 'العزلة', 'الحي/القرية',
            'المؤهل العلمي', 'الحالة الاجتماعية',
            'العمل', 'الإدارة', 'مكان العمل',
            'الرقم الوطني', 'الرقم العسكري', 'رقم الهاتف'
        ]

        df = pd.DataFrame(columns=columns)

        # إضافة صفوف أمثلة
        df.loc[0] = [
            'محمد علي أحمد', 'أبو علي', '35',
            'صنعاء', 'السبعين', 'بني الحارث', 'حي الجامعة',
            'بكالوريوس', 'متزوج',
            'مهندس برمجيات', 'وزارة الدفاع', 'مركز التدريب',
            '1234567890', '987654321', '777123456'
        ]

        df.loc[1] = [
            'أحمد محمد علي', 'أبو محمد', '42',
            'عدن', 'المنصورة', 'الشيخ عثمان', 'المنصورة الشمالية',
            'ماجستير', 'متزوج',
            'مدرس', 'وزارة التربية', 'مدرسة الثورة',
            '0987654321', '123456789', '733987654'
        ]

        # إضافة خلية فارغة للتوضيح
        df.loc[2] = [
            '', '', '',
            '', '', '', '',
            '', '',
            '', '', '',
            '', '', ''
        ]

        # إنشاء ملف إكسل في الذاكرة
        output = io.BytesIO()

        # معالجة القيم NaN و INF
        df = df.fillna('')  # استبدال القيم NaN بسلاسل فارغة

        with pd.ExcelWriter(output, engine='xlsxwriter') as writer:
            df.to_excel(writer, sheet_name='قالب البيانات الشخصية', index=False)

            # تنسيق الملف
            workbook = writer.book
            worksheet = writer.sheets['قالب البيانات الشخصية']

            # تعيين اتجاه الورقة من اليمين إلى اليسار
            worksheet.right_to_left()

            # تنسيق العناوين
            header_format = workbook.add_format({
                'bold': True,
                'text_wrap': True,
                'valign': 'center',
                'align': 'center',
                'fg_color': '#FFFF00',  # لون أصفر للعناوين
                'border': 1
            })

            # تنسيق البيانات
            data_format = workbook.add_format({
                'valign': 'center',
                'align': 'center',
                'border': 1
            })

            # تطبيق التنسيق على العناوين
            for col_num, value in enumerate(df.columns.values):
                worksheet.write(0, col_num, value, header_format)
                worksheet.set_column(col_num, col_num, 20)  # تعيين عرض العمود

            # تطبيق التنسيق على البيانات
            for row_num in range(1, len(df) + 1):
                for col_num in range(len(df.columns)):
                    worksheet.write(row_num, col_num, df.iloc[row_num-1, col_num], data_format)

        # إعادة المؤشر إلى بداية الملف
        output.seek(0)

        # إرسال الملف كاستجابة
        return send_file(
            output,
            as_attachment=True,
            download_name='personal_data_template.xlsx',
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )

    except Exception as e:
        flash(f'حدث خطأ أثناء إنشاء قالب البيانات: {str(e)}', 'danger')
        return redirect(url_for('personal_data_excel'))

@app.route('/personal_data/import', methods=['POST'])
@login_required
def import_excel_data():
    """استيراد البيانات الشخصية من ملف إكسل - نسخة محسنة باستخدام معالجة الدفعات"""
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    if 'excel_file' not in request.files:
        flash('لم يتم اختيار ملف', 'danger')
        return redirect(url_for('add_personal_data_new'))

    file = request.files['excel_file']
    if file.filename == '':
        flash('لم يتم اختيار ملف', 'danger')
        return redirect(url_for('add_personal_data_new'))

    if file and file.filename.endswith(('.xlsx', '.xls')):
        try:
            # حفظ الملف مؤقتاً
            temp_file_path = os.path.join(app.config['UPLOAD_FOLDER'], 'temp_import.xlsx')
            file.save(temp_file_path)

            # قراءة ملف الإكسل للتحقق من الأعمدة المطلوبة
            try:
                df = pd.read_excel(temp_file_path, nrows=1)  # قراءة صف واحد فقط للتحقق
            except Exception as e:
                flash(f'خطأ في قراءة ملف الإكسل: {str(e)}', 'danger')
                return redirect(url_for('add_personal_data_new'))

            # التحقق من وجود الأعمدة المطلوبة
            required_columns = ['الاسم الشخصي', 'الرقم الوطني']
            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                flash(f'الملف لا يحتوي على الأعمدة التالية: {", ".join(missing_columns)}', 'danger')
                return redirect(url_for('add_personal_data_new'))

            # تهيئة معلومات التقدم في الجلسة
            session['import_progress'] = {
                'total': 0,  # سيتم تحديثه في الدالة المساعدة
                'processed': 0,
                'success': 0,
                'error': 0,
                'status': 'جاري الاستيراد...'
            }
            session.modified = True

            # توجيه المستخدم إلى صفحة النتائج مباشرة
            flash('بدأت عملية استيراد البيانات. يرجى الانتظار...', 'info')

            # بدء عملية الاستيراد في خلفية الخادم
            # استخدام خيط منفصل لمعالجة الاستيراد
            import threading

            def process_import():
                try:
                    # استدعاء الدالة المساعدة لاستيراد البيانات على دفعات
                    import_results = import_excel_data_in_chunks(
                        file_path=temp_file_path,
                        user_id=current_user.id,
                        session_obj=session,
                        chunk_size=1000  # يمكن تعديل حجم الدفعة حسب الحاجة
                    )

                    # تخزين نتائج الاستيراد في الجلسة
                    session['import_results'] = import_results
                    session.modified = True

                    # حذف الملف المؤقت
                    if os.path.exists(temp_file_path):
                        os.remove(temp_file_path)

                except Exception as e:
                    logger.error(f"خطأ في عملية الاستيراد: {e}")
                    session['import_progress']['status'] = 'فشل الاستيراد'
                    session.modified = True

            # بدء خيط جديد لمعالجة الاستيراد
            import_thread = threading.Thread(target=process_import)
            import_thread.daemon = True  # جعل الخيط daemon لإنهائه عند إنهاء البرنامج الرئيسي
            import_thread.start()

            # توجيه المستخدم إلى صفحة النتائج
            return redirect(url_for('import_results'))

        except Exception as e:
            flash(f'حدث خطأ أثناء قراءة الملف: {str(e)}', 'danger')
            return redirect(url_for('add_personal_data_new'))
    else:
        flash('صيغة الملف غير مدعومة. يرجى استخدام ملف بصيغة Excel (.xlsx, .xls)', 'danger')
        return redirect(url_for('add_personal_data_new'))

@app.route('/personal_data/import/progress')
@login_required
def get_import_progress():
    """API لجلب تقدم الاستيراد"""
    if 'import_progress' in session:
        return jsonify(session['import_progress'])
    else:
        return jsonify({
            'total': 0,
            'processed': 0,
            'success': 0,
            'error': 0,
            'status': 'لا توجد عملية استيراد جارية'
        })

@app.route('/personal_data/import/results')
@login_required
def import_results():
    """صفحة نتائج الاستيراد"""
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    return render_template('personal_data/import_results.html', title='نتائج استيراد البيانات الشخصية')

@app.route('/personal_data/compare', methods=['POST'])
@login_required
# @csrf.exempt  # استثناء حماية CSRF لهذا المسار - تم تعطيله مؤقتاً
def compare_excel_files():
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    if 'excel_file_1' not in request.files or 'excel_file_2' not in request.files:
        flash('يرجى اختيار الملفين للمقارنة', 'danger')
        return redirect(url_for('add_personal_data_new'))

    file1 = request.files['excel_file_1']
    file2 = request.files['excel_file_2']

    if file1.filename == '' or file2.filename == '':
        flash('يرجى اختيار الملفين للمقارنة', 'danger')
        return redirect(url_for('add_personal_data_new'))

    if file1 and file2 and file1.filename.endswith(('.xlsx', '.xls')) and file2.filename.endswith(('.xlsx', '.xls')):
        try:
            # قراءة ملفات الإكسل
            try:
                # محاولة قراءة الورقة الأولى من الملف الأول
                xls1 = pd.ExcelFile(file1)
                if not xls1.sheet_names:
                    flash('الملف الأول لا يحتوي على أي أوراق عمل', 'danger')
                    return redirect(url_for('add_personal_data_new'))

                # محاولة قراءة الورقة الأولى من الملف الثاني
                xls2 = pd.ExcelFile(file2)
                if not xls2.sheet_names:
                    flash('الملف الثاني لا يحتوي على أي أوراق عمل', 'danger')
                    return redirect(url_for('add_personal_data_new'))

                # استخدام الورقة الأولى من كل ملف
                df1 = pd.read_excel(file1, sheet_name=0)
                df2 = pd.read_excel(file2, sheet_name=0)

                print(f"تم قراءة ورقة العمل من الملف الأول: {xls1.sheet_names[0]}")
                print(f"تم قراءة ورقة العمل من الملف الثاني: {xls2.sheet_names[0]}")
            except Exception as e:
                flash(f'خطأ في قراءة ملفات الإكسل: {str(e)}', 'danger')
                return redirect(url_for('add_personal_data_new'))

            # التحقق من وجود الأعمدة المطلوبة
            required_columns = ['الاسم الشخصي', 'الرقم الوطني']

            missing_columns_1 = [col for col in required_columns if col not in df1.columns]
            missing_columns_2 = [col for col in required_columns if col not in df2.columns]

            if missing_columns_1 or missing_columns_2:
                if missing_columns_1:
                    flash(f'الملف الأول لا يحتوي على الأعمدة التالية: {", ".join(missing_columns_1)}', 'danger')
                if missing_columns_2:
                    flash(f'الملف الثاني لا يحتوي على الأعمدة التالية: {", ".join(missing_columns_2)}', 'danger')
                return redirect(url_for('add_personal_data_new'))

            # تصحيح الأسماء العربية
            if 'الاسم الشخصي' in df1.columns:
                df1['الاسم الشخصي'] = df1['الاسم الشخصي'].apply(lambda x: correct_arabic_name(x) if isinstance(x, str) else x)
            if 'الاسم الشخصي' in df2.columns:
                df2['الاسم الشخصي'] = df2['الاسم الشخصي'].apply(lambda x: correct_arabic_name(x) if isinstance(x, str) else x)

            # تحويل الرقم الوطني إلى نص
            df1['الرقم الوطني'] = df1['الرقم الوطني'].astype(str)
            df2['الرقم الوطني'] = df2['الرقم الوطني'].astype(str)

            # إجراء المباينة
            comparison_results = compare_dataframes(df1, df2)

            # حفظ النتائج في الجلسة
            session['comparison_results'] = comparison_results
            session['file1_name'] = file1.filename
            session['file2_name'] = file2.filename

            return render_template('personal_data/compare_results.html',
                                  title='نتائج المباينة',
                                  file1_name=file1.filename,
                                  file2_name=file2.filename,
                                  total_records=comparison_results['total_records'],
                                  matching_records=len(comparison_results['matching_data']),
                                  different_records=len(comparison_results['different_data']),
                                  duplicate_records=len(comparison_results['duplicates']),
                                  matching_data=comparison_results['matching_data'],
                                  different_data=comparison_results['different_data'],
                                  unique_file1=comparison_results['unique_file1'],
                                  unique_file2=comparison_results['unique_file2'],
                                  duplicates=comparison_results['duplicates'],
                                  corrections=comparison_results['corrections'])

        except Exception as e:
            flash(f'حدث خطأ أثناء مقارنة الملفات: {str(e)}', 'danger')
            return redirect(url_for('add_personal_data_new'))
    else:
        flash('صيغة الملف غير مدعومة. يرجى استخدام ملفات بصيغة Excel (.xlsx, .xls)', 'danger')
        return redirect(url_for('add_personal_data_new'))

@app.route('/personal_data/export_comparison')
@login_required
# @csrf.exempt  # استثناء حماية CSRF لهذا المسار - تم تعطيله مؤقتاً
def export_comparison_results():
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    if 'comparison_results' not in session:
        flash('لا توجد نتائج مباينة للتصدير', 'danger')
        return redirect(url_for('add_personal_data_new'))

    try:
        comparison_results = session['comparison_results']

        # إنشاء ملف إكسل
        output = io.BytesIO()
        writer = pd.ExcelWriter(output, engine='xlsxwriter')

        # تعيين اتجاه الورقة من اليمين إلى اليسار لكل ورقة
        workbook = writer.book

        # إنشاء ورقة للسجلات المتطابقة
        if comparison_results['matching_data']:
            df_matching = pd.DataFrame(comparison_results['matching_data'])
            df_matching = df_matching.fillna('')  # استبدال القيم NaN بسلاسل فارغة
            df_matching.to_excel(writer, sheet_name='السجلات المتطابقة', index=False)
            worksheet = writer.sheets['السجلات المتطابقة']
            worksheet.right_to_left()

        # إنشاء ورقة للسجلات المختلفة
        if comparison_results['different_data']:
            df_different = pd.DataFrame(comparison_results['different_data'])
            df_different = df_different.fillna('')  # استبدال القيم NaN بسلاسل فارغة
            df_different.to_excel(writer, sheet_name='السجلات المختلفة', index=False)
            worksheet = writer.sheets['السجلات المختلفة']
            worksheet.right_to_left()

        # إنشاء ورقة للسجلات الفريدة في الملف الأول
        if comparison_results['unique_file1']:
            df_unique1 = pd.DataFrame(comparison_results['unique_file1'])
            df_unique1 = df_unique1.fillna('')  # استبدال القيم NaN بسلاسل فارغة
            df_unique1.to_excel(writer, sheet_name='سجلات فقط في الملف الأول', index=False)
            worksheet = writer.sheets['سجلات فقط في الملف الأول']
            worksheet.right_to_left()

        # إنشاء ورقة للسجلات الفريدة في الملف الثاني
        if comparison_results['unique_file2']:
            df_unique2 = pd.DataFrame(comparison_results['unique_file2'])
            df_unique2 = df_unique2.fillna('')  # استبدال القيم NaN بسلاسل فارغة
            df_unique2.to_excel(writer, sheet_name='سجلات فقط في الملف الثاني', index=False)
            worksheet = writer.sheets['سجلات فقط في الملف الثاني']
            worksheet.right_to_left()

        # إنشاء ورقة للسجلات المكررة
        if comparison_results['duplicates']:
            df_duplicates = pd.DataFrame(comparison_results['duplicates'])
            df_duplicates = df_duplicates.fillna('')  # استبدال القيم NaN بسلاسل فارغة
            df_duplicates.to_excel(writer, sheet_name='السجلات المكررة', index=False)
            worksheet = writer.sheets['السجلات المكررة']
            worksheet.right_to_left()

        # إنشاء ورقة للتصحيحات المقترحة
        if comparison_results['corrections']:
            df_corrections = pd.DataFrame(comparison_results['corrections'])
            df_corrections = df_corrections.fillna('')  # استبدال القيم NaN بسلاسل فارغة
            df_corrections.to_excel(writer, sheet_name='التصحيحات المقترحة', index=False)
            worksheet = writer.sheets['التصحيحات المقترحة']
            worksheet.right_to_left()

        writer.close()
        output.seek(0)

        # إنشاء استجابة لتنزيل الملف
        return Response(
            output,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            headers={
                'Content-Disposition': 'attachment; filename=comparison_results.xlsx'
            }
        )

    except Exception as e:
        flash(f'حدث خطأ أثناء تصدير النتائج: {str(e)}', 'danger')
        return redirect(url_for('add_personal_data_new'))

@app.route('/personal_data/<int:personal_data_id>')
@login_required
def view_personal_data(personal_data_id):
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # جلب البيانات الشخصية
    personal_data = PersonalData.query.get_or_404(personal_data_id)

    # جلب الدورات السابقة
    previous_courses = PreviousCourse.query.filter_by(personal_data_id=personal_data_id).all()

    return render_template('personal_data/view.html', title='عرض البيانات الشخصية', personal_data=personal_data, previous_courses=previous_courses)

@app.route('/personal_data/<int:personal_data_id>/edit', methods=['GET', 'POST'])
@login_required
def edit_personal_data(personal_data_id):
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # جلب البيانات الشخصية
    personal_data = PersonalData.query.get_or_404(personal_data_id)

    form = PersonalDataForm()

    # تحديث خيارات القوائم المنسدلة
    form.marital_status_id.choices = [(m.id, m.name) for m in MaritalStatus.query.all()]
    form.residence_governorate_id.choices = [(g.id, g.name) for g in Governorate.query.all()]
    form.residence_directorate_id.choices = [(0, 'اختر المديرية')]
    form.residence_village_id.choices = [(0, 'اختر الحي/القرية')]
    form.qualification_type_id.choices = [(q.id, q.name) for q in QualificationType.query.all()]
    form.specialization_id.choices = [(s.id, s.name) for s in Specialization.query.all()]

    # إضافة الجهات من جدول Agency بدلاً من WorkPlace
    agencies = Agency.query.all()
    form.work_place_id.choices = [(a.id, a.name) for a in agencies]

    form.work_governorate_id.choices = [(g.id, g.name) for g in Governorate.query.all()]
    form.work_directorate_id.choices = [(0, 'اختر المديرية')]
    form.work_village_id.choices = [(0, 'اختر الحي/القرية')]
    form.assignment_type_id.choices = [(a.id, a.name) for a in AssignmentType.query.all()]
    form.assignment_authority_id.choices = [(a.id, a.name) for a in IssuingAuthority.query.all()]
    form.blood_type_id.choices = [(b.id, b.name) for b in BloodType.query.all()]
    form.military_rank_id.choices = [(m.id, m.name) for m in MilitaryRank.query.all()]
    form.injury_type_id.choices = [(i.id, i.name) for i in InjuryType.query.all()]
    form.injury_cause_id.choices = [(i.id, i.name) for i in InjuryCause.query.all()]
    form.issuing_authority_id.choices = [(a.id, a.name) for a in IssuingAuthority.query.all()]

    if form.validate_on_submit():
        # تحديث البيانات الشخصية
        personal_data.full_name = form.full_name.data
        personal_data.nickname = form.nickname.data
        personal_data.triple_number = form.triple_number.data
        personal_data.national_number = form.national_number.data
        personal_data.birth_date = form.birth_date.data
        personal_data.age = form.age.data
        personal_data.issue_date = form.issue_date.data
        personal_data.issuing_authority_id = form.issuing_authority_id.data
        personal_data.children_count = form.children_count.data
        personal_data.marital_status_id = form.marital_status_id.data
        personal_data.residence_governorate_id = form.residence_governorate_id.data
        personal_data.residence_directorate_id = form.residence_directorate_id.data
        personal_data.residence_village_id = form.residence_village_id.data
        personal_data.residence_house = form.residence_house.data
        personal_data.residence_type = form.residence_type.data
        personal_data.phone_yemen_mobile = form.phone_yemen_mobile.data
        personal_data.phone_you = form.phone_you.data
        personal_data.phone_sayyon = form.phone_sayyon.data
        personal_data.phone_landline = form.phone_landline.data
        personal_data.qualification_type_id = form.qualification_type_id.data
        personal_data.specialization_id = form.specialization_id.data
        personal_data.qualification_place = form.qualification_place.data
        personal_data.qualification_date = form.qualification_date.data
        # استخدام جدول Agency بدلاً من WorkPlace
        personal_data.work_place_id = form.work_place_id.data
        personal_data.job_title = form.job_title.data
        personal_data.work_governorate_id = form.work_governorate_id.data
        personal_data.work_directorate_id = form.work_directorate_id.data
        personal_data.work_village_id = form.work_village_id.data
        personal_data.work_unit = form.work_unit.data
        personal_data.assignment_type_id = form.assignment_type_id.data
        personal_data.assignment_date = form.assignment_date.data
        personal_data.assignment_authority_id = form.assignment_authority_id.data
        personal_data.military_number = form.military_number.data
        personal_data.blood_type_id = form.blood_type_id.data
        personal_data.is_fighter = form.is_fighter.data
        personal_data.weapon_ownership = form.weapon_ownership.data
        personal_data.military_rank_id = form.military_rank_id.data
        personal_data.rank_date = form.rank_date.data
        personal_data.health_status = form.health_status.data
        personal_data.injury_type_id = form.injury_type_id.data
        personal_data.injury_cause_id = form.injury_cause_id.data
        personal_data.injury_place = form.injury_place.data
        personal_data.injury_date = form.injury_date.data
        personal_data.injury_body_location = form.injury_body_location.data
        personal_data.injury_disability = form.injury_disability.data
        personal_data.injury_hinders_work = form.injury_hinders_work.data
        personal_data.injury_authority = form.injury_authority.data
        personal_data.notes = form.notes.data

        db.session.commit()

        flash('تم تحديث البيانات الشخصية بنجاح!', 'success')
        return redirect(url_for('view_personal_data', personal_data_id=personal_data_id))

    # ملء النموذج ببيانات الشخص الحالية
    elif request.method == 'GET':
        form.full_name.data = personal_data.full_name
        form.nickname.data = personal_data.nickname
        form.triple_number.data = personal_data.triple_number
        form.national_number.data = personal_data.national_number
        form.birth_date.data = personal_data.birth_date
        form.age.data = personal_data.age
        form.issue_date.data = personal_data.issue_date
        form.issuing_authority_id.data = personal_data.issuing_authority_id
        form.children_count.data = personal_data.children_count
        form.marital_status_id.data = personal_data.marital_status_id
        form.residence_governorate_id.data = personal_data.residence_governorate_id
        form.residence_directorate_id.data = personal_data.residence_directorate_id
        form.residence_village_id.data = personal_data.residence_village_id
        form.residence_house.data = personal_data.residence_house
        form.residence_type.data = personal_data.residence_type
        form.phone_yemen_mobile.data = personal_data.phone_yemen_mobile
        form.phone_you.data = personal_data.phone_you
        form.phone_sayyon.data = personal_data.phone_sayyon
        form.phone_landline.data = personal_data.phone_landline
        form.qualification_type_id.data = personal_data.qualification_type_id
        form.specialization_id.data = personal_data.specialization_id
        form.qualification_place.data = personal_data.qualification_place
        form.qualification_date.data = personal_data.qualification_date
        form.work_place_id.data = personal_data.work_place_id
        form.job_title.data = personal_data.job_title
        form.work_governorate_id.data = personal_data.work_governorate_id
        form.work_directorate_id.data = personal_data.work_directorate_id
        form.work_village_id.data = personal_data.work_village_id
        form.work_unit.data = personal_data.work_unit
        form.assignment_type_id.data = personal_data.assignment_type_id
        form.assignment_date.data = personal_data.assignment_date
        form.assignment_authority_id.data = personal_data.assignment_authority_id
        form.military_number.data = personal_data.military_number
        form.blood_type_id.data = personal_data.blood_type_id
        form.is_fighter.data = personal_data.is_fighter
        form.weapon_ownership.data = personal_data.weapon_ownership
        form.military_rank_id.data = personal_data.military_rank_id
        form.rank_date.data = personal_data.rank_date
        form.health_status.data = personal_data.health_status
        form.injury_type_id.data = personal_data.injury_type_id
        form.injury_cause_id.data = personal_data.injury_cause_id
        form.injury_place.data = personal_data.injury_place
        form.injury_date.data = personal_data.injury_date
        form.injury_body_location.data = personal_data.injury_body_location
        form.injury_disability.data = personal_data.injury_disability
        form.injury_hinders_work.data = personal_data.injury_hinders_work
        form.injury_authority.data = personal_data.injury_authority
        form.notes.data = personal_data.notes

    return render_template('personal_data/edit.html', title='تعديل البيانات الشخصية', form=form, personal_data=personal_data)

@app.route('/personal_data/<int:personal_data_id>/delete')
@login_required
def delete_personal_data(personal_data_id):
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # جلب البيانات الشخصية
    personal_data = PersonalData.query.get_or_404(personal_data_id)

    # حذف البيانات الشخصية
    db.session.delete(personal_data)
    db.session.commit()

    flash('تم حذف البيانات الشخصية بنجاح!', 'success')
    return redirect(url_for('personal_data_list'))

@app.route('/personal_data/<int:personal_data_id>/add_course', methods=['GET', 'POST'])
@login_required
def add_previous_course(personal_data_id):
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # جلب البيانات الشخصية
    personal_data = PersonalData.query.get_or_404(personal_data_id)

    form = PreviousCourseForm()

    # تحديث خيارات القوائم المنسدلة
    form.course_type_id.choices = [(c.id, c.name) for c in CourseType.query.all()]

    if form.validate_on_submit():
        # إنشاء دورة سابقة جديدة
        previous_course = PreviousCourse(
            personal_data_id=personal_data_id,
            course_type_id=form.course_type_id.data,
            course_name=form.course_name.data,
            course_place=form.course_place.data,
            course_date=form.course_date.data,
            course_duration=form.course_duration.data
        )

        db.session.add(previous_course)
        db.session.commit()

        flash('تم إضافة الدورة السابقة بنجاح!', 'success')
        return redirect(url_for('view_personal_data', personal_data_id=personal_data_id))

    return render_template('personal_data/add_course.html', title='إضافة دورة سابقة', form=form, personal_data=personal_data)

@app.route('/personal_data/previous_course/<int:previous_course_id>/delete')
@login_required
def delete_previous_course(previous_course_id):
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # جلب الدورة السابقة
    previous_course = PreviousCourse.query.get_or_404(previous_course_id)
    personal_data_id = previous_course.personal_data_id

    # حذف الدورة السابقة
    db.session.delete(previous_course)
    db.session.commit()

    flash('تم حذف الدورة السابقة بنجاح!', 'success')
    return redirect(url_for('view_personal_data', personal_data_id=personal_data_id))

# واجهات برمجة التطبيقات API
@app.route('/api/directorates/<int:governorate_id>')
def get_directorates_by_governorate(governorate_id):
    directorates = Directorate.query.filter_by(governorate_id=governorate_id).all()
    return jsonify([{'id': d.id, 'name': d.name} for d in directorates])

@app.route('/api/villages/<int:directorate_id>')
def get_villages_by_directorate(directorate_id):
    villages = Village.query.filter_by(directorate_id=directorate_id).all()
    return jsonify([{'id': v.id, 'name': v.name} for v in villages])

@app.route('/api/path_levels/<int:path_id>')
def get_path_levels(path_id):
    levels = CoursePathLevel.query.filter_by(path_id=path_id).order_by(CoursePathLevel.order).all()
    return jsonify([{'id': l.id, 'name': l.name, 'code': l.code, 'order': l.order} for l in levels])

@app.route('/api/training_centers/<int:center_id>')
def get_training_center(center_id):
    center = TrainingCenter.query.get_or_404(center_id)

    # الحصول على معلومات نوع المركز
    center_type = TrainingCenterType.query.get(center.center_type_id).name if center.center_type_id else '-'

    # الحصول على معلومات الموقع
    location = Location.query.get(center.location_id).name if center.location_id else '-'

    # الحصول على معلومات الجهة
    agency = Agency.query.get(center.agency_id).name if center.agency_id else '-'

    return jsonify({
        'id': center.id,
        'name': center.name,
        'center_type': center_type,
        'location': location,
        'agency': agency,
        'capacity': center.capacity,
        'is_ready': center.is_ready,
        'not_ready_reason': center.not_ready_reason
    })

# مسارات الجداول الترميزية

# الرتب العسكرية
@app.route('/military_ranks')
@login_required
def military_ranks():
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # جلب جميع الرتب العسكرية
    ranks = MilitaryRank.query.all()
    return render_template('reference_tables/military_ranks.html', title='الرتب العسكرية', ranks=ranks)

@app.route('/military_ranks/add', methods=['GET', 'POST'])
@login_required
def add_military_rank():
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    form = MilitaryRankForm()

    if form.validate_on_submit():
        # إنشاء رتبة عسكرية جديدة
        rank = MilitaryRank(
            name=form.name.data,
            code=form.code.data
        )

        db.session.add(rank)
        db.session.commit()

        flash('تم إضافة الرتبة العسكرية بنجاح!', 'success')
        return redirect(url_for('military_ranks'))

    return render_template('reference_tables/add_military_rank.html', title='إضافة رتبة عسكرية', form=form)

@app.route('/military_ranks/<int:rank_id>/edit', methods=['GET', 'POST'])
@login_required
def edit_military_rank(rank_id):
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # جلب الرتبة العسكرية
    rank = MilitaryRank.query.get_or_404(rank_id)

    form = MilitaryRankForm()

    if form.validate_on_submit():
        # تحديث الرتبة العسكرية
        rank.name = form.name.data
        rank.code = form.code.data

        db.session.commit()

        flash('تم تحديث الرتبة العسكرية بنجاح!', 'success')
        return redirect(url_for('military_ranks'))

    # ملء النموذج ببيانات الرتبة العسكرية الحالية
    elif request.method == 'GET':
        form.name.data = rank.name
        form.code.data = rank.code

    return render_template('reference_tables/edit_military_rank.html', title='تعديل رتبة عسكرية', form=form, rank=rank)

@app.route('/military_ranks/<int:rank_id>/delete')
@login_required
def delete_military_rank(rank_id):
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # جلب الرتبة العسكرية
    rank = MilitaryRank.query.get_or_404(rank_id)

    # التحقق من عدم وجود بيانات شخصية مرتبطة بالرتبة العسكرية
    if PersonalData.query.filter_by(military_rank_id=rank_id).first():
        flash('لا يمكن حذف الرتبة العسكرية لأنها مرتبطة ببيانات شخصية!', 'danger')
        return redirect(url_for('military_ranks'))

    # حذف الرتبة العسكرية
    db.session.delete(rank)
    db.session.commit()

    flash('تم حذف الرتبة العسكرية بنجاح!', 'success')
    return redirect(url_for('military_ranks'))

# أنواع الإصابات
@app.route('/injury_types')
@login_required
def injury_types():
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # جلب جميع أنواع الإصابات
    injury_types = InjuryType.query.all()
    return render_template('reference_tables/injury_types.html', title='أنواع الإصابات', injury_types=injury_types)

@app.route('/injury_types/add', methods=['GET', 'POST'])
@login_required
def add_injury_type():
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    form = InjuryTypeForm()

    if form.validate_on_submit():
        # إنشاء نوع إصابة جديد
        injury_type = InjuryType(
            name=form.name.data
        )

        db.session.add(injury_type)
        db.session.commit()

        flash('تم إضافة نوع الإصابة بنجاح!', 'success')
        return redirect(url_for('injury_types'))

    return render_template('reference_tables/add_injury_type.html', title='إضافة نوع إصابة', form=form)

@app.route('/injury_types/<int:injury_type_id>/edit', methods=['GET', 'POST'])
@login_required
def edit_injury_type(injury_type_id):
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # جلب نوع الإصابة
    injury_type = InjuryType.query.get_or_404(injury_type_id)

    form = InjuryTypeForm()

    if form.validate_on_submit():
        # تحديث بيانات نوع الإصابة
        injury_type.name = form.name.data

        db.session.commit()

        flash('تم تحديث نوع الإصابة بنجاح!', 'success')
        return redirect(url_for('injury_types'))

    # ملء النموذج ببيانات نوع الإصابة الحالي
    elif request.method == 'GET':
        form.name.data = injury_type.name

    return render_template('reference_tables/edit_injury_type.html', title='تعديل نوع إصابة', form=form, injury_type=injury_type)

@app.route('/injury_types/<int:injury_type_id>/delete')
@login_required
def delete_injury_type(injury_type_id):
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # جلب نوع الإصابة
    injury_type = InjuryType.query.get_or_404(injury_type_id)

    # التحقق من عدم وجود بيانات شخصية مرتبطة بنوع الإصابة
    if PersonalData.query.filter_by(injury_type_id=injury_type_id).first():
        flash('لا يمكن حذف نوع الإصابة لأنه مرتبط ببيانات شخصية!', 'danger')
        return redirect(url_for('injury_types'))

    # حذف نوع الإصابة
    db.session.delete(injury_type)
    db.session.commit()

    flash('تم حذف نوع الإصابة بنجاح!', 'success')
    return redirect(url_for('injury_types'))

# أسباب الإصابات
@app.route('/injury_causes')
@login_required
def injury_causes():
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # جلب جميع أسباب الإصابات
    injury_causes = InjuryCause.query.all()
    return render_template('reference_tables/injury_causes.html', title='أسباب الإصابات', injury_causes=injury_causes)

@app.route('/injury_causes/add', methods=['GET', 'POST'])
@login_required
def add_injury_cause():
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    form = InjuryCauseForm()

    if form.validate_on_submit():
        # إنشاء سبب إصابة جديد
        injury_cause = InjuryCause(
            name=form.name.data
        )

        db.session.add(injury_cause)
        db.session.commit()

        flash('تم إضافة سبب الإصابة بنجاح!', 'success')
        return redirect(url_for('injury_causes'))

    return render_template('reference_tables/add_injury_cause.html', title='إضافة سبب إصابة', form=form)

@app.route('/injury_causes/<int:injury_cause_id>/edit', methods=['GET', 'POST'])
@login_required
def edit_injury_cause(injury_cause_id):
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # جلب سبب الإصابة
    injury_cause = InjuryCause.query.get_or_404(injury_cause_id)

    form = InjuryCauseForm()

    if form.validate_on_submit():
        # تحديث بيانات سبب الإصابة
        injury_cause.name = form.name.data

        db.session.commit()

        flash('تم تحديث سبب الإصابة بنجاح!', 'success')
        return redirect(url_for('injury_causes'))

    # ملء النموذج ببيانات سبب الإصابة الحالي
    elif request.method == 'GET':
        form.name.data = injury_cause.name

    return render_template('reference_tables/edit_injury_cause.html', title='تعديل سبب إصابة', form=form, injury_cause=injury_cause)

@app.route('/injury_causes/<int:injury_cause_id>/delete')
@login_required
def delete_injury_cause(injury_cause_id):
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # جلب سبب الإصابة
    injury_cause = InjuryCause.query.get_or_404(injury_cause_id)

    # التحقق من عدم وجود بيانات شخصية مرتبطة بسبب الإصابة
    if PersonalData.query.filter_by(injury_cause_id=injury_cause_id).first():
        flash('لا يمكن حذف سبب الإصابة لأنه مرتبط ببيانات شخصية!', 'danger')
        return redirect(url_for('injury_causes'))

    # حذف سبب الإصابة
    db.session.delete(injury_cause)
    db.session.commit()

    flash('تم حذف سبب الإصابة بنجاح!', 'success')
    return redirect(url_for('injury_causes'))

# المحافظات
@app.route('/governorates')
@login_required
def governorates():
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # جلب جميع المحافظات
    governorates = Governorate.query.all()
    return render_template('reference_tables/governorates.html', title='المحافظات', governorates=governorates)

@app.route('/governorates/tree')
@login_required
def governorates_tree():
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # جلب جميع المحافظات مع المديريات والعزل
    governorates = Governorate.query.all()
    return render_template('reference_tables/governorates_tree.html', title='المحافظات والمديريات والعزل', governorates=governorates)

@app.route('/governorates/add', methods=['GET', 'POST'])
@login_required
def add_governorate():
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    form = GovernorateForm()

    if form.validate_on_submit():
        # إنشاء محافظة جديدة
        governorate = Governorate(
            name=form.name.data,
            code=form.code.data
        )

        db.session.add(governorate)
        db.session.commit()

        flash('تم إضافة المحافظة بنجاح!', 'success')
        return redirect(url_for('governorates'))

    return render_template('reference_tables/add_governorate.html', title='إضافة محافظة', form=form)

@app.route('/governorates/<int:governorate_id>/edit', methods=['GET', 'POST'])
@login_required
def edit_governorate(governorate_id):
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # جلب المحافظة
    governorate = Governorate.query.get_or_404(governorate_id)

    form = GovernorateForm()

    if form.validate_on_submit():
        # تحديث المحافظة
        governorate.name = form.name.data
        governorate.code = form.code.data

        db.session.commit()

        flash('تم تحديث المحافظة بنجاح!', 'success')
        return redirect(url_for('governorates'))

    # ملء النموذج ببيانات المحافظة الحالية
    elif request.method == 'GET':
        form.name.data = governorate.name
        form.code.data = governorate.code

    return render_template('reference_tables/edit_governorate.html', title='تعديل محافظة', form=form, governorate=governorate)

@app.route('/governorates/<int:governorate_id>/delete')
@login_required
def delete_governorate(governorate_id):
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # جلب المحافظة
    governorate = Governorate.query.get_or_404(governorate_id)

    # التحقق من عدم وجود مديريات مرتبطة بالمحافظة
    if governorate.directorates:
        flash('لا يمكن حذف المحافظة لأنها تحتوي على مديريات!', 'danger')
        return redirect(url_for('governorates'))

    # حذف المحافظة
    db.session.delete(governorate)
    db.session.commit()

    flash('تم حذف المحافظة بنجاح!', 'success')
    return redirect(url_for('governorates'))

# المديريات
@app.route('/directorates')
@login_required
def directorates():
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # جلب جميع المديريات
    directorates = Directorate.query.all()
    return render_template('reference_tables/directorates.html', title='المديريات', directorates=directorates)

@app.route('/directorates/add', methods=['GET', 'POST'])
@login_required
def add_directorate():
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    form = DirectorateForm()

    # تحديث خيارات القوائم المنسدلة
    form.governorate_id.choices = [(g.id, g.name) for g in Governorate.query.all()]

    if form.validate_on_submit():
        # إنشاء مديرية جديدة
        directorate = Directorate(
            name=form.name.data,
            code=form.code.data,
            governorate_id=form.governorate_id.data
        )

        db.session.add(directorate)
        db.session.commit()

        flash('تم إضافة المديرية بنجاح!', 'success')
        return redirect(url_for('directorates'))

    return render_template('reference_tables/add_directorate.html', title='إضافة مديرية', form=form)

@app.route('/directorates/<int:directorate_id>/edit', methods=['GET', 'POST'])
@login_required
def edit_directorate(directorate_id):
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # جلب المديرية
    directorate = Directorate.query.get_or_404(directorate_id)

    form = DirectorateForm()

    # تحديث خيارات القوائم المنسدلة
    form.governorate_id.choices = [(g.id, g.name) for g in Governorate.query.all()]

    if form.validate_on_submit():
        # تحديث المديرية
        directorate.name = form.name.data
        directorate.code = form.code.data
        directorate.governorate_id = form.governorate_id.data

        db.session.commit()

        flash('تم تحديث المديرية بنجاح!', 'success')
        return redirect(url_for('directorates'))

    # ملء النموذج ببيانات المديرية الحالية
    elif request.method == 'GET':
        form.name.data = directorate.name
        form.code.data = directorate.code
        form.governorate_id.data = directorate.governorate_id

    return render_template('reference_tables/edit_directorate.html', title='تعديل مديرية', form=form, directorate=directorate)

@app.route('/directorates/<int:directorate_id>/delete')
@login_required
def delete_directorate(directorate_id):
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # جلب المديرية
    directorate = Directorate.query.get_or_404(directorate_id)

    # التحقق من عدم وجود قرى/أحياء مرتبطة بالمديرية
    if directorate.villages:
        flash('لا يمكن حذف المديرية لأنها تحتوي على قرى/أحياء!', 'danger')
        return redirect(url_for('directorates'))

    # حذف المديرية
    db.session.delete(directorate)
    db.session.commit()

    flash('تم حذف المديرية بنجاح!', 'success')
    return redirect(url_for('directorates'))

# مسارات الجداول الترميزية الأخرى

@app.route('/villages')
@login_required
def villages():
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # جلب جميع القرى/الأحياء
    villages = Village.query.all()
    return render_template('reference_tables/villages.html', title='القرى/الأحياء', villages=villages)

@app.route('/villages/add', methods=['GET', 'POST'])
@login_required
def add_village():
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    form = VillageForm()

    # تحديث خيارات القوائم المنسدلة
    form.directorate_id.choices = [(d.id, d.name) for d in Directorate.query.all()]

    if form.validate_on_submit():
        # إنشاء قرية/حي جديد
        village = Village(
            name=form.name.data,
            code=form.code.data,
            directorate_id=form.directorate_id.data
        )

        db.session.add(village)
        db.session.commit()

        flash('تم إضافة القرية/الحي بنجاح!', 'success')
        return redirect(url_for('villages'))

    return render_template('reference_tables/add_village.html', title='إضافة قرية/حي', form=form)

@app.route('/villages/<int:village_id>/edit', methods=['GET', 'POST'])
@login_required
def edit_village(village_id):
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # جلب القرية/الحي
    village = Village.query.get_or_404(village_id)

    form = VillageForm()

    # تحديث خيارات القوائم المنسدلة
    form.directorate_id.choices = [(d.id, d.name) for d in Directorate.query.all()]

    if form.validate_on_submit():
        # تحديث القرية/الحي
        village.name = form.name.data
        village.code = form.code.data
        village.directorate_id = form.directorate_id.data

        db.session.commit()

        flash('تم تحديث القرية/الحي بنجاح!', 'success')
        return redirect(url_for('villages'))

    # ملء النموذج ببيانات القرية/الحي الحالية
    elif request.method == 'GET':
        form.name.data = village.name
        form.code.data = village.code
        form.directorate_id.data = village.directorate_id

    return render_template('reference_tables/edit_village.html', title='تعديل قرية/حي', form=form, village=village)

@app.route('/villages/<int:village_id>/delete')
@login_required
def delete_village(village_id):
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # جلب القرية/الحي
    village = Village.query.get_or_404(village_id)

    # حذف القرية/الحي
    db.session.delete(village)
    db.session.commit()

    flash('تم حذف القرية/الحي بنجاح!', 'success')
    return redirect(url_for('villages'))

@app.route('/marital_statuses')
@login_required
def marital_statuses():
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # جلب جميع الحالات الاجتماعية
    marital_statuses = MaritalStatus.query.all()
    return render_template('reference_tables/marital_statuses.html', title='الحالات الاجتماعية', marital_statuses=marital_statuses)

@app.route('/marital_statuses/add', methods=['GET', 'POST'])
@login_required
def add_marital_status():
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    form = MaritalStatusForm()

    if form.validate_on_submit():
        # إنشاء حالة اجتماعية جديدة
        marital_status = MaritalStatus(
            name=form.name.data
        )

        db.session.add(marital_status)
        db.session.commit()

        flash('تم إضافة الحالة الاجتماعية بنجاح!', 'success')
        return redirect(url_for('marital_statuses'))

    return render_template('reference_tables/add_marital_status.html', title='إضافة حالة اجتماعية', form=form)

@app.route('/marital_statuses/<int:marital_status_id>/edit', methods=['GET', 'POST'])
@login_required
def edit_marital_status(marital_status_id):
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # جلب الحالة الاجتماعية
    marital_status = MaritalStatus.query.get_or_404(marital_status_id)

    form = MaritalStatusForm()

    if form.validate_on_submit():
        # تحديث الحالة الاجتماعية
        marital_status.name = form.name.data

        db.session.commit()

        flash('تم تحديث الحالة الاجتماعية بنجاح!', 'success')
        return redirect(url_for('marital_statuses'))

    # ملء النموذج ببيانات الحالة الاجتماعية الحالية
    elif request.method == 'GET':
        form.name.data = marital_status.name

    return render_template('reference_tables/edit_marital_status.html', title='تعديل حالة اجتماعية', form=form, marital_status=marital_status)

@app.route('/marital_statuses/<int:marital_status_id>/delete')
@login_required
def delete_marital_status(marital_status_id):
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # جلب الحالة الاجتماعية
    marital_status = MaritalStatus.query.get_or_404(marital_status_id)

    # التحقق من عدم وجود بيانات شخصية مرتبطة بالحالة الاجتماعية
    if PersonalData.query.filter_by(marital_status_id=marital_status_id).first():
        flash('لا يمكن حذف الحالة الاجتماعية لأنها مرتبطة ببيانات شخصية!', 'danger')
        return redirect(url_for('marital_statuses'))

    # حذف الحالة الاجتماعية
    db.session.delete(marital_status)
    db.session.commit()

    flash('تم حذف الحالة الاجتماعية بنجاح!', 'success')
    return redirect(url_for('marital_statuses'))

@app.route('/blood_types')
@login_required
def blood_types():
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # جلب جميع فصائل الدم
    blood_types = BloodType.query.all()
    return render_template('reference_tables/blood_types.html', title='فصائل الدم', blood_types=blood_types)

@app.route('/blood_types/add', methods=['GET', 'POST'])
@login_required
def add_blood_type():
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    form = BloodTypeForm()

    if form.validate_on_submit():
        # إنشاء فصيلة دم جديدة
        blood_type = BloodType(
            name=form.name.data
        )

        db.session.add(blood_type)
        db.session.commit()

        flash('تم إضافة فصيلة الدم بنجاح!', 'success')
        return redirect(url_for('blood_types'))

    return render_template('reference_tables/add_blood_type.html', title='إضافة فصيلة دم', form=form)

@app.route('/blood_types/<int:blood_type_id>/edit', methods=['GET', 'POST'])
@login_required
def edit_blood_type(blood_type_id):
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # جلب فصيلة الدم
    blood_type = BloodType.query.get_or_404(blood_type_id)

    form = BloodTypeForm()

    if form.validate_on_submit():
        # تحديث فصيلة الدم
        blood_type.name = form.name.data

        db.session.commit()

        flash('تم تحديث فصيلة الدم بنجاح!', 'success')
        return redirect(url_for('blood_types'))

    # ملء النموذج ببيانات فصيلة الدم الحالية
    elif request.method == 'GET':
        form.name.data = blood_type.name

    return render_template('reference_tables/edit_blood_type.html', title='تعديل فصيلة دم', form=form, blood_type=blood_type)

@app.route('/blood_types/<int:blood_type_id>/delete')
@login_required
def delete_blood_type(blood_type_id):
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # جلب فصيلة الدم
    blood_type = BloodType.query.get_or_404(blood_type_id)

    # التحقق من عدم وجود بيانات شخصية مرتبطة بفصيلة الدم
    if PersonalData.query.filter_by(blood_type_id=blood_type_id).first():
        flash('لا يمكن حذف فصيلة الدم لأنها مرتبطة ببيانات شخصية!', 'danger')
        return redirect(url_for('blood_types'))

    # حذف فصيلة الدم
    db.session.delete(blood_type)
    db.session.commit()

    flash('تم حذف فصيلة الدم بنجاح!', 'success')
    return redirect(url_for('blood_types'))

@app.route('/agencies')
@login_required
def agencies():
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # جلب جميع الجهات
    agencies = Agency.query.all()
    return render_template('reference_tables/agencies.html', title='الجهات', agencies=agencies)

@app.route('/agencies/tree')
@login_required
def agencies_tree():
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # جلب الجهات الرئيسية (التي ليس لها جهة أم)
    root_agencies = Agency.query.filter_by(parent_id=None).all()
    return render_template('reference_tables/agencies_tree.html', title='هيكل الجهات', root_agencies=root_agencies)

@app.route('/agencies/add', methods=['GET', 'POST'])
@login_required
def add_agency():
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    form = AgencyForm()

    # تحديث خيارات القوائم المنسدلة
    form.parent_id.choices = [(0, 'بدون جهة أم')] + [(a.id, a.name) for a in Agency.query.all()]

    if form.validate_on_submit():
        # إنشاء جهة جديدة
        agency = Agency(
            name=form.name.data,
            code=form.code.data,
            parent_id=form.parent_id.data if form.parent_id.data != 0 else None
        )

        db.session.add(agency)
        db.session.commit()

        flash('تم إضافة الجهة بنجاح!', 'success')
        return redirect(url_for('agencies'))

    return render_template('reference_tables/add_agency.html', title='إضافة جهة', form=form)

@app.route('/agencies/<int:agency_id>/edit', methods=['GET', 'POST'])
@login_required
def edit_agency(agency_id):
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # جلب الجهة
    agency = Agency.query.get_or_404(agency_id)

    form = AgencyForm()

    # تحديث خيارات القوائم المنسدلة
    # استبعاد الجهة الحالية وجميع الجهات التابعة لها من خيارات الجهة الأم
    # لمنع تكوين حلقة مغلقة في التسلسل الهرمي
    excluded_ids = [agency_id]

    # الحصول على جميع الجهات التابعة للجهة الحالية
    def get_child_agencies(parent_id):
        children = Agency.query.filter_by(parent_id=parent_id).all()
        result = []
        for child in children:
            result.append(child.id)
            result.extend(get_child_agencies(child.id))
        return result

    excluded_ids.extend(get_child_agencies(agency_id))

    # تحديث خيارات القائمة المنسدلة
    form.parent_id.choices = [(0, 'بدون جهة أم')] + [(a.id, a.name) for a in Agency.query.all() if a.id not in excluded_ids]

    if form.validate_on_submit():
        # تحديث الجهة
        agency.name = form.name.data
        agency.code = form.code.data
        agency.parent_id = form.parent_id.data if form.parent_id.data != 0 else None

        db.session.commit()

        flash('تم تحديث الجهة بنجاح!', 'success')
        return redirect(url_for('agencies'))

    # ملء النموذج ببيانات الجهة الحالية
    elif request.method == 'GET':
        form.name.data = agency.name
        form.code.data = agency.code
        form.parent_id.data = agency.parent_id if agency.parent_id else 0

    return render_template('reference_tables/edit_agency.html', title='تعديل جهة', form=form, agency=agency)

@app.route('/agencies/<int:agency_id>/delete')
@login_required
def delete_agency(agency_id):
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # جلب الجهة
    agency = Agency.query.get_or_404(agency_id)

    # التحقق من عدم وجود جهات تابعة
    if Agency.query.filter_by(parent_id=agency_id).first():
        flash('لا يمكن حذف الجهة لأنها تحتوي على جهات تابعة!', 'danger')
        return redirect(url_for('agencies'))

    # التحقق من عدم وجود دورات مرتبطة بالجهة
    if Course.query.filter_by(agency_id=agency_id).first():
        flash('لا يمكن حذف الجهة لأنها مرتبطة بدورات!', 'danger')
        return redirect(url_for('agencies'))

    # حذف الجهة
    db.session.delete(agency)
    db.session.commit()

    flash('تم حذف الجهة بنجاح!', 'success')
    return redirect(url_for('agencies'))

@app.route('/training_centers')
@login_required
def training_centers():
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # جلب جميع المراكز التدريبية
    centers = TrainingCenter.query.all()
    return render_template('reference_tables/training_centers.html', title='المراكز التدريبية', centers=centers)

@app.route('/training_centers/add', methods=['GET', 'POST'])
@login_required
def add_training_center():
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    form = TrainingCenterForm()

    # تحديث خيارات القوائم المنسدلة
    form.center_type_id.choices = [(0, 'اختر نوع المركز')] + [(t.id, t.name) for t in TrainingCenterType.query.all()]
    form.location_id.choices = [(0, 'اختر الموقع')] + [(l.id, l.name) for l in Location.query.all()]
    form.governorate_id.choices = [(0, 'اختر المحافظة')] + [(g.id, g.name) for g in Governorate.query.all()]
    form.directorate_id.choices = [(0, 'اختر المديرية')] + [(d.id, d.name) for d in Directorate.query.all()]
    form.agency_id.choices = [(0, 'اختر الجهة')] + [(a.id, a.name) for a in Agency.query.all()]

    if form.validate_on_submit():
        # إنشاء مركز تدريبي جديد
        center = TrainingCenter(
            name=form.name.data,
            center_type_id=form.center_type_id.data if form.center_type_id.data != 0 else None,
            location_id=form.location_id.data if form.location_id.data != 0 else None,
            governorate_id=form.governorate_id.data if form.governorate_id.data != 0 else None,
            directorate_id=form.directorate_id.data if form.directorate_id.data != 0 else None,
            agency_id=form.agency_id.data if form.agency_id.data != 0 else None,
            capacity=form.capacity.data,
            is_ready=form.is_ready.data,
            not_ready_reason=form.not_ready_reason.data if not form.is_ready.data else None,
            notes=form.notes.data
        )

        db.session.add(center)
        db.session.commit()

        flash('تم إضافة المركز التدريبي بنجاح!', 'success')
        return redirect(url_for('training_centers'))

    return render_template('reference_tables/add_training_center.html', title='إضافة مركز تدريبي', form=form)

@app.route('/training_centers/<int:center_id>/edit', methods=['GET', 'POST'])
@login_required
def edit_training_center(center_id):
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # جلب المركز التدريبي
    center = TrainingCenter.query.get_or_404(center_id)

    form = TrainingCenterForm()

    # تحديث خيارات القوائم المنسدلة
    form.center_type_id.choices = [(0, 'اختر نوع المركز')] + [(t.id, t.name) for t in TrainingCenterType.query.all()]
    form.location_id.choices = [(0, 'اختر الموقع')] + [(l.id, l.name) for l in Location.query.all()]
    form.governorate_id.choices = [(0, 'اختر المحافظة')] + [(g.id, g.name) for g in Governorate.query.all()]
    form.directorate_id.choices = [(0, 'اختر المديرية')] + [(d.id, d.name) for d in Directorate.query.all()]
    form.agency_id.choices = [(0, 'اختر الجهة')] + [(a.id, a.name) for a in Agency.query.all()]

    if form.validate_on_submit():
        # تحديث بيانات المركز التدريبي
        center.name = form.name.data
        center.center_type_id = form.center_type_id.data if form.center_type_id.data != 0 else None
        center.location_id = form.location_id.data if form.location_id.data != 0 else None
        center.governorate_id = form.governorate_id.data if form.governorate_id.data != 0 else None
        center.directorate_id = form.directorate_id.data if form.directorate_id.data != 0 else None
        center.agency_id = form.agency_id.data if form.agency_id.data != 0 else None
        center.capacity = form.capacity.data
        center.is_ready = form.is_ready.data
        center.not_ready_reason = form.not_ready_reason.data if not form.is_ready.data else None
        center.notes = form.notes.data

        db.session.commit()

        flash('تم تحديث المركز التدريبي بنجاح!', 'success')
        return redirect(url_for('training_centers'))

    # ملء النموذج ببيانات المركز الحالية
    elif request.method == 'GET':
        form.name.data = center.name
        form.center_type_id.data = center.center_type_id if center.center_type_id else 0
        form.location_id.data = center.location_id if center.location_id else 0
        form.governorate_id.data = center.governorate_id if center.governorate_id else 0
        form.directorate_id.data = center.directorate_id if center.directorate_id else 0
        form.agency_id.data = center.agency_id if center.agency_id else 0
        form.capacity.data = center.capacity
        form.is_ready.data = center.is_ready
        form.not_ready_reason.data = center.not_ready_reason
        form.notes.data = center.notes

    return render_template('reference_tables/edit_training_center.html', title='تعديل مركز تدريبي', form=form, center=center)

@app.route('/training_centers/<int:center_id>/delete')
@login_required
def delete_training_center(center_id):
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # جلب المركز التدريبي
    center = TrainingCenter.query.get_or_404(center_id)

    # حذف المركز التدريبي
    db.session.delete(center)
    db.session.commit()

    flash('تم حذف المركز التدريبي بنجاح!', 'success')
    return redirect(url_for('training_centers'))

@app.route('/participant_types')
@login_required
def participant_types():
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # جلب جميع أنواع المشاركين
    participant_types = ParticipantType.query.all()
    return render_template('reference_tables/participant_types.html', title='أنواع المشاركين', participant_types=participant_types)

@app.route('/participant_types/add', methods=['GET', 'POST'])
@login_required
def add_participant_type():
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    form = ParticipantTypeForm()

    if form.validate_on_submit():
        # التحقق من عدم وجود نوع مشاركين بنفس الاسم
        existing_type = ParticipantType.query.filter_by(name=form.name.data).first()
        if existing_type:
            flash(f'نوع المشاركين "{form.name.data}" موجود بالفعل!', 'danger')
            return redirect(url_for('participant_types'))

        # إنشاء نوع مشاركين جديد
        participant_type = ParticipantType(
            name=form.name.data
        )

        db.session.add(participant_type)
        db.session.commit()

        flash('تم إضافة نوع المشاركين بنجاح!', 'success')
        return redirect(url_for('participant_types'))

    return render_template('reference_tables/add_participant_type.html', title='إضافة نوع مشاركين', form=form)

@app.route('/participant_types/<int:participant_type_id>/edit', methods=['GET', 'POST'])
@login_required
def edit_participant_type(participant_type_id):
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # جلب نوع المشاركين
    participant_type = ParticipantType.query.get_or_404(participant_type_id)

    form = ParticipantTypeForm()

    if form.validate_on_submit():
        # تحديث نوع المشاركين
        participant_type.name = form.name.data

        db.session.commit()

        flash('تم تحديث نوع المشاركين بنجاح!', 'success')
        return redirect(url_for('participant_types'))

    # ملء النموذج ببيانات نوع المشاركين الحالي
    elif request.method == 'GET':
        form.name.data = participant_type.name

    return render_template('reference_tables/edit_participant_type.html', title='تعديل نوع مشاركين', form=form, participant_type=participant_type)

@app.route('/participant_types/<int:participant_type_id>/delete')
@login_required
def delete_participant_type(participant_type_id):
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # جلب نوع المشاركين
    participant_type = ParticipantType.query.get_or_404(participant_type_id)

    # التحقق من عدم وجود دورات مرتبطة بنوع المشاركين
    if Course.query.filter_by(participant_type_id=participant_type_id).first():
        flash('لا يمكن حذف نوع المشاركين لأنه مرتبط بدورات!', 'danger')
        return redirect(url_for('participant_types'))

    # حذف نوع المشاركين
    db.session.delete(participant_type)
    db.session.commit()

    flash('تم حذف نوع المشاركين بنجاح!', 'success')
    return redirect(url_for('participant_types'))

@app.route('/card_types')
@login_required
def card_types():
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # جلب جميع أنواع البطاقات
    card_types = CardType.query.all()
    return render_template('reference_tables/card_types.html', title='أنواع البطاقات', card_types=card_types)

@app.route('/card_types/add', methods=['GET', 'POST'])
@login_required
def add_card_type():
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    form = CardTypeForm()

    if form.validate_on_submit():
        # إنشاء نوع بطاقة جديد
        card_type = CardType(
            name=form.name.data
        )

        db.session.add(card_type)
        db.session.commit()

        flash('تم إضافة نوع البطاقة بنجاح!', 'success')
        return redirect(url_for('card_types'))

    return render_template('reference_tables/add_card_type.html', title='إضافة نوع بطاقة', form=form)

@app.route('/card_types/<int:card_type_id>/edit', methods=['GET', 'POST'])
@login_required
def edit_card_type(card_type_id):
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # جلب نوع البطاقة
    card_type = CardType.query.get_or_404(card_type_id)

    form = CardTypeForm()

    if form.validate_on_submit():
        # تحديث بيانات نوع البطاقة
        card_type.name = form.name.data

        db.session.commit()

        flash('تم تحديث نوع البطاقة بنجاح!', 'success')
        return redirect(url_for('card_types'))

    # ملء النموذج ببيانات نوع البطاقة الحالي
    elif request.method == 'GET':
        form.name.data = card_type.name

    return render_template('reference_tables/add_card_type.html', title='تعديل نوع بطاقة', form=form, card_type=card_type)

@app.route('/card_types/<int:card_type_id>/delete')
@login_required
def delete_card_type(card_type_id):
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # جلب نوع البطاقة
    card_type = CardType.query.get_or_404(card_type_id)

    # حذف نوع البطاقة
    db.session.delete(card_type)
    db.session.commit()

    flash('تم حذف نوع البطاقة بنجاح!', 'success')
    return redirect(url_for('card_types'))

@app.route('/course_levels')
@login_required
def course_levels():
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # جلب جميع مستويات الدورات
    levels = CourseLevel.query.all()
    return render_template('reference_tables/course_levels.html', title='مستويات الدورات', levels=levels)

@app.route('/course_levels/add', methods=['GET', 'POST'])
@login_required
def add_course_level():
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    form = CourseLevelForm()

    if form.validate_on_submit():
        # إنشاء مستوى دورة جديد
        course_level = CourseLevel(
            name=form.name.data
        )

        db.session.add(course_level)
        db.session.commit()

        flash('تم إضافة مستوى الدورة بنجاح!', 'success')
        return redirect(url_for('course_levels'))

    return render_template('reference_tables/add_course_level.html', title='إضافة مستوى دورة', form=form)

@app.route('/course_levels/<int:course_level_id>/edit', methods=['GET', 'POST'])
@login_required
def edit_course_level(course_level_id):
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # جلب مستوى الدورة
    course_level = CourseLevel.query.get_or_404(course_level_id)

    form = CourseLevelForm()

    if form.validate_on_submit():
        # تحديث مستوى الدورة
        course_level.name = form.name.data

        db.session.commit()

        flash('تم تحديث مستوى الدورة بنجاح!', 'success')
        return redirect(url_for('course_levels'))

    # ملء النموذج ببيانات مستوى الدورة الحالي
    elif request.method == 'GET':
        form.name.data = course_level.name

    return render_template('reference_tables/edit_course_level.html', title='تعديل مستوى دورة', form=form, course_level=course_level)

@app.route('/course_levels/<int:course_level_id>/delete')
@login_required
def delete_course_level(course_level_id):
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # جلب مستوى الدورة
    course_level = CourseLevel.query.get_or_404(course_level_id)

    # التحقق من عدم وجود دورات مرتبطة بمستوى الدورة
    if Course.query.filter_by(level=course_level.name).first():
        flash('لا يمكن حذف مستوى الدورة لأنه مرتبط بدورات!', 'danger')
        return redirect(url_for('course_levels'))

    # حذف مستوى الدورة
    db.session.delete(course_level)
    db.session.commit()

    flash('تم حذف مستوى الدورة بنجاح!', 'success')
    return redirect(url_for('course_levels'))

# مسارات تصنيفات الدورات
@app.route('/course_categories')
@login_required
def course_categories():
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # جلب جميع تصنيفات الدورات
    categories = CourseCategory.query.all()
    return render_template('reference_tables/course_categories.html', title='تصنيفات الدورات', categories=categories)

@app.route('/course_categories/add', methods=['GET', 'POST'])
@login_required
def add_course_category():
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    form = CourseCategoryForm()

    if form.validate_on_submit():
        # إنشاء تصنيف دورة جديد
        course_category = CourseCategory(
            name=form.name.data,
            code=form.code.data
        )

        db.session.add(course_category)
        db.session.commit()

        flash('تم إضافة تصنيف الدورة بنجاح!', 'success')
        return redirect(url_for('course_categories'))

    return render_template('reference_tables/add_course_category.html', title='إضافة تصنيف دورة', form=form)

@app.route('/course_categories/<int:course_category_id>/edit', methods=['GET', 'POST'])
@login_required
def edit_course_category(course_category_id):
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # جلب تصنيف الدورة
    course_category = CourseCategory.query.get_or_404(course_category_id)
    form = CourseCategoryForm()

    if form.validate_on_submit():
        # تحديث بيانات تصنيف الدورة
        course_category.name = form.name.data
        course_category.code = form.code.data
        db.session.commit()

        flash('تم تحديث تصنيف الدورة بنجاح!', 'success')
        return redirect(url_for('course_categories'))
    elif request.method == 'GET':
        # ملء النموذج ببيانات تصنيف الدورة الحالية
        form.name.data = course_category.name
        form.code.data = course_category.code

    return render_template('reference_tables/edit_course_category.html', title='تعديل تصنيف دورة', form=form, course_category=course_category)

@app.route('/course_categories/<int:course_category_id>/delete')
@login_required
def delete_course_category(course_category_id):
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # جلب تصنيف الدورة
    course_category = CourseCategory.query.get_or_404(course_category_id)

    # التحقق من عدم وجود دورات مرتبطة بتصنيف الدورة
    if Course.query.filter_by(category=course_category.name).first():
        flash('لا يمكن حذف تصنيف الدورة لأنه مرتبط بدورات!', 'danger')
        return redirect(url_for('course_categories'))

    # حذف تصنيف الدورة
    db.session.delete(course_category)
    db.session.commit()

    flash('تم حذف تصنيف الدورة بنجاح!', 'success')
    return redirect(url_for('course_categories'))

@app.route('/reports')
@login_required
def reports():
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    return render_template('reports.html', title='التقارير التفاعلية المتطورة')

@app.route('/reports/dashboard')
@login_required
def reports_dashboard():
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    return render_template('reports_dashboard.html', title='لوحة التقارير التفاعلية')

# Route لإنشاء مستخدم مدير للاختبار (يمكن حذفه لاحقاً)
@app.route('/create-admin-user')
def create_admin_user():
    try:
        # التحقق من وجود مستخدم مدير
        admin = User.query.filter_by(role='admin').first()
        if admin:
            return f"<h1>مستخدم مدير موجود بالفعل:</h1><p>اسم المستخدم: {admin.username}</p><p>البريد: {admin.email}</p><p><a href='/login'>تسجيل الدخول</a></p>"

        # إنشاء مستخدم مدير جديد
        from werkzeug.security import generate_password_hash
        admin = User(
            username='admin',
            email='<EMAIL>',
            password=generate_password_hash('admin123'),
            role='admin'
        )
        db.session.add(admin)
        db.session.commit()

        return "<h1>تم إنشاء مستخدم مدير بنجاح!</h1><p>اسم المستخدم: admin</p><p>كلمة المرور: admin123</p><p><a href='/login'>تسجيل الدخول</a></p>"
    except Exception as e:
        return f"<h1>خطأ في إنشاء المدير:</h1><p>{str(e)}</p>"

# Route لاختبار البيانات
@app.route('/test-data')
def test_data():
    try:
        # جلب إحصائيات البيانات
        courses_count = Course.query.count()
        persons_count = PersonData.query.count()
        participants_count = CourseParticipant.query.count()

        # جلب عينة من البيانات
        sample_courses = Course.query.limit(5).all()
        sample_persons = PersonData.query.limit(5).all()

        html = f"""
        <h1>🔍 اختبار البيانات</h1>
        <h2>📊 الإحصائيات:</h2>
        <ul>
            <li>عدد الدورات: {courses_count}</li>
            <li>عدد الأشخاص: {persons_count}</li>
            <li>عدد المشاركين: {participants_count}</li>
        </ul>

        <h2>📋 عينة من الدورات:</h2>
        <ul>
        """

        for course in sample_courses:
            html += f"<li>{course.course_number}: {course.title}</li>"

        html += """
        </ul>

        <h2>👥 عينة من الأشخاص:</h2>
        <ul>
        """

        for person in sample_persons:
            html += f"<li>{person.full_name} - {person.governorate or 'غير محدد'}</li>"

        html += """
        </ul>

        <p><a href="/login">تسجيل الدخول للنظام</a></p>
        <p><a href="/persons">عرض قائمة الأشخاص</a></p>
        <p><a href="/courses">عرض قائمة الدورات</a></p>
        """

        return html

    except Exception as e:
        return f"<h1>خطأ في جلب البيانات:</h1><p>{str(e)}</p>"

# Route لإنشاء بيانات وهمية للاختبار
@app.route('/create-sample-data')
@login_required
def create_sample_data():
    try:
        # التحقق من أن المستخدم هو مدير
        if current_user.role != 'admin':
            return "<h1>ليس لديك صلاحية للوصول إلى هذه الصفحة</h1>"

        # إنشاء دورات وهمية
        sample_courses = [
            {
                'course_number': 'C001',
                'title': 'دورة الأمن السيبراني',
                'agency': 'وزارة الداخلية',
                'center_name': 'مركز التدريب الأمني',
                'status': 'المستوى الأول',
                'start_date': datetime(2024, 1, 15),
                'end_date': datetime(2024, 1, 30),
                'total_participants': 25,
                'total_graduates': 23,
                'total_dropouts': 2,
                'duration_days': 15
            },
            {
                'course_number': 'C002',
                'title': 'دورة إدارة المشاريع',
                'agency': 'وزارة التخطيط',
                'center_name': 'معهد الإدارة العامة',
                'status': 'المستوى الثاني',
                'start_date': datetime(2024, 2, 1),
                'end_date': datetime(2024, 2, 20),
                'total_participants': 30,
                'total_graduates': 28,
                'total_dropouts': 2,
                'duration_days': 20
            },
            {
                'course_number': 'C003',
                'title': 'دورة التطوير المهني',
                'agency': 'وزارة التربية',
                'center_name': 'مركز التطوير التربوي',
                'status': 'المستوى الثالث',
                'start_date': datetime(2024, 3, 1),
                'end_date': datetime(2024, 3, 25),
                'total_participants': 20,
                'total_graduates': 18,
                'total_dropouts': 2,
                'duration_days': 25
            }
        ]

        for course_data in sample_courses:
            # التحقق من عدم وجود الدورة
            existing_course = Course.query.filter_by(course_number=course_data['course_number']).first()
            if not existing_course:
                course = Course(**course_data)
                db.session.add(course)

        db.session.commit()
        return "<h1>تم إنشاء البيانات الوهمية بنجاح!</h1><p><a href='/reports/dashboard'>عرض التقارير</a></p>"
    except Exception as e:
        return f"<h1>خطأ في إنشاء البيانات:</h1><p>{str(e)}</p>"

@app.route('/generate_report', methods=['GET', 'POST'])
@csrf.exempt  # استثناء حماية CSRF لهذا المسار
@login_required
def generate_report():
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        return jsonify({
            'success': False,
            'message': 'ليس لديك صلاحية للوصول إلى هذه الوظيفة'
        })

    if request.method == 'POST':
        # طباعة البيانات المستلمة للتشخيص
        print("🔍 البيانات المستلمة:", dict(request.form))
        print("🔍 Content-Type:", request.content_type)

        start_date = request.form.get('start_date')
        end_date = request.form.get('end_date')

        print(f"📅 start_date: {start_date}")
        print(f"📅 end_date: {end_date}")

        if not start_date or not end_date:
            return jsonify({
                'success': False,
                'message': f'يجب تحديد تاريخ البداية والنهاية. البيانات المستلمة: {dict(request.form)}'
            }), 400

        # التحقق من صحة التواريخ
        try:
            datetime.strptime(start_date, '%Y-%m-%d')
            datetime.strptime(end_date, '%Y-%m-%d')
        except ValueError:
            return jsonify({
                'success': False,
                'message': 'تنسيق التاريخ غير صحيح'
            })

        try:
            # تحويل التواريخ
            start_date_obj = datetime.strptime(start_date, '%Y-%m-%d')
            end_date_obj = datetime.strptime(end_date, '%Y-%m-%d')

            # جلب الدورات في الفترة المحددة
            courses = Course.query.filter(
                Course.start_date >= start_date_obj,
                Course.start_date <= end_date_obj
            ).all()

            # حساب الإحصائيات
            total_courses = len(courses)
            total_participants = sum(course.total_participants or 0 for course in courses)
            total_graduates = sum(course.total_graduates or 0 for course in courses)

            # حساب عدد المراكز الفريدة
            centers = set()
            for course in courses:
                if course.center and course.center.name:
                    centers.add(course.center.name)
            total_centers = len(centers)

            # تجميع البيانات حسب المستوى
            level_data = {'level1': 0, 'level2': 0, 'level3': 0}
            center_data = {}

            for course in courses:
                # تجميع حسب المستوى
                if course.level:
                    level_name = course.level
                    if level_name in ['مبتدئ', 'أساسي', 'beginner', '1'] or 'أول' in level_name:
                        level_data['level1'] += course.total_participants or 0
                    elif level_name in ['متوسط', 'intermediate', '2'] or 'ثان' in level_name:
                        level_data['level2'] += course.total_participants or 0
                    elif level_name in ['متقدم', 'advanced', '3'] or 'ثالث' in level_name:
                        level_data['level3'] += course.total_participants or 0

                # تجميع حسب المركز
                center_name = (course.center.name if course.center else None) or 'غير محدد'
                if center_name not in center_data:
                    center_data[center_name] = 0
                center_data[center_name] += course.total_participants or 0

            # إعداد بيانات الجداول
            level_table_data = []
            participants_table_data = []

            # جدول المستويات (مبسط)
            for center_name, participants in center_data.items():
                def get_center_courses(center_name, level_keywords):
                    return [c for c in courses
                           if ((c.center.name if c.center else None) or 'غير محدد') == center_name
                           and c.level and any(keyword in c.level or c.level in level_keywords for keyword in ['أول', 'ثان', 'ثالث'])]

                level1_courses = [c for c in courses if ((c.center.name if c.center else None) or 'غير محدد') == center_name and c.level and (c.level in ['مبتدئ', 'أساسي', 'beginner', '1'] or 'أول' in c.level)]
                level2_courses = [c for c in courses if ((c.center.name if c.center else None) or 'غير محدد') == center_name and c.level and (c.level in ['متوسط', 'intermediate', '2'] or 'ثان' in c.level)]
                level3_courses = [c for c in courses if ((c.center.name if c.center else None) or 'غير محدد') == center_name and c.level and (c.level in ['متقدم', 'advanced', '3'] or 'ثالث' in c.level)]

                level_table_data.append({
                    'center': center_name,
                    'level1_courses': len(level1_courses),
                    'level1_participants': sum(c.total_participants or 0 for c in level1_courses),
                    'level1_graduates': sum(c.total_graduates or 0 for c in level1_courses),
                    'level2_courses': len(level2_courses),
                    'level2_participants': sum(c.total_participants or 0 for c in level2_courses),
                    'level2_graduates': sum(c.total_graduates or 0 for c in level2_courses),
                    'level3_courses': len(level3_courses),
                    'level3_participants': sum(c.total_participants or 0 for c in level3_courses),
                    'level3_graduates': sum(c.total_graduates or 0 for c in level3_courses),
                    'total': participants
                })

            # جدول المشاركين
            for i, course in enumerate(courses[:20]):  # أول 20 دورة
                participants_table_data.append({
                    'agency': (course.agency.name if course.agency else None) or 'غير محدد',
                    'center': (course.center.name if course.center else None) or 'غير محدد',
                    'director': 'غير محدد',  # يمكن إضافة هذا الحقل لاحقاً
                    'level': course.level or 'غير محدد',
                    'duration': f"{course.duration_days or 0} يوم",
                    'participants': course.total_participants or 0,
                    'notes': course.notes or 'لا توجد ملاحظات'
                })

            # إحصائيات إضافية مطلوبة

            # 1. توزيع الدورات حسب مسارات التدريب
            course_path_stats = db.session.query(
                CoursePath.name,
                db.func.count(Course.id).label('count')
            ).join(Course, Course.path_id == CoursePath.id)\
             .filter(CoursePath.name.isnot(None))\
             .filter(CoursePath.name != '')\
             .group_by(CoursePath.name).all()

            # 2. توزيع المراكز حسب الجهات
            center_agency_stats = db.session.query(
                Agency.name,
                db.func.count(TrainingCenter.id).label('count')
            ).join(TrainingCenter, TrainingCenter.agency_id == Agency.id)\
             .filter(Agency.name.isnot(None))\
             .filter(Agency.name != '')\
             .group_by(Agency.name).all()

            # 3. توزيع الدورات حسب المواقع (مراكز التدريب)
            course_location_stats = db.session.query(
                TrainingCenter.name,
                db.func.count(Course.id).label('count')
            ).join(Course, Course.center_id == TrainingCenter.id)\
             .filter(TrainingCenter.name.isnot(None))\
             .filter(TrainingCenter.name != '')\
             .group_by(TrainingCenter.name).all()

            # 4. توزيع المشاركين حسب أنواع المشاركين
            participant_type_stats = db.session.query(
                PersonData.job,
                db.func.count(CourseParticipant.id).label('count')
            ).join(CourseParticipant, CourseParticipant.personal_data_id == PersonData.id)\
             .filter(PersonData.job.isnot(None))\
             .filter(PersonData.job != '')\
             .group_by(PersonData.job).all()

            # 5. توزيع المشاركين حسب تصنيفات القوة
            force_classification_stats = db.session.query(
                PersonData.agency,
                db.func.count(CourseParticipant.id).label('count')
            ).join(CourseParticipant, CourseParticipant.personal_data_id == PersonData.id)\
             .filter(PersonData.agency.isnot(None))\
             .filter(PersonData.agency != '')\
             .group_by(PersonData.agency).all()

            # إعداد البيانات للإرجاع
            report_data = {
                'success': True,
                'totalCourses': total_courses,
                'totalParticipants': total_participants,
                'totalCenters': total_centers,
                'totalGraduates': total_graduates,
                'chartData': {
                    'level1': level_data['level1'],
                    'level2': level_data['level2'],
                    'level3': level_data['level3'],
                    'centerNames': list(center_data.keys())[:10],  # أول 10 مراكز
                    'centerParticipants': list(center_data.values())[:10],
                    # الإحصائيات الجديدة
                    'coursePathStats': [{'name': stat.name, 'count': stat.count} for stat in course_path_stats],
                    'centerAgencyStats': [{'name': stat.name, 'count': stat.count} for stat in center_agency_stats],
                    'courseLocationStats': [{'name': stat.name, 'count': stat.count} for stat in course_location_stats],
                    'participantTypeStats': [{'name': stat.job, 'count': stat.count} for stat in participant_type_stats],
                    'forceClassificationStats': [{'name': stat.agency, 'count': stat.count} for stat in force_classification_stats]
                },
                'tableData': {
                    'levelData': level_table_data,
                    'participantsData': participants_table_data
                }
            }

            return jsonify(report_data)

        except Exception as e:
            print(f"❌ خطأ في إنشاء التقرير: {str(e)}")
            import traceback
            traceback.print_exc()
            return jsonify({
                'success': False,
                'message': f'حدث خطأ في إنشاء التقرير: {str(e)}'
            }), 500

    return render_template('reports_dashboard.html', title='لوحة التقارير التفاعلية')

@app.route('/graduates')
@login_required
def graduates():
    # التحقق من أن المستخدم هو مدرب أو مدير
    if current_user.role not in ['trainer', 'admin']:
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # جلب المشاركين الذين أكملوا الدورات
    graduates = CourseParticipant.query.filter_by(status='completed').all()

    return render_template('graduates.html', title='الخريجين', graduates=graduates)

# مسار المدربين
@app.route('/trainers')
@login_required
def trainers():
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # جلب المستخدمين من نوع مدرب
    trainers = User.query.filter_by(role='trainer').all()

    return render_template('trainers.html', title='المدربين', trainers=trainers)

# مسار الدفعات
@app.route('/batches')
@login_required
def batches():
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # جلب جميع الدفعات
    batches = Batch.query.all()

    return render_template('batches.html', title='الدفعات', batches=batches)

# مسار بديل للدفعات
@app.route('/course_batches')
@login_required
def course_batches():
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # جلب جميع الدفعات
    batches = Batch.query.all()

    return render_template('batches.html', title='الدفعات', batches=batches)

@app.route('/batches/add', methods=['GET', 'POST'])
@login_required
def add_batch():
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    form = BatchForm()

    # تحديث خيارات القوائم المنسدلة
    form.course_id.choices = [(c.id, f"{c.course_number} - {c.title}") for c in Course.query.all()]

    if form.validate_on_submit():
        # إنشاء دفعة جديدة
        batch = Batch(
            name=form.name.data,
            code=form.code.data,
            start_date=form.start_date.data,
            end_date=form.end_date.data,
            course_id=form.course_id.data,
            max_participants=form.max_participants.data,
            notes=form.notes.data
        )

        db.session.add(batch)
        db.session.commit()

        flash('تم إضافة الدفعة بنجاح!', 'success')
        return redirect(url_for('batches'))

    return render_template('batches/add_batch.html', title='إضافة دفعة', form=form)

@app.route('/batches/<int:batch_id>/edit', methods=['GET', 'POST'])
@login_required
def edit_batch(batch_id):
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # جلب الدفعة
    batch = Batch.query.get_or_404(batch_id)

    form = BatchForm()

    # تحديث خيارات القوائم المنسدلة
    form.course_id.choices = [(c.id, f"{c.course_number} - {c.title}") for c in Course.query.all()]

    if form.validate_on_submit():
        # تحديث بيانات الدفعة
        batch.name = form.name.data
        batch.code = form.code.data
        batch.start_date = form.start_date.data
        batch.end_date = form.end_date.data
        batch.course_id = form.course_id.data
        batch.max_participants = form.max_participants.data
        batch.notes = form.notes.data

        db.session.commit()

        flash('تم تحديث الدفعة بنجاح!', 'success')
        return redirect(url_for('batches'))

    # ملء النموذج ببيانات الدفعة الحالية
    elif request.method == 'GET':
        form.name.data = batch.name
        form.code.data = batch.code
        form.start_date.data = batch.start_date
        form.end_date.data = batch.end_date
        form.course_id.data = batch.course_id
        form.max_participants.data = batch.max_participants
        form.notes.data = batch.notes

    return render_template('batches/edit_batch.html', title='تعديل دفعة', form=form, batch=batch)

@app.route('/batches/<int:batch_id>/delete')
@login_required
def delete_batch(batch_id):
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # جلب الدفعة
    batch = Batch.query.get_or_404(batch_id)

    # التحقق من عدم وجود مشاركين في الدفعة
    if BatchParticipant.query.filter_by(batch_id=batch_id).first():
        flash('لا يمكن حذف الدفعة لأنها تحتوي على مشاركين!', 'danger')
        return redirect(url_for('batches'))

    # حذف الدفعة
    db.session.delete(batch)
    db.session.commit()

    flash('تم حذف الدفعة بنجاح!', 'success')
    return redirect(url_for('batches'))





@app.route('/reports/courses')
@login_required
def course_reports():
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # جلب جميع الدورات
    courses = Course.query.all()

    # جلب إحصائيات الدورات
    total_courses = len(courses)
    total_participants = sum(course.total_participants or 0 for course in courses)
    total_graduates = sum(course.total_graduates or 0 for course in courses)
    total_dropouts = sum(course.total_dropouts or 0 for course in courses)

    # حساب إجمالي المبالغ المالية
    total_daily_allowance = sum(course.daily_allowance or 0 for course in courses)
    total_transportation_allowance = sum(course.transportation_allowance or 0 for course in courses)
    total_accommodation_allowance = sum(course.accommodation_allowance or 0 for course in courses)
    total_allowance = sum(course.total_allowance or 0 for course in courses)

    # تجميع الدورات حسب الشهر والسنة
    courses_by_month = {}
    for course in courses:
        month_year = course.start_date.strftime('%Y-%m')
        if month_year not in courses_by_month:
            courses_by_month[month_year] = []
        courses_by_month[month_year].append(course)

    # ترتيب الأشهر تنازليًا
    sorted_months = sorted(courses_by_month.keys(), reverse=True)

    return render_template('course_reports.html', title='تقارير الدورات',
                          courses=courses, total_courses=total_courses,
                          total_participants=total_participants, total_graduates=total_graduates,
                          total_dropouts=total_dropouts, total_daily_allowance=total_daily_allowance,
                          total_transportation_allowance=total_transportation_allowance,
                          total_accommodation_allowance=total_accommodation_allowance,
                          total_allowance=total_allowance, courses_by_month=courses_by_month,
                          sorted_months=sorted_months)

@app.route('/reports/courses/monthly/<month_year>')
@login_required
def monthly_course_report(month_year):
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # تحليل الشهر والسنة
    try:
        year, month = map(int, month_year.split('-'))
        start_date = datetime(year, month, 1)
        if month == 12:
            end_date = datetime(year + 1, 1, 1) - timedelta(days=1)
        else:
            end_date = datetime(year, month + 1, 1) - timedelta(days=1)
    except ValueError:
        flash('تنسيق التاريخ غير صحيح', 'danger')
        return redirect(url_for('course_reports'))

    # جلب الدورات في الشهر المحدد
    courses = Course.query.filter(Course.start_date >= start_date, Course.start_date <= end_date).all()

    # جلب إحصائيات الدورات
    total_courses = len(courses)
    total_participants = sum(course.total_participants or 0 for course in courses)
    total_graduates = sum(course.total_graduates or 0 for course in courses)
    total_dropouts = sum(course.total_dropouts or 0 for course in courses)

    # حساب إجمالي المبالغ المالية
    total_daily_allowance = sum(course.daily_allowance or 0 for course in courses)
    total_transportation_allowance = sum(course.transportation_allowance or 0 for course in courses)
    total_accommodation_allowance = sum(course.accommodation_allowance or 0 for course in courses)
    total_allowance = sum(course.total_allowance or 0 for course in courses)

    # تنسيق اسم الشهر بالعربية
    month_names = {
        1: 'يناير', 2: 'فبراير', 3: 'مارس', 4: 'أبريل', 5: 'مايو', 6: 'يونيو',
        7: 'يوليو', 8: 'أغسطس', 9: 'سبتمبر', 10: 'أكتوبر', 11: 'نوفمبر', 12: 'ديسمبر'
    }
    month_name = month_names.get(month, '')
    report_title = f'تقرير دورات شهر {month_name} {year}'

    return render_template('monthly_course_report.html', title=report_title,
                          courses=courses, month_year=month_year, month_name=month_name, year=year,
                          total_courses=total_courses, total_participants=total_participants,
                          total_graduates=total_graduates, total_dropouts=total_dropouts,
                          total_daily_allowance=total_daily_allowance,
                          total_transportation_allowance=total_transportation_allowance,
                          total_accommodation_allowance=total_accommodation_allowance,
                          total_allowance=total_allowance)

@app.route('/reports/courses/export/<month_year>')
@login_required
def export_course_report(month_year):
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # تحليل الشهر والسنة
    try:
        year, month = map(int, month_year.split('-'))
        start_date = datetime(year, month, 1)
        if month == 12:
            end_date = datetime(year + 1, 1, 1) - timedelta(days=1)
        else:
            end_date = datetime(year, month + 1, 1) - timedelta(days=1)
    except ValueError:
        flash('تنسيق التاريخ غير صحيح', 'danger')
        return redirect(url_for('course_reports'))

    # جلب الدورات في الشهر المحدد
    courses = Course.query.filter(Course.start_date >= start_date, Course.start_date <= end_date).all()

    # تنسيق اسم الشهر بالعربية
    month_names = {
        1: 'يناير', 2: 'فبراير', 3: 'مارس', 4: 'أبريل', 5: 'مايو', 6: 'يونيو',
        7: 'يوليو', 8: 'أغسطس', 9: 'سبتمبر', 10: 'أكتوبر', 11: 'نوفمبر', 12: 'ديسمبر'
    }
    month_name = month_names.get(month, '')

    # إنشاء ملف CSV
    output = io.StringIO()
    writer = csv.writer(output)

    # كتابة رأس الملف
    writer.writerow(['رقم الدورة', 'عنوان الدورة', 'الجهة', 'اسم المركز', 'نوع المشاركين', 'رقم البطاقة', 'المستوى', 'تاريخ الدخول', 'تاريخ الخروج', 'عدد المشاركين', 'عدد الخريجين', 'عدد المنسحبين', 'مبلغ الإعاشة اليومي', 'مبلغ المواصلات', 'مبلغ السكن', 'إجمالي المبلغ', 'ملاحظات'])

    # كتابة بيانات الدورات
    for course in courses:
        writer.writerow([
            course.course_number,
            course.title,
            course.agency,
            course.center_name,
            course.participant_type,
            course.card_number,
            course.status,
            course.entry_date.strftime('%Y-%m-%d') if course.entry_date else '',
            course.exit_date.strftime('%Y-%m-%d') if course.exit_date else '',
            course.total_participants,
            course.total_graduates,
            course.total_dropouts,
            course.daily_allowance,
            course.transportation_allowance,
            course.accommodation_allowance,
            course.total_allowance,
            course.notes
        ])

    # إرجاع الملف للتنزيل
    output.seek(0)
    return Response(
        output,
        mimetype='text/csv',
        headers={'Content-Disposition': f'attachment;filename=course_report_{month_year}.csv'}
    )

@app.route('/departments')
@login_required
def departments():
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # جلب جميع الأقسام
    departments = Department.query.all()
    return render_template('reference_tables/departments.html', title='الأقسام', departments=departments)

@app.route('/departments/tree')
@login_required
def departments_tree():
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # جلب الأقسام الرئيسية (التي ليس لها قسم أب)
    root_departments = Department.query.filter_by(parent_id=None).all()
    return render_template('reference_tables/departments_tree.html', title='هيكل الأقسام', root_departments=root_departments)

@app.route('/departments/add', methods=['GET', 'POST'])
@login_required
def add_department():
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    form = DepartmentForm()

    # تحديث خيارات القوائم المنسدلة
    form.parent_id.choices = [(0, 'بدون قسم أب')] + [(d.id, d.name) for d in Department.query.all()]
    form.agency_id.choices = [(0, 'بدون جهة')] + [(a.id, a.name) for a in Agency.query.all()]

    if form.validate_on_submit():
        # إنشاء قسم جديد
        department = Department(
            name=form.name.data,
            code=form.code.data,
            parent_id=form.parent_id.data if form.parent_id.data != 0 else None,
            agency_id=form.agency_id.data if form.agency_id.data != 0 else None
        )

        db.session.add(department)
        db.session.commit()

        flash('تم إضافة القسم بنجاح!', 'success')
        return redirect(url_for('departments'))

    return render_template('reference_tables/add_department.html', title='إضافة قسم', form=form)

@app.route('/departments/<int:department_id>/edit', methods=['GET', 'POST'])
@login_required
def edit_department(department_id):
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # جلب القسم
    department = Department.query.get_or_404(department_id)

    form = DepartmentForm()

    # تحديث خيارات القوائم المنسدلة
    # استبعاد القسم الحالي وأقسامه الفرعية من خيارات القسم الأب لمنع الدورات
    excluded_ids = [department_id]

    def get_child_ids(dept_id):
        children = Department.query.filter_by(parent_id=dept_id).all()
        child_ids = []
        for child in children:
            child_ids.append(child.id)
            child_ids.extend(get_child_ids(child.id))
        return child_ids

    excluded_ids.extend(get_child_ids(department_id))

    available_departments = Department.query.filter(~Department.id.in_(excluded_ids)).all()
    form.parent_id.choices = [(0, 'بدون قسم أب')] + [(d.id, d.name) for d in available_departments]
    form.agency_id.choices = [(0, 'بدون جهة')] + [(a.id, a.name) for a in Agency.query.all()]

    if form.validate_on_submit():
        # تحديث بيانات القسم
        department.name = form.name.data
        department.code = form.code.data
        department.parent_id = form.parent_id.data if form.parent_id.data != 0 else None
        department.agency_id = form.agency_id.data if form.agency_id.data != 0 else None

        db.session.commit()

        flash('تم تحديث القسم بنجاح!', 'success')
        return redirect(url_for('departments'))

    # ملء النموذج ببيانات القسم الحالي
    elif request.method == 'GET':
        form.name.data = department.name
        form.code.data = department.code
        form.parent_id.data = department.parent_id if department.parent_id else 0
        form.agency_id.data = department.agency_id if department.agency_id else 0

    return render_template('reference_tables/edit_department.html', title='تعديل قسم', form=form, department=department)

@app.route('/departments/<int:department_id>/delete')
@login_required
def delete_department(department_id):
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # جلب القسم
    department = Department.query.get_or_404(department_id)

    # التحقق من عدم وجود أقسام فرعية
    if Department.query.filter_by(parent_id=department_id).first():
        flash('لا يمكن حذف القسم لأنه يحتوي على أقسام فرعية!', 'danger')
        return redirect(url_for('departments'))

    # حذف القسم
    db.session.delete(department)
    db.session.commit()

    flash('تم حذف القسم بنجاح!', 'success')
    return redirect(url_for('departments'))

@app.context_processor
def inject_now():
    # استخدام datetime.now() بدون timezone للتبسيط
    return {'now': datetime.now()}

# وظائف مساعدة لمعالجة البيانات

def correct_arabic_name(name):
    """تصحيح الأسماء العربية وإزالة الأخطاء الشائعة"""
    if not isinstance(name, str) or not name.strip():
        return name

    import re

    # إزالة الفراغات الزائدة
    name = ' '.join(name.split())

    # إزالة الأرقام من نهاية الاسم
    name = re.sub(r'\d+$', '', name)

    # تصحيح الهمزات
    hamza_corrections = {
        r'\bاحمد\b': 'أحمد',
        r'\bابراهيم\b': 'إبراهيم',
        r'\bاسماعيل\b': 'إسماعيل',
        r'\bايمن\b': 'أيمن',
        r'\bانس\b': 'أنس',
        r'\bاسامة\b': 'أسامة',
        r'\bامين\b': 'أمين',
        r'\bابو\b': 'أبو',
        r'\bام\b': 'أم',
    }

    for wrong, correct in hamza_corrections.items():
        name = re.sub(wrong, correct, name, flags=re.IGNORECASE)

    # تصحيح الألف المقصورة
    alif_corrections = {
        r'\bعيسي\b': 'عيسى',
        r'\bموسي\b': 'موسى',
        r'\bيحيي\b': 'يحيى',
        r'\bمصطفي\b': 'مصطفى',
        r'\bمرتضي\b': 'مرتضى',
    }

    for wrong, correct in alif_corrections.items():
        name = re.sub(wrong, correct, name, flags=re.IGNORECASE)

    # تصحيح الأسماء المركبة
    compound_corrections = {
        r'\bعبد\s+الله\b': 'عبدالله',
        r'\bعبد\s+الرحمن\b': 'عبدالرحمن',
        r'\bعبد\s+العزيز\b': 'عبدالعزيز',
        r'\bعبد\s+الكريم\b': 'عبدالكريم',
        r'\bعبد\s+المجيد\b': 'عبدالمجيد',
        r'\bعبد\s+الرحيم\b': 'عبدالرحيم',
        r'\bمحمد\s+على\b': 'محمد علي',
        r'\bعلى\b': 'علي',
        r'\bعبداللة\b': 'عبدالله',
    }

    for wrong, correct in compound_corrections.items():
        name = re.sub(wrong, correct, name, flags=re.IGNORECASE)

    # إزالة الرموز غير المرغوبة
    name = re.sub(r'[^\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF\s]', '', name)

    return name.strip()

def compare_dataframes(df1, df2):
    """مقارنة بين إطاري بيانات واستخراج الاختلافات والتشابهات"""
    # تحضير النتائج
    results = {
        'total_records': len(df1) + len(df2),
        'matching_data': [],
        'different_data': [],
        'unique_file1': [],
        'unique_file2': [],
        'duplicates': [],
        'corrections': []
    }

    # البحث عن السجلات المكررة في كل ملف
    duplicates_df1 = df1[df1.duplicated(['الرقم الوطني'], keep=False)]
    duplicates_df2 = df2[df2.duplicated(['الرقم الوطني'], keep=False)]

    for _, row in duplicates_df1.iterrows():
        results['duplicates'].append({
            'name': row['الاسم الكامل'],
            'national_id': row['الرقم الوطني'],
            'count': len(df1[df1['الرقم الوطني'] == row['الرقم الوطني']]),
            'file': 'الملف الأول'
        })

    for _, row in duplicates_df2.iterrows():
        results['duplicates'].append({
            'name': row['الاسم الكامل'],
            'national_id': row['الرقم الوطني'],
            'count': len(df2[df2['الرقم الوطني'] == row['الرقم الوطني']]),
            'file': 'الملف الثاني'
        })

    # إزالة التكرارات من قائمة المكررات
    unique_duplicates = []
    seen = set()
    for item in results['duplicates']:
        key = (item['national_id'], item['file'])
        if key not in seen:
            seen.add(key)
            unique_duplicates.append(item)
    results['duplicates'] = unique_duplicates

    # مقارنة السجلات بين الملفين
    df1_ids = set(df1['الرقم الوطني'])
    df2_ids = set(df2['الرقم الوطني'])

    # السجلات المشتركة
    common_ids = df1_ids.intersection(df2_ids)

    # السجلات الفريدة في كل ملف
    unique_df1_ids = df1_ids - df2_ids
    unique_df2_ids = df2_ids - df1_ids

    # استخراج السجلات الفريدة في الملف الأول
    for id_val in unique_df1_ids:
        row = df1[df1['الرقم الوطني'] == id_val].iloc[0]
        record = {
            'name': row['الاسم الكامل'],
            'national_id': row['الرقم الوطني'],
            'phone': row['رقم الهاتف'] if 'رقم الهاتف' in row else '',
            'agency': row['الجهة'] if 'الجهة' in row else ''
        }
        results['unique_file1'].append(record)

    # استخراج السجلات الفريدة في الملف الثاني
    for id_val in unique_df2_ids:
        row = df2[df2['الرقم الوطني'] == id_val].iloc[0]
        record = {
            'name': row['الاسم الكامل'],
            'national_id': row['الرقم الوطني'],
            'phone': row['رقم الهاتف'] if 'رقم الهاتف' in row else '',
            'agency': row['الجهة'] if 'الجهة' in row else ''
        }
        results['unique_file2'].append(record)

    # مقارنة السجلات المشتركة
    for id_val in common_ids:
        row1 = df1[df1['الرقم الوطني'] == id_val].iloc[0]
        row2 = df2[df2['الرقم الوطني'] == id_val].iloc[0]

        # مقارنة الاسم
        name1 = row1['الاسم الكامل']
        name2 = row2['الاسم الكامل']

        # مقارنة رقم الهاتف إذا كان موجوداً
        phone1 = row1['رقم الهاتف'] if 'رقم الهاتف' in row1 else ''
        phone2 = row2['رقم الهاتف'] if 'رقم الهاتف' in row2 else ''

        # مقارنة الجهة إذا كانت موجودة
        agency1 = row1['الجهة'] if 'الجهة' in row1 else ''
        agency2 = row2['الجهة'] if 'الجهة' in row2 else ''

        # التحقق من التطابق
        if name1 == name2 and phone1 == phone2 and agency1 == agency2:
            # السجلات متطابقة
            results['matching_data'].append({
                'name': name1,
                'national_id': id_val,
                'phone': phone1,
                'agency': agency1
            })
        else:
            # السجلات مختلفة
            if name1 != name2:
                results['different_data'].append({
                    'field': 'الاسم الكامل',
                    'value1': name1,
                    'value2': name2
                })

                # اقتراح تصحيح للاسم
                similarity = difflib.SequenceMatcher(None, name1, name2).ratio()
                if similarity > 0.7:  # إذا كان التشابه أكبر من 70%
                    results['corrections'].append({
                        'type': 'تصحيح اسم',
                        'original': name1 if len(name1) < len(name2) else name2,
                        'corrected': name2 if len(name1) < len(name2) else name1
                    })

            if phone1 != phone2 and phone1 and phone2:
                results['different_data'].append({
                    'field': 'رقم الهاتف',
                    'value1': phone1,
                    'value2': phone2
                })

            if agency1 != agency2 and agency1 and agency2:
                results['different_data'].append({
                    'field': 'الجهة',
                    'value1': agency1,
                    'value2': agency2
                })

    return results

@app.route('/backup', methods=['GET', 'POST'])
@login_required
def backup():
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    form = BackupForm()

    if form.validate_on_submit():
        success, message, backup_file = backup_utils.create_backup(
            backup_path=form.backup_path.data if form.backup_path.data else None,
            backup_name=form.backup_name.data if form.backup_name.data else None,
            include_personal_data=form.include_personal_data.data,
            include_courses=form.include_courses.data,
            include_uploads=form.include_uploads.data
        )

        if success:
            flash(message, 'success')
        else:
            flash(message, 'danger')

        return redirect(url_for('backup'))

    # الحصول على قائمة النسخ الاحتياطية
    backups = backup_utils.list_backups()

    return render_template('backup/backup.html', title='النسخ الاحتياطي', form=form, backups=backups)

@app.route('/backup/download/<path:filename>')
@login_required
def download_backup(filename):
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # الحصول على مسار مجلد النسخ الاحتياطية
    backup_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'backups')

    # التحقق من وجود الملف
    file_path = os.path.join(backup_path, filename)
    if not os.path.exists(file_path):
        flash('الملف غير موجود', 'danger')
        return redirect(url_for('backup'))

    return send_file(file_path, as_attachment=True)

@app.route('/backup/restore/<path:filename>')
@login_required
def restore_backup(filename):
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # الحصول على مسار مجلد النسخ الاحتياطية
    backup_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'backups')

    # التحقق من وجود الملف
    file_path = os.path.join(backup_path, filename)
    if not os.path.exists(file_path):
        flash('الملف غير موجود', 'danger')
        return redirect(url_for('backup'))

    # استعادة النسخة الاحتياطية
    success, message = backup_utils.restore_backup(file_path)

    if success:
        flash(message, 'success')
    else:
        flash(message, 'danger')

    return redirect(url_for('backup'))

# ===== النظام الذكي للنسخ الاحتياطي =====

@app.route('/smart_backup_status')
@login_required
def smart_backup_status():
    """عرض حالة النظام الذكي للنسخ الاحتياطي"""
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    try:
        status = smart_backup_manager.get_backup_status()
        return jsonify({
            'success': True,
            'status': status
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': str(e)
        })

@app.route('/smart_backup_force')
@login_required
def smart_backup_force():
    """إجبار إنشاء نسخة احتياطية فورية"""
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    try:
        success, message, backup_file = smart_backup_manager.force_backup()
        if success:
            flash(f'تم إنشاء النسخة الاحتياطية بنجاح: {backup_file}', 'success')
        else:
            flash(f'فشل في إنشاء النسخة الاحتياطية: {message}', 'danger')
    except Exception as e:
        flash(f'خطأ: {str(e)}', 'danger')

    return redirect(url_for('backup'))

@app.route('/backup/delete/<path:filename>')
@login_required
def delete_backup(filename):
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # الحصول على مسار مجلد النسخ الاحتياطية
    backup_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'backups')

    # التحقق من وجود الملف
    file_path = os.path.join(backup_path, filename)
    if not os.path.exists(file_path):
        flash('الملف غير موجود', 'danger')
        return redirect(url_for('backup'))

    # حذف الملف
    try:
        os.remove(file_path)
        flash('تم حذف النسخة الاحتياطية بنجاح', 'success')
    except Exception as e:
        flash(f'حدث خطأ أثناء حذف النسخة الاحتياطية: {str(e)}', 'danger')

    return redirect(url_for('backup'))

@app.route('/personal_data/excel/data', methods=['GET'])
@login_required
def get_personal_data_excel_data():
    """
    الحصول على بيانات الأشخاص لعرضها في DataTables
    """
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        return jsonify({'success': False, 'message': 'ليس لديك صلاحية للوصول إلى هذه الصفحة'})

    try:
        # جلب جميع البيانات الشخصية
        records = PersonalData.query.all()

        # طباعة معلومات تفصيلية عن جميع السجلات للتشخيص
        print(f"عدد السجلات في قاعدة البيانات: {len(records)}")
        for record in records:
            print(f"\n--- معلومات السجل رقم {record.id} ---")
            print(f"الاسم الشخصي: {record.full_name}")
            print(f"الاسم المستعار: {record.nickname}")
            print(f"العمر: {record.age}")
            print(f"الرقم الوطني: {record.national_number}")
            print(f"الرقم العسكري: {record.military_number}")
            print(f"العمل: {record.job_title}")
            print(f"مكان العمل: {record.work_place_text}")
            print(f"رقم الهاتف: {record.phone_yemen_mobile}")
            print(f"العزلة: {record.uzla}")

            # الحقول الترميزية
            print(f"المؤهل العلمي ID: {record.qualification_type_id}")
            if record.qualification_type:
                print(f"المؤهل العلمي اسم: {record.qualification_type.name}")
            else:
                print(f"المؤهل العلمي اسم: غير محدد")

            print(f"الحالة الاجتماعية ID: {record.marital_status_id}")
            if record.marital_status:
                print(f"الحالة الاجتماعية اسم: {record.marital_status.name}")
            else:
                print(f"الحالة الاجتماعية اسم: غير محدد")

            print(f"القرية ID: {record.village_id}")
            if record.village:
                print(f"القرية اسم: {record.village.name}")
            else:
                print(f"القرية اسم: غير محدد")

            print(f"المحافظة ID: {record.governorate_id}")
            if record.governorate:
                print(f"المحافظة اسم: {record.governorate.name}")
            else:
                print(f"المحافظة اسم: غير محدد")

            print(f"المديرية ID: {record.directorate_id}")
            if record.directorate:
                print(f"المديرية اسم: {record.directorate.name}")
            else:
                print(f"المديرية اسم: غير محدد")

            print(f"الإدارة ID: {record.agency_id}")
            if record.agency:
                print(f"الإدارة اسم: {record.agency.name}")
            else:
                print(f"الإدارة اسم: غير محدد")

        # إضافة استعلام مباشر لعرض البيانات من قاعدة البيانات
        print("\n--- استعلام مباشر من قاعدة البيانات ---")
        # استعلام عن جدول المؤهلات العلمية
        qualification_types = QualificationType.query.all()
        print(f"عدد المؤهلات العلمية: {len(qualification_types)}")
        for qt in qualification_types:
            print(f"المؤهل العلمي: ID={qt.id}, الاسم={qt.name}")

        # استعلام عن جدول الحالات الاجتماعية
        marital_statuses = MaritalStatus.query.all()
        print(f"عدد الحالات الاجتماعية: {len(marital_statuses)}")
        for ms in marital_statuses:
            print(f"الحالة الاجتماعية: ID={ms.id}, الاسم={ms.name}")

        # استعلام عن جدول القرى
        villages = Village.query.all()
        print(f"عدد القرى: {len(villages)}")
        for village in villages:
            print(f"القرية: ID={village.id}, الاسم={village.name}")

        data = []
        for record in records:
            # استخدام القيم النصية المباشرة بدلاً من الجداول المرجعية
            data.append({
                'id': record.id,
                'full_name': record.full_name,
                'nickname': record.nickname if record.nickname else '',
                'age': record.age if record.age else '',
                'national_number': record.national_number if record.national_number else '',
                'military_number': record.military_number if record.military_number else '',
                'job': record.job_title if record.job_title else '',
                'work_place': record.work_place_text if record.work_place_text else '',
                'phone': record.phone_yemen_mobile if record.phone_yemen_mobile else '',
                'work_number': record.work_number if record.work_number else '',
                'work_rank': record.work_rank if record.work_rank else '',
                # استخدام القيم النصية المباشرة
                'governorate': record.governorate if record.governorate else '',
                'directorate': record.directorate if record.directorate else '',
                'uzla': record.uzla if record.uzla else '',
                'village': record.village if record.village else '',
                'qualification': record.qualification_text if hasattr(record, 'qualification_text') and record.qualification_text else (record.qualification_type.name if record.qualification_type else ''),
                'agency': record.agency.name if record.agency else '',
                'marital_status': record.marital_status_text if hasattr(record, 'marital_status_text') and record.marital_status_text else (record.marital_status.name if record.marital_status else '')
            })

        # طباعة البيانات للتشخيص
        print("البيانات المرسلة إلى DevExtreme:")
        for item in data[:5]:  # طباعة أول 5 عناصر فقط للتشخيص
            print(f"ID: {item.get('id')}, الاسم: {item.get('full_name')}")

        # التأكد من أن البيانات تحتوي على حقل id
        if data and 'id' not in data[0]:
            print("تحذير: حقل 'id' غير موجود في البيانات!")

        return jsonify(data)
    except Exception as e:
        print(f"خطأ في جلب البيانات: {str(e)}")
        return jsonify({'success': False, 'message': str(e)})

@app.route('/personal_data/excel/<int:personal_data_id>/get', methods=['GET'])
@login_required
def get_personal_data_excel_by_id(personal_data_id):
    """
    الحصول على بيانات شخص محدد
    """
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        return jsonify({'success': False, 'message': 'ليس لديك صلاحية للوصول إلى هذه الصفحة'})

    try:
        # البحث عن البيانات الشخصية
        personal_data = PersonalData.query.get_or_404(personal_data_id)

        # تحويل البيانات إلى قاموس
        data = {
            'id': personal_data.id,
            'full_name': personal_data.full_name,
            'nickname': personal_data.nickname if personal_data.nickname else '',
            'age': personal_data.age if personal_data.age else '',
            'national_number': personal_data.national_number if personal_data.national_number else '',
            'military_number': personal_data.military_number if personal_data.military_number else '',
            'job': personal_data.job_title if personal_data.job_title else '',
            'work_place': personal_data.work_place_text if personal_data.work_place_text else '',
            'phone': personal_data.phone_yemen_mobile if personal_data.phone_yemen_mobile else '',
            'work_number': personal_data.work_number if personal_data.work_number else '',
            'work_rank': personal_data.work_rank if personal_data.work_rank else '',
            'governorate': personal_data.governorate if personal_data.governorate else '',
            'directorate': personal_data.directorate if personal_data.directorate else '',
            'village': personal_data.village if personal_data.village else '',
            'uzla': personal_data.uzla if personal_data.uzla else '',
            'qualification': personal_data.qualification_text if personal_data.qualification_text else (personal_data.qualification_type.name if personal_data.qualification_type else ''),
            'agency': personal_data.agency.name if personal_data.agency else '',
            'marital_status': personal_data.marital_status_text if personal_data.marital_status_text else (personal_data.marital_status.name if personal_data.marital_status else '')
        }

        return jsonify({'success': True, 'data': data})
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

if __name__ == '__main__':
    # إنشاء قاعدة البيانات إذا لم تكن موجودة
    with app.app_context():
        # إنشاء الجداول فقط إذا لم تكن موجودة
        db.create_all()

        # تهيئة النظام الذكي للنسخ الاحتياطي
        smart_backup_manager.init_app(app)
        print("✅ تم تفعيل النظام الذكي للنسخ الاحتياطي")

        # إضافة بيانات افتراضية للجداول الترميزية
        # الجهات
        if not Agency.query.first():
            agencies = [
                Agency(name='وزارة الدفاع', code='MOD'),
                Agency(name='وزارة الداخلية', code='MOI'),
                Agency(name='وزارة التعليم', code='MOE')
            ]
            db.session.add_all(agencies)
            db.session.commit()

        # إضافة بيانات افتراضية للأقسام إذا كانت فارغة
        if not Department.query.first():
            # جلب الجهات
            mod = Agency.query.filter_by(name='وزارة الدفاع').first()
            moi = Agency.query.filter_by(name='وزارة الداخلية').first()

            if mod and moi:
                # أقسام وزارة الدفاع
                dept1 = Department(name='قسم التدريب', code='D001', agency_id=mod.id)
                db.session.add(dept1)
                db.session.commit()

                dept2 = Department(name='قسم العمليات', code='D002', agency_id=mod.id)
                db.session.add(dept2)
                db.session.commit()

                # أقسام فرعية
                dept1_1 = Department(name='وحدة التدريب الفني', code='D001-1', parent_id=dept1.id, agency_id=mod.id)
                db.session.add(dept1_1)

                dept1_2 = Department(name='وحدة التدريب الإداري', code='D001-2', parent_id=dept1.id, agency_id=mod.id)
                db.session.add(dept1_2)

                # أقسام وزارة الداخلية
                dept3 = Department(name='قسم الأمن', code='I001', agency_id=moi.id)
                db.session.add(dept3)

                dept4 = Department(name='قسم المرور', code='I002', agency_id=moi.id)
                db.session.add(dept4)

                db.session.commit()

        # إضافة بيانات افتراضية لأنواع الإصابات إذا كانت فارغة
        if not InjuryType.query.first():
            injury_types = [
                'إصابة طفيفة',
                'إصابة متوسطة',
                'إصابة بليغة',
                'إصابة خطيرة',
                'إصابة مميتة'
            ]

            for injury_type in injury_types:
                db.session.add(InjuryType(name=injury_type))

            db.session.commit()

        # إضافة بيانات افتراضية لأسباب الإصابات إذا كانت فارغة
        if not InjuryCause.query.first():
            injury_causes = [
                'حادث مروري',
                'حادث عمل',
                'إصابة أثناء التدريب',
                'إصابة أثناء المهمة',
                'إصابة أثناء القتال',
                'أخرى'
            ]

            for injury_cause in injury_causes:
                db.session.add(InjuryCause(name=injury_cause))

            db.session.commit()

        # إضافة بيانات افتراضية لأنواع التكليف إذا كانت فارغة
        if not AssignmentType.query.first():
            assignment_types = [
                'تكليف دائم',
                'تكليف مؤقت',
                'انتداب',
                'إعارة',
                'تعيين',
                'تعاقد'
            ]

            for assignment_type in assignment_types:
                db.session.add(AssignmentType(name=assignment_type))

            db.session.commit()

        # إضافة بيانات افتراضية لجهات إصدار القرار إذا كانت فارغة
        if not IssuingAuthority.query.first():
            issuing_authorities = [
                'وزارة الدفاع',
                'وزارة الداخلية',
                'رئاسة الجمهورية',
                'مجلس الوزراء',
                'هيئة الأركان العامة',
                'قيادة المنطقة العسكرية',
                'مدير الأمن',
                'أخرى'
            ]

            for authority in issuing_authorities:
                db.session.add(IssuingAuthority(name=authority))

            db.session.commit()

        # إضافة بيانات افتراضية للرتب العسكرية إذا كانت فارغة
        if not MilitaryRank.query.first():
            military_ranks = [
                {'name': 'جندي', 'code': 'R01'},
                {'name': 'عريف', 'code': 'R02'},
                {'name': 'رقيب', 'code': 'R03'},
                {'name': 'رقيب أول', 'code': 'R04'},
                {'name': 'مساعد', 'code': 'R05'},
                {'name': 'مساعد أول', 'code': 'R06'},
                {'name': 'ملازم', 'code': 'R07'},
                {'name': 'ملازم أول', 'code': 'R08'},
                {'name': 'نقيب', 'code': 'R09'},
                {'name': 'رائد', 'code': 'R10'},
                {'name': 'مقدم', 'code': 'R11'},
                {'name': 'عقيد', 'code': 'R12'},
                {'name': 'عميد', 'code': 'R13'},
                {'name': 'لواء', 'code': 'R14'},
                {'name': 'فريق', 'code': 'R15'},
                {'name': 'فريق أول', 'code': 'R16'}
            ]

            for rank in military_ranks:
                db.session.add(MilitaryRank(name=rank['name'], code=rank['code']))

            db.session.commit()

    # إضافة مستخدم افتراضي للاختبار إذا لم يكن هناك مستخدمين
    with app.app_context():
        if not User.query.first():
            hashed_password = generate_password_hash('admin123')
            admin = User(username='admin', email='<EMAIL>', password=hashed_password, role='admin')
            db.session.add(admin)

            # إضافة مدرب افتراضي
            trainer_password = generate_password_hash('trainer123')
            trainer = User(username='مدرب تجريبي', email='<EMAIL>', password=trainer_password, role='trainer')
            db.session.add(trainer)

            db.session.commit()

        # إضافة بيانات افتراضية للجداول الترميزية إذا كانت فارغة
        # المحافظات
        if not Governorate.query.first():
            governorates = [
                Governorate(name='صنعاء', code='01'),
                Governorate(name='عدن', code='02'),
                Governorate(name='تعز', code='03'),
                Governorate(name='الحديدة', code='04'),
                Governorate(name='حضرموت', code='05')
            ]
            db.session.add_all(governorates)
            db.session.commit()

            # المديريات (لمحافظة صنعاء)
            sanaa_gov = Governorate.query.filter_by(name='صنعاء').first()
            if sanaa_gov:
                directorates = [
                    Directorate(name='السبعين', code='0101', governorate_id=sanaa_gov.id),
                    Directorate(name='معين', code='0102', governorate_id=sanaa_gov.id),
                    Directorate(name='الثورة', code='0103', governorate_id=sanaa_gov.id)
                ]
                db.session.add_all(directorates)
                db.session.commit()

        # الحالات الاجتماعية
        if not MaritalStatus.query.first():
            marital_statuses = [
                MaritalStatus(name='أعزب'),
                MaritalStatus(name='متزوج'),
                MaritalStatus(name='مطلق'),
                MaritalStatus(name='أرمل')
            ]
            db.session.add_all(marital_statuses)
            db.session.commit()

        # فصائل الدم
        if not BloodType.query.first():
            blood_types = [
                BloodType(name='A+'),
                BloodType(name='A-'),
                BloodType(name='B+'),
                BloodType(name='B-'),
                BloodType(name='AB+'),
                BloodType(name='AB-'),
                BloodType(name='O+'),
                BloodType(name='O-')
            ]
            db.session.add_all(blood_types)
            db.session.commit()

        # الجهات
        if not Agency.query.first():
            agencies = [
                Agency(name='وزارة الدفاع', code='MOD'),
                Agency(name='وزارة الداخلية', code='MOI'),
                Agency(name='وزارة التعليم', code='MOE')
            ]
            db.session.add_all(agencies)
            db.session.commit()

        # أنواع المراكز التدريبية
        if not TrainingCenterType.query.first():
            center_types = [
                TrainingCenterType(name='مركز تدريب عسكري', description='مركز مخصص للتدريب العسكري'),
                TrainingCenterType(name='مركز تدريب أمني', description='مركز مخصص للتدريب الأمني'),
                TrainingCenterType(name='مركز تدريب إداري', description='مركز مخصص للتدريب الإداري'),
                TrainingCenterType(name='مركز تدريب فني', description='مركز مخصص للتدريب الفني')
            ]
            db.session.add_all(center_types)
            db.session.commit()

        # المواقع
        if not Location.query.first():
            locations = [
                Location(name='صنعاء - المركز الرئيسي', address='صنعاء - شارع الستين'),
                Location(name='عدن - المركز الرئيسي', address='عدن - المنصورة'),
                Location(name='تعز - المركز الرئيسي', address='تعز - شارع جمال')
            ]
            db.session.add_all(locations)
            db.session.commit()

        # المراكز التدريبية
        if not TrainingCenter.query.first():
            centers = [
                TrainingCenter(name='مركز التدريب الأول', center_type_id=1, location_id=1, capacity=100, is_ready=True),
                TrainingCenter(name='مركز التدريب الثاني', center_type_id=2, location_id=2, capacity=80, is_ready=True),
                TrainingCenter(name='مركز التدريب الثالث', center_type_id=3, location_id=3, capacity=50, is_ready=False, not_ready_reason='قيد الصيانة')
            ]
            db.session.add_all(centers)
            db.session.commit()

        # أنواع المشاركين
        if not ParticipantType.query.first():
            participant_types = [
                ParticipantType(name='ضباط'),
                ParticipantType(name='صف ضباط'),
                ParticipantType(name='جنود'),
                ParticipantType(name='مدنيين')
            ]
            db.session.add_all(participant_types)
            db.session.commit()

        # أنواع البطاقات
        if not CardType.query.first():
            card_types = [
                CardType(name='بطاقة عسكرية'),
                CardType(name='بطاقة شخصية'),
                CardType(name='جواز سفر')
            ]
            db.session.add_all(card_types)
            db.session.commit()

        # مستويات الدورات
        if not CourseLevel.query.first():
            course_levels = [
                CourseLevel(name='مبتدئ'),
                CourseLevel(name='متوسط'),
                CourseLevel(name='متقدم')
            ]
            db.session.add_all(course_levels)
            db.session.commit()

        # تصنيفات الدورات
        if not CourseCategory.query.first():
            course_categories = [
                CourseCategory(name='البرمجة', code='programming'),
                CourseCategory(name='التصميم', code='design'),
                CourseCategory(name='الإدارة', code='management'),
                CourseCategory(name='التسويق', code='marketing'),
                CourseCategory(name='الذكاء الاصطناعي', code='ai'),
                CourseCategory(name='قواعد البيانات', code='database'),
                CourseCategory(name='أخرى', code='other')
            ]
            db.session.add_all(course_categories)
            db.session.commit()

        # إضافة بيانات افتراضية لأنواع المؤهلات إذا كانت فارغة
        if not QualificationType.query.first():
            qualification_types = [
                QualificationType(name='ابتدائي', description='شهادة المرحلة الابتدائية', level='primary', code='Q01'),
                QualificationType(name='إعدادي', description='شهادة المرحلة الإعدادية', level='middle', code='Q02'),
                QualificationType(name='ثانوي', description='شهادة المرحلة الثانوية', level='secondary', code='Q03'),
                QualificationType(name='دبلوم', description='شهادة الدبلوم', level='diploma', code='Q04'),
                QualificationType(name='بكالوريوس', description='شهادة البكالوريوس', level='bachelor', code='Q05'),
                QualificationType(name='ماجستير', description='شهادة الماجستير', level='master', code='Q06'),
                QualificationType(name='دكتوراه', description='شهادة الدكتوراه', level='phd', code='Q07')
            ]
            db.session.add_all(qualification_types)
            db.session.commit()

        # إضافة بيانات افتراضية للتخصصات إذا كانت فارغة
        if not Specialization.query.first():
            specializations = [
                Specialization(name='طب عام', description='تخصص في الطب العام', field='medical', code='S01'),
                Specialization(name='جراحة', description='تخصص في الجراحة', field='medical', code='S02'),
                Specialization(name='هندسة مدنية', description='تخصص في الهندسة المدنية', field='engineering', code='S03'),
                Specialization(name='هندسة كهربائية', description='تخصص في الهندسة الكهربائية', field='engineering', code='S04'),
                Specialization(name='إدارة أعمال', description='تخصص في إدارة الأعمال', field='administrative', code='S05'),
                Specialization(name='محاسبة', description='تخصص في المحاسبة', field='administrative', code='S06'),
                Specialization(name='علوم حاسوب', description='تخصص في علوم الحاسوب', field='technical', code='S07'),
                Specialization(name='أمن معلومات', description='تخصص في أمن المعلومات', field='security', code='S08'),
                Specialization(name='علوم عسكرية', description='تخصص في العلوم العسكرية', field='military', code='S09')
            ]
            db.session.add_all(specializations)
            db.session.commit()

        # طرق الدفع
        if not PaymentMethod.query.first():
            payment_methods = [
                PaymentMethod(name='نقداً'),
                PaymentMethod(name='تحويل بنكي'),
                PaymentMethod(name='شيك')
            ]
            db.session.add_all(payment_methods)
            db.session.commit()

        # مسارات التدريب
        if not CoursePath.query.first():
            course_paths = [
                CoursePath(name='مسار التدريب العسكري', description='مسار خاص بالتدريب العسكري', code='CP01'),
                CoursePath(name='مسار التدريب الأمني', description='مسار خاص بالتدريب الأمني', code='CP02'),
                CoursePath(name='مسار التدريب الإداري', description='مسار خاص بالتدريب الإداري', code='CP03'),
                CoursePath(name='مسار التدريب الفني', description='مسار خاص بالتدريب الفني', code='CP04')
            ]
            db.session.add_all(course_paths)
            db.session.commit()

            # مستويات المسارات
            military_path = CoursePath.query.filter_by(name='مسار التدريب العسكري').first()
            if military_path:
                path_levels = [
                    CoursePathLevel(name='المستوى الأساسي', description='المستوى الأساسي للتدريب العسكري', code='CPL01', order=1, path_id=military_path.id),
                    CoursePathLevel(name='المستوى المتوسط', description='المستوى المتوسط للتدريب العسكري', code='CPL02', order=2, path_id=military_path.id),
                    CoursePathLevel(name='المستوى المتقدم', description='المستوى المتقدم للتدريب العسكري', code='CPL03', order=3, path_id=military_path.id)
                ]
                db.session.add_all(path_levels)
                db.session.commit()

        # تصنيفات القوة
        if not ForceClassification.query.first():
            force_classifications = [
                ForceClassification(name='قوات برية', description='تصنيف للقوات البرية'),
                ForceClassification(name='قوات بحرية', description='تصنيف للقوات البحرية'),
                ForceClassification(name='قوات جوية', description='تصنيف للقوات الجوية'),
                ForceClassification(name='قوات خاصة', description='تصنيف للقوات الخاصة'),
                ForceClassification(name='قوات أمنية', description='تصنيف للقوات الأمنية')
            ]
            db.session.add_all(force_classifications)
            db.session.commit()

    # إنشاء جدول البيانات الشخصية المبسطة
    with app.app_context():
        db.create_all()


# مسارات API للبيانات الشخصية المبسطة
@app.route('/simple', methods=['GET'])
@login_required
def simple_personal_data_index():
    """
    الصفحة الرئيسية للبيانات الشخصية المبسطة
    """
    print("تم استدعاء دالة simple_personal_data_index")

    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    print("المستخدم هو مدير، جاري عرض الصفحة")
    return render_template('simple_data/index.html', title='البيانات الشخصية المبسطة')

@app.route('/simple/data', methods=['GET'])
@login_required
def get_simple_personal_data():
    """
    الحصول على بيانات الأشخاص المبسطة لعرضها في DevExtreme
    """
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        return jsonify({'success': False, 'message': 'ليس لديك صلاحية للوصول إلى هذه الصفحة'})

    try:
        # جلب جميع البيانات الشخصية المبسطة
        records = SimplePersonalData.query.all()

        # طباعة معلومات تفصيلية عن السجلات للتشخيص
        print(f"عدد السجلات في قاعدة البيانات: {len(records)}")

        data = []
        for record in records:
            data.append({
                'id': record.id,
                'full_name': record.full_name,
                'nickname': record.nickname if record.nickname else '',
                'age': record.age if record.age else '',
                'governorate': record.governorate if record.governorate else '',
                'directorate': record.directorate if record.directorate else '',
                'uzla': record.uzla if record.uzla else '',
                'village': record.village if record.village else '',
                'qualification': record.qualification if record.qualification else '',
                'marital_status': record.marital_status if record.marital_status else '',
                'job': record.job if record.job else '',
                'agency': record.agency if record.agency else '',
                'work_place': record.work_place if record.work_place else '',
                'national_number': record.national_number if record.national_number else '',
                'military_number': record.military_number if record.military_number else '',
                'phone': record.phone if record.phone else ''
            })

        # طباعة البيانات للتشخيص
        print("البيانات المرسلة إلى DevExtreme:")
        for item in data[:5]:  # طباعة أول 5 عناصر فقط للتشخيص
            print(f"ID: {item.get('id')}, الاسم: {item.get('full_name')}")

        return jsonify(data)
    except Exception as e:
        print(f"خطأ في جلب البيانات: {str(e)}")
        return jsonify({'success': False, 'message': str(e)})

@app.route('/simple/data/<int:data_id>', methods=['GET'])
@login_required
def get_simple_personal_data_by_id(data_id):
    """
    الحصول على بيانات شخص محدد من الجدول المبسط
    """
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        return jsonify({'success': False, 'message': 'ليس لديك صلاحية للوصول إلى هذه الصفحة'})

    try:
        # البحث عن البيانات الشخصية
        personal_data = SimplePersonalData.query.get_or_404(data_id)

        # تحويل البيانات إلى قاموس
        data = {
            'id': personal_data.id,
            'full_name': personal_data.full_name,
            'nickname': personal_data.nickname if personal_data.nickname else '',
            'age': personal_data.age if personal_data.age else '',
            'governorate': personal_data.governorate if personal_data.governorate else '',
            'directorate': personal_data.directorate if personal_data.directorate else '',
            'uzla': personal_data.uzla if personal_data.uzla else '',
            'village': personal_data.village if personal_data.village else '',
            'qualification': personal_data.qualification if personal_data.qualification else '',
            'marital_status': personal_data.marital_status if personal_data.marital_status else '',
            'job': personal_data.job if personal_data.job else '',
            'agency': personal_data.agency if personal_data.agency else '',
            'work_place': personal_data.work_place if personal_data.work_place else '',
            'national_number': personal_data.national_number if personal_data.national_number else '',
            'military_number': personal_data.military_number if personal_data.military_number else '',
            'phone': personal_data.phone if personal_data.phone else ''
        }

        return jsonify({'success': True, 'data': data})
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/simple/data/add', methods=['POST'])
@login_required
# @csrf.exempt
def add_simple_personal_data():
    """
    إضافة بيانات شخصية جديدة إلى الجدول المبسط
    """
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        return jsonify({'success': False, 'message': 'ليس لديك صلاحية للوصول إلى هذه الصفحة'})

    try:
        # الحصول على البيانات من الطلب
        data = request.json

        # طباعة البيانات المستلمة للتشخيص
        print("البيانات المستلمة للإضافة:", data)

        # التحقق من وجود البيانات المطلوبة
        if not data:
            return jsonify({'success': False, 'message': 'لم يتم استلام أي بيانات'})

        if not data.get('full_name'):
            return jsonify({'success': False, 'message': 'الاسم الشخصي مطلوب'})

        # معالجة القيم العددية
        age = data.get('age')
        if age and age != '':
            try:
                age = int(age)
            except:
                age = None
        else:
            age = None

        # إنشاء سجل جديد
        personal_data = SimplePersonalData(
            user_id=current_user.id,
            full_name=data.get('full_name'),
            nickname=data.get('nickname') if data.get('nickname') else None,
            age=age,
            governorate=data.get('governorate') if data.get('governorate') else None,
            directorate=data.get('directorate') if data.get('directorate') else None,
            uzla=data.get('uzla') if data.get('uzla') else None,
            village=data.get('village') if data.get('village') else None,
            qualification=data.get('qualification') if data.get('qualification') else None,
            marital_status=data.get('marital_status') if data.get('marital_status') else None,
            job=data.get('job') if data.get('job') else None,
            agency=data.get('agency') if data.get('agency') else None,
            work_place=data.get('work_place') if data.get('work_place') else None,
            national_number=data.get('national_number') if data.get('national_number') else None,
            military_number=data.get('military_number') if data.get('military_number') else None,
            phone=data.get('phone') if data.get('phone') else None
        )

        db.session.add(personal_data)
        db.session.commit()

        return jsonify({'success': True, 'id': personal_data.id})
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': str(e)})

@app.route('/simple/data/<int:data_id>/update', methods=['POST'])
@login_required
# @csrf.exempt
def update_simple_personal_data(data_id):
    """
    تحديث بيانات شخصية في الجدول المبسط
    """
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        return jsonify({'success': False, 'message': 'ليس لديك صلاحية للوصول إلى هذه الصفحة'})

    try:
        # البحث عن البيانات الشخصية
        personal_data = SimplePersonalData.query.get_or_404(data_id)

        # الحصول على البيانات من الطلب
        data = request.json

        # طباعة البيانات المستلمة للتشخيص
        print("البيانات المستلمة للتحديث:", data)

        # التحقق من وجود البيانات المطلوبة
        if not data:
            return jsonify({'success': False, 'message': 'لم يتم استلام أي بيانات'})

        if not data.get('full_name'):
            return jsonify({'success': False, 'message': 'الاسم الشخصي مطلوب'})

        # معالجة القيم العددية
        age = data.get('age')
        if age and age != '':
            try:
                age = int(age)
            except:
                age = None
        else:
            age = None

        # تحديث البيانات
        personal_data.full_name = data.get('full_name')
        personal_data.nickname = data.get('nickname') if data.get('nickname') else None
        personal_data.age = age
        personal_data.governorate = data.get('governorate') if data.get('governorate') else None
        personal_data.directorate = data.get('directorate') if data.get('directorate') else None
        personal_data.uzla = data.get('uzla') if data.get('uzla') else None
        personal_data.village = data.get('village') if data.get('village') else None
        personal_data.qualification = data.get('qualification') if data.get('qualification') else None
        personal_data.marital_status = data.get('marital_status') if data.get('marital_status') else None
        personal_data.job = data.get('job') if data.get('job') else None
        personal_data.agency = data.get('agency') if data.get('agency') else None
        personal_data.work_place = data.get('work_place') if data.get('work_place') else None
        personal_data.national_number = data.get('national_number') if data.get('national_number') else None
        personal_data.military_number = data.get('military_number') if data.get('military_number') else None
        personal_data.phone = data.get('phone') if data.get('phone') else None

        db.session.commit()

        return jsonify({'success': True})
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': str(e)})

@app.route('/simple/data/<int:data_id>/delete', methods=['POST'])
@login_required
# @csrf.exempt
def delete_simple_personal_data(data_id):
    """
    حذف بيانات شخصية من الجدول المبسط
    """
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        return jsonify({'success': False, 'message': 'ليس لديك صلاحية للوصول إلى هذه الصفحة'})

    try:
        # البحث عن البيانات الشخصية
        personal_data = SimplePersonalData.query.get_or_404(data_id)

        # حذف البيانات
        db.session.delete(personal_data)
        db.session.commit()

        return jsonify({'success': True})
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': str(e)})

@app.route('/simple/data/export', methods=['GET'])
@login_required
def export_simple_personal_data():
    """
    تصدير البيانات الشخصية المبسطة إلى ملف إكسل
    """
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    try:
        # جلب البيانات الشخصية المبسطة
        records = SimplePersonalData.query.all()
        print(f"عدد السجلات المراد تصديرها: {len(records)}")

        # إنشاء DataFrame
        data = []
        for record in records:
            person_data = {
                'الاسم الشخصي': record.full_name,
                'الاسم المستعار': record.nickname if record.nickname else '',
                'العمر': record.age if record.age else '',
                'المحافظة': record.governorate if record.governorate else '',
                'المديرية': record.directorate if record.directorate else '',
                'العزلة': record.uzla if record.uzla else '',
                'الحي/القرية': record.village if record.village else '',
                'المؤهل العلمي': record.qualification if record.qualification else '',
                'الحالة الاجتماعية': record.marital_status if record.marital_status else '',
                'العمل': record.job if record.job else '',
                'الإدارة': record.agency if record.agency else '',
                'مكان العمل': record.work_place if record.work_place else '',
                'الرقم الوطني': record.national_number if record.national_number else '',
                'الرقم العسكري': record.military_number if record.military_number else '',
                'رقم التلفون': record.phone if record.phone else ''
            }
            data.append(person_data)

        df = pd.DataFrame(data)
        print(f"تم إنشاء DataFrame بنجاح. عدد الصفوف: {len(df)}")

        # معالجة القيم NaN و INF
        df = df.fillna('')  # استبدال القيم NaN بسلاسل فارغة

        # إنشاء ملف إكسل في الذاكرة
        output = io.BytesIO()
        writer = pd.ExcelWriter(output, engine='xlsxwriter')

        # تعيين اتجاه الورقة من اليمين إلى اليسار
        workbook = writer.book

        # إنشاء ورقة البيانات
        df.to_excel(writer, sheet_name='البيانات الشخصية', index=False)

        # الحصول على ورقة العمل
        worksheet = writer.sheets['البيانات الشخصية']

        # تنسيق الورقة
        # إنشاء تنسيق للعناوين
        header_format = workbook.add_format({
            'bold': True,
            'text_wrap': True,
            'valign': 'top',
            'fg_color': '#D7E4BC',
            'border': 1,
            'align': 'center'
        })

        # إنشاء تنسيق للبيانات
        data_format = workbook.add_format({
            'border': 1,
            'align': 'right'
        })

        # تطبيق التنسيق على العناوين
        for col_num, value in enumerate(df.columns.values):
            worksheet.write(0, col_num, value, header_format)
            worksheet.set_column(col_num, col_num, 20)  # تعيين عرض العمود

        # تطبيق التنسيق على البيانات
        for row_num in range(1, len(df) + 1):
            for col_num in range(len(df.columns)):
                worksheet.write(row_num, col_num, df.iloc[row_num-1, col_num], data_format)

        # إعادة المؤشر إلى بداية الملف
        writer.close()
        output.seek(0)

        # إنشاء اسم الملف
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f'simple_personal_data_{timestamp}.xlsx'

        # إرسال الملف كاستجابة
        return send_file(
            output,
            as_attachment=True,
            download_name=filename,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
    except Exception as e:
        flash(f'حدث خطأ أثناء تصدير البيانات: {str(e)}', 'danger')
        return redirect(url_for('dashboard'))

@app.route('/simple/data/template', methods=['GET'])
@login_required
def download_simple_personal_data_template():
    """
    تنزيل قالب البيانات الشخصية المبسطة
    """
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    try:
        # إنشاء DataFrame فارغ بالأعمدة المطلوبة
        columns = [
            'الاسم الشخصي',
            'الاسم المستعار',
            'العمر',
            'المحافظة',
            'المديرية',
            'العزلة',
            'الحي/القرية',
            'المؤهل العلمي',
            'الحالة الاجتماعية',
            'العمل',
            'الإدارة',
            'مكان العمل',
            'الرقم الوطني',
            'الرقم العسكري',
            'رقم التلفون'
        ]

        # إنشاء DataFrame فارغ مع صف واحد كمثال
        df = pd.DataFrame(columns=columns)
        df.loc[0] = ['اسم شخص', 'اسم مستعار', 30, 'صنعاء', 'السبعين', 'عزلة', 'حي/قرية', 'بكالوريوس', 'متزوج', 'مهندس', 'وزارة الدفاع', 'صنعاء', '123456789', '987654321', '777123456']

        # إنشاء ملف إكسل في الذاكرة
        output = io.BytesIO()
        writer = pd.ExcelWriter(output, engine='xlsxwriter')

        # تعيين اتجاه الورقة من اليمين إلى اليسار
        workbook = writer.book

        # إنشاء ورقة البيانات
        df.to_excel(writer, sheet_name='البيانات الشخصية', index=False)

        # الحصول على ورقة العمل
        worksheet = writer.sheets['البيانات الشخصية']

        # تنسيق الورقة
        # إنشاء تنسيق للعناوين
        header_format = workbook.add_format({
            'bold': True,
            'text_wrap': True,
            'valign': 'top',
            'fg_color': '#D7E4BC',
            'border': 1,
            'align': 'center'
        })

        # إنشاء تنسيق للبيانات
        data_format = workbook.add_format({
            'border': 1,
            'align': 'right'
        })

        # تطبيق التنسيق على العناوين
        for col_num, value in enumerate(df.columns.values):
            worksheet.write(0, col_num, value, header_format)
            worksheet.set_column(col_num, col_num, 20)  # تعيين عرض العمود

        # تطبيق التنسيق على البيانات
        for row_num in range(1, len(df) + 1):
            for col_num in range(len(df.columns)):
                worksheet.write(row_num, col_num, df.iloc[row_num-1, col_num], data_format)

        # إعادة المؤشر إلى بداية الملف
        writer.close()
        output.seek(0)

        # إرسال الملف كاستجابة
        return send_file(
            output,
            as_attachment=True,
            download_name='simple_personal_data_template.xlsx',
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
    except Exception as e:
        flash(f'حدث خطأ أثناء إنشاء قالب البيانات: {str(e)}', 'danger')
        return redirect(url_for('dashboard'))



@app.route('/simple/data/import', methods=['POST'])
@login_required
def import_simple_personal_data():
    """
    استيراد البيانات الشخصية المبسطة من ملف إكسل
    """
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # التحقق من وجود ملف
    if 'excel_file' not in request.files:
        flash('لم يتم تحديد ملف', 'danger')
        return redirect(url_for('dashboard'))

    file = request.files['excel_file']

    # التحقق من أن الملف له اسم
    if file.filename == '':
        flash('لم يتم تحديد ملف', 'danger')
        return redirect(url_for('dashboard'))

    # التحقق من أن الملف بصيغة إكسل
    if not file.filename.endswith(('.xlsx', '.xls')):
        flash('يجب أن يكون الملف بصيغة Excel (.xlsx أو .xls)', 'danger')
        return redirect(url_for('dashboard'))

    try:
        # قراءة ملف الإكسل
        sheet_name = request.form.get('sheet_name', None)

        try:
            # محاولة قراءة الورقة بالاسم المحدد
            if sheet_name and sheet_name.strip():
                # التحقق من وجود الورقة بالاسم المحدد
                xls = pd.ExcelFile(file)
                if sheet_name not in xls.sheet_names:
                    flash(f'لم يتم العثور على ورقة باسم "{sheet_name}" في الملف. الأوراق المتاحة: {", ".join(xls.sheet_names)}', 'danger')
                    return redirect(url_for('dashboard'))
                df = pd.read_excel(file, sheet_name=sheet_name)
            else:
                # استخدام الورقة الأولى إذا لم يتم تحديد اسم الورقة
                df = pd.read_excel(file, sheet_name=0)
        except Exception as e:
            flash(f'خطأ في قراءة ملف الإكسل: {str(e)}', 'danger')
            return redirect(url_for('dashboard'))

        # التحقق من وجود الأعمدة المطلوبة
        required_columns = ['الاسم الشخصي']

        # تحويل أسماء الأعمدة إلى قائمة
        file_columns = df.columns.tolist()

        # طباعة أسماء الأعمدة للتشخيص
        print("أعمدة الملف:", file_columns)

        # محاولة مطابقة الأعمدة المطلوبة مع الأعمدة الموجودة
        column_mapping = {}
        for req_col in required_columns:
            # البحث عن تطابق دقيق
            if req_col in file_columns:
                column_mapping[req_col] = req_col
                print(f"تم العثور على العمود: {req_col}")
            else:
                # البحث عن تطابق جزئي
                for file_col in file_columns:
                    if isinstance(file_col, str) and (req_col in file_col or file_col in req_col):
                        column_mapping[req_col] = file_col
                        print(f"تم العثور على تطابق جزئي: {req_col} -> {file_col}")
                        break
                    # محاولة البحث عن كلمات مشابهة
                    elif isinstance(file_col, str):
                        if 'اسم' in file_col and req_col == 'الاسم الشخصي':
                            column_mapping[req_col] = file_col
                            print(f"تم العثور على تطابق بالكلمات المفتاحية: {req_col} -> {file_col}")
                            break

        # التحقق من وجود جميع الأعمدة المطلوبة
        missing_columns = [col for col in required_columns if col not in column_mapping]

        if missing_columns:
            flash(f'الأعمدة التالية مفقودة في ملف الإكسل: {", ".join(missing_columns)}', 'danger')
            return redirect(url_for('dashboard'))

        # إضافة البيانات إلى قاعدة البيانات
        success_count = 0
        error_count = 0
        existing_records = []  # قائمة للسجلات الموجودة مسبقاً
        new_records = []  # قائمة للسجلات الجديدة

        for _, row in df.iterrows():
            try:
                # استخدام التعيين الجديد للأعمدة
                name_col = column_mapping.get('الاسم الشخصي')

                if not name_col:
                    error_count += 1
                    continue

                # التحقق من أن القيم غير فارغة
                if pd.isna(row[name_col]):
                    error_count += 1
                    print(f"قيم فارغة: {row[name_col] if not pd.isna(row[name_col]) else 'فارغ'}")
                    continue

                # إنشاء سجل جديد
                personal_data = SimplePersonalData(
                    user_id=current_user.id,
                    full_name=row[name_col],
                    nickname=row.get('الاسم المستعار', '') if not pd.isna(row.get('الاسم المستعار', '')) else None,
                    age=int(row.get('العمر', 0)) if not pd.isna(row.get('العمر', 0)) else None,
                    governorate=row.get('المحافظة', '') if not pd.isna(row.get('المحافظة', '')) else None,
                    directorate=row.get('المديرية', '') if not pd.isna(row.get('المديرية', '')) else None,
                    uzla=row.get('العزلة', '') if not pd.isna(row.get('العزلة', '')) else None,
                    village=row.get('الحي/القرية', '') if not pd.isna(row.get('الحي/القرية', '')) else None,
                    qualification=row.get('المؤهل العلمي', '') if not pd.isna(row.get('المؤهل العلمي', '')) else None,
                    marital_status=row.get('الحالة الاجتماعية', '') if not pd.isna(row.get('الحالة الاجتماعية', '')) else None,
                    job=row.get('العمل', '') if not pd.isna(row.get('العمل', '')) else None,
                    agency=row.get('الإدارة', '') if not pd.isna(row.get('الإدارة', '')) else None,
                    work_place=row.get('مكان العمل', '') if not pd.isna(row.get('مكان العمل', '')) else None,
                    national_number=str(row.get('الرقم الوطني', '')) if not pd.isna(row.get('الرقم الوطني', '')) else None,
                    military_number=str(row.get('الرقم العسكري', '')) if not pd.isna(row.get('الرقم العسكري', '')) else None,
                    phone=str(row.get('رقم التلفون', '')) if not pd.isna(row.get('رقم التلفون', '')) else None
                )

                db.session.add(personal_data)
                success_count += 1
                new_records.append({
                    'full_name': row[name_col]
                })
            except Exception as e:
                error_count += 1
                print(f"خطأ في إضافة السجل: {str(e)}")

        db.session.commit()

        flash(f'تم استيراد {success_count} سجل بنجاح من أصل {len(df)} سجل.', 'success')
        return redirect(url_for('dashboard'))
    except Exception as e:
        flash(f'حدث خطأ أثناء استيراد البيانات: {str(e)}', 'danger')
        return redirect(url_for('dashboard'))

# مسار اختبار بسيط
@app.route('/test', methods=['GET'])
def test_route():
    return "مسار الاختبار يعمل بشكل صحيح!"

# مسار اختبار آخر
@app.route('/test_template', methods=['GET'])
def test_template():
    return render_template('simple_test.html')

# مسار بيانات الأفراد
@app.route('/person_data', methods=['GET'])
@login_required
def person_data_index():
    """
    صفحة إدارة بيانات الأفراد
    """
    print("تم استدعاء دالة person_data_index")

    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    return render_template('person_data.html', title='بيانات الأفراد')

# مسار للحصول على بيانات الأفراد بتنسيق JSON
@app.route('/person_data/data', methods=['GET'])
@login_required
def person_data_data():
    """
    الحصول على بيانات الأفراد بتنسيق JSON
    """
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        return jsonify({'success': False, 'message': 'ليس لديك صلاحية للوصول إلى هذه البيانات'})

    try:
        # جلب بيانات الأفراد
        records = PersonData.query.all()

        # تحويل البيانات إلى قائمة من القواميس
        data = []
        for record in records:
            person_data = {
                'id': record.id,
                'full_name': record.full_name,
                'nickname': record.nickname if record.nickname else '',
                'age': record.age if record.age else '',
                'governorate': record.governorate if record.governorate else '',
                'directorate': record.directorate if record.directorate else '',
                'uzla': record.uzla if record.uzla else '',
                'village': record.village if record.village else '',
                'qualification': record.qualification if record.qualification else '',
                'marital_status': record.marital_status if record.marital_status else '',
                'job': record.job if record.job else '',
                'agency': record.agency if record.agency else '',
                'work_place': record.work_place if record.work_place else '',
                'national_number': record.national_number if record.national_number else '',
                'military_number': record.military_number if record.military_number else '',
                'phone': record.phone if record.phone else ''
            }
            data.append(person_data)

        return jsonify(data)
    except Exception as e:
        print(f"حدث خطأ أثناء جلب البيانات: {str(e)}")
        return jsonify({'success': False, 'message': str(e)})

# مسار لإضافة بيانات فرد جديد
@app.route('/person_data/add', methods=['POST'])
@login_required
# @csrf.exempt
def person_data_add():
    """
    إضافة بيانات فرد جديد
    """
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        return jsonify({'success': False, 'message': 'ليس لديك صلاحية للوصول إلى هذه الصفحة'})

    try:
        # الحصول على البيانات من الطلب
        data = request.json

        # التحقق من وجود البيانات المطلوبة
        if not data:
            return jsonify({'success': False, 'message': 'لم يتم استلام أي بيانات'})

        if not data.get('full_name'):
            return jsonify({'success': False, 'message': 'الاسم الشخصي مطلوب'})

        # معالجة القيم العددية
        age = data.get('age')
        if age and age != '':
            try:
                age = int(age)
            except:
                age = None
        else:
            age = None

        # إنشاء سجل جديد
        person_data = PersonData(
            full_name=data.get('full_name'),
            nickname=data.get('nickname') if data.get('nickname') else None,
            age=age,
            governorate=data.get('governorate') if data.get('governorate') else None,
            directorate=data.get('directorate') if data.get('directorate') else None,
            uzla=data.get('uzla') if data.get('uzla') else None,
            village=data.get('village') if data.get('village') else None,
            qualification=data.get('qualification') if data.get('qualification') else None,
            marital_status=data.get('marital_status') if data.get('marital_status') else None,
            job=data.get('job') if data.get('job') else None,
            agency=data.get('agency') if data.get('agency') else None,
            work_place=data.get('work_place') if data.get('work_place') else None,
            national_number=data.get('national_number') if data.get('national_number') else None,
            military_number=data.get('military_number') if data.get('military_number') else None,
            phone=data.get('phone') if data.get('phone') else None
        )

        db.session.add(person_data)
        db.session.commit()

        return jsonify({'success': True, 'id': person_data.id})
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': str(e)})

# مسار لتحديث بيانات فرد
@app.route('/person_data/update/<int:data_id>', methods=['POST'])
@login_required
# @csrf.exempt
def person_data_update(data_id):
    """
    تحديث بيانات فرد
    """
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        return jsonify({'success': False, 'message': 'ليس لديك صلاحية للوصول إلى هذه الصفحة'})

    try:
        # البحث عن بيانات الفرد
        person_data = PersonData.query.get_or_404(data_id)

        # الحصول على البيانات من الطلب
        data = request.json

        # التحقق من وجود البيانات المطلوبة
        if not data:
            return jsonify({'success': False, 'message': 'لم يتم استلام أي بيانات'})

        # معالجة القيم العددية
        age = data.get('age')
        if age and age != '':
            try:
                age = int(age)
            except:
                age = None
        else:
            age = None

        # تحديث البيانات
        if 'full_name' in data:
            person_data.full_name = data.get('full_name')
        if 'nickname' in data:
            person_data.nickname = data.get('nickname') if data.get('nickname') else None
        if 'age' in data:
            person_data.age = age
        if 'governorate' in data:
            person_data.governorate = data.get('governorate') if data.get('governorate') else None
        if 'directorate' in data:
            person_data.directorate = data.get('directorate') if data.get('directorate') else None
        if 'uzla' in data:
            person_data.uzla = data.get('uzla') if data.get('uzla') else None
        if 'village' in data:
            person_data.village = data.get('village') if data.get('village') else None
        if 'qualification' in data:
            person_data.qualification = data.get('qualification') if data.get('qualification') else None
        if 'marital_status' in data:
            person_data.marital_status = data.get('marital_status') if data.get('marital_status') else None
        if 'job' in data:
            person_data.job = data.get('job') if data.get('job') else None
        if 'agency' in data:
            person_data.agency = data.get('agency') if data.get('agency') else None
        if 'work_place' in data:
            person_data.work_place = data.get('work_place') if data.get('work_place') else None
        if 'national_number' in data:
            person_data.national_number = data.get('national_number') if data.get('national_number') else None
        if 'military_number' in data:
            person_data.military_number = data.get('military_number') if data.get('military_number') else None
        if 'phone' in data:
            person_data.phone = data.get('phone') if data.get('phone') else None

        db.session.commit()

        return jsonify({'success': True})
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': str(e)})

# مسار لحذف بيانات فرد
@app.route('/person_data/delete/<int:data_id>', methods=['POST'])
@login_required
# @csrf.exempt
def person_data_delete(data_id):
    """
    حذف بيانات فرد
    """
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        return jsonify({'success': False, 'message': 'ليس لديك صلاحية للوصول إلى هذه الصفحة'})

    try:
        # البحث عن بيانات الفرد
        person_data = PersonData.query.get_or_404(data_id)

        # حذف البيانات
        db.session.delete(person_data)
        db.session.commit()

        return jsonify({'success': True})
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': str(e)})

# مسار لتصدير بيانات الأفراد إلى ملف إكسل
@app.route('/person_data/export', methods=['GET'])
@login_required
def person_data_export():
    """
    تصدير بيانات الأفراد إلى ملف إكسل
    """
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    try:
        # جلب بيانات الأفراد
        records = PersonData.query.all()
        print(f"عدد السجلات المراد تصديرها: {len(records)}")

        # إنشاء DataFrame
        data = []
        for record in records:
            person_data = {
                'الاسم الشخصي': record.full_name,
                'الاسم المستعار': record.nickname if record.nickname else '',
                'العمر': record.age if record.age else '',
                'المحافظة': record.governorate if record.governorate else '',
                'المديرية': record.directorate if record.directorate else '',
                'العزلة': record.uzla if record.uzla else '',
                'الحي/القرية': record.village if record.village else '',
                'المؤهل العلمي': record.qualification if record.qualification else '',
                'الحالة الاجتماعية': record.marital_status if record.marital_status else '',
                'العمل': record.job if record.job else '',
                'الإدارة': record.agency if record.agency else '',
                'مكان العمل': record.work_place if record.work_place else '',
                'الرقم الوطني': record.national_number if record.national_number else '',
                'الرقم العسكري': record.military_number if record.military_number else '',
                'رقم التلفون': record.phone if record.phone else ''
            }
            data.append(person_data)

        df = pd.DataFrame(data)
        print(f"تم إنشاء DataFrame بنجاح. عدد الصفوف: {len(df)}")

        # معالجة القيم NaN و INF
        df = df.fillna('')  # استبدال القيم NaN بسلاسل فارغة

        # إنشاء ملف إكسل في الذاكرة
        output = io.BytesIO()
        writer = pd.ExcelWriter(output, engine='xlsxwriter')

        # تعيين اتجاه الورقة من اليمين إلى اليسار
        workbook = writer.book

        # إنشاء ورقة البيانات
        df.to_excel(writer, sheet_name='بيانات الأفراد', index=False)

        # الحصول على ورقة العمل
        worksheet = writer.sheets['بيانات الأفراد']

        # تنسيق الورقة
        # إنشاء تنسيق للعناوين
        header_format = workbook.add_format({
            'bold': True,
            'text_wrap': True,
            'valign': 'top',
            'fg_color': '#D7E4BC',
            'border': 1,
            'align': 'center'
        })

        # إنشاء تنسيق للبيانات
        data_format = workbook.add_format({
            'border': 1,
            'align': 'right'
        })

        # تطبيق التنسيق على العناوين
        for col_num, value in enumerate(df.columns.values):
            worksheet.write(0, col_num, value, header_format)
            worksheet.set_column(col_num, col_num, 20)  # تعيين عرض العمود

        # تطبيق التنسيق على البيانات
        for row_num in range(1, len(df) + 1):
            for col_num in range(len(df.columns)):
                worksheet.write(row_num, col_num, df.iloc[row_num-1, col_num], data_format)

        # إعادة المؤشر إلى بداية الملف
        writer.close()
        output.seek(0)

        # إنشاء اسم الملف
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f'person_data_{timestamp}.xlsx'

        # إرسال الملف كاستجابة
        return send_file(
            output,
            as_attachment=True,
            download_name=filename,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
    except Exception as e:
        flash(f'حدث خطأ أثناء تصدير البيانات: {str(e)}', 'danger')
        return redirect(url_for('dashboard'))

# مسار لتنزيل قالب إكسل
@app.route('/person_data/template', methods=['GET'])
@login_required
def person_data_template():
    """
    تنزيل قالب إكسل لبيانات الأفراد
    """
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    try:
        # إنشاء DataFrame فارغ بالأعمدة المطلوبة
        columns = [
            'الاسم الشخصي',
            'الاسم المستعار',
            'العمر',
            'المحافظة',
            'المديرية',
            'العزلة',
            'الحي/القرية',
            'المؤهل العلمي',
            'الحالة الاجتماعية',
            'العمل',
            'الإدارة',
            'مكان العمل',
            'الرقم الوطني',
            'الرقم العسكري',
            'رقم التلفون'
        ]

        # إنشاء DataFrame فارغ مع صف واحد كمثال
        df = pd.DataFrame(columns=columns)
        df.loc[0] = ['اسم شخص', 'اسم مستعار', 30, 'صنعاء', 'السبعين', 'عزلة', 'حي/قرية', 'بكالوريوس', 'متزوج', 'مهندس', 'وزارة الدفاع', 'صنعاء', '123456789', '987654321', '777123456']

        # إنشاء ملف إكسل في الذاكرة
        output = io.BytesIO()
        writer = pd.ExcelWriter(output, engine='xlsxwriter')

        # تعيين اتجاه الورقة من اليمين إلى اليسار
        workbook = writer.book

        # إنشاء ورقة البيانات
        df.to_excel(writer, sheet_name='بيانات الأفراد', index=False)

        # الحصول على ورقة العمل
        worksheet = writer.sheets['بيانات الأفراد']

        # تنسيق الورقة
        # إنشاء تنسيق للعناوين
        header_format = workbook.add_format({
            'bold': True,
            'text_wrap': True,
            'valign': 'top',
            'fg_color': '#D7E4BC',
            'border': 1,
            'align': 'center'
        })

        # إنشاء تنسيق للبيانات
        data_format = workbook.add_format({
            'border': 1,
            'align': 'right'
        })

        # تطبيق التنسيق على العناوين
        for col_num, value in enumerate(df.columns.values):
            worksheet.write(0, col_num, value, header_format)
            worksheet.set_column(col_num, col_num, 20)  # تعيين عرض العمود

        # تطبيق التنسيق على البيانات
        for row_num in range(1, len(df) + 1):
            for col_num in range(len(df.columns)):
                worksheet.write(row_num, col_num, df.iloc[row_num-1, col_num], data_format)

        # إعادة المؤشر إلى بداية الملف
        writer.close()
        output.seek(0)

        # إرسال الملف كاستجابة
        return send_file(
            output,
            as_attachment=True,
            download_name='person_data_template.xlsx',
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
    except Exception as e:
        flash(f'حدث خطأ أثناء إنشاء قالب البيانات: {str(e)}', 'danger')
        return redirect(url_for('dashboard'))

# مسار لاستيراد بيانات الأفراد من ملف إكسل
@app.route('/person_data/import', methods=['POST'])
@login_required
def person_data_import():
    """
    استيراد بيانات الأفراد من ملف إكسل
    """
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # التحقق من وجود ملف
    if 'excel_file' not in request.files:
        flash('لم يتم تحديد ملف', 'danger')
        return redirect(url_for('person_data_index'))

    file = request.files['excel_file']

    # التحقق من أن الملف له اسم
    if file.filename == '':
        flash('لم يتم تحديد ملف', 'danger')
        return redirect(url_for('person_data_index'))

    # التحقق من أن الملف بصيغة إكسل
    if not file.filename.endswith(('.xlsx', '.xls')):
        flash('يجب أن يكون الملف بصيغة Excel (.xlsx أو .xls)', 'danger')
        return redirect(url_for('person_data_index'))

    try:
        # قراءة ملف الإكسل
        sheet_name = request.form.get('sheet_name', None)

        try:
            # محاولة قراءة الورقة بالاسم المحدد
            if sheet_name and sheet_name.strip():
                # التحقق من وجود الورقة بالاسم المحدد
                xls = pd.ExcelFile(file)
                if sheet_name not in xls.sheet_names:
                    flash(f'لم يتم العثور على ورقة باسم "{sheet_name}" في الملف. الأوراق المتاحة: {", ".join(xls.sheet_names)}', 'danger')
                    return redirect(url_for('person_data_index'))
                df = pd.read_excel(file, sheet_name=sheet_name)
            else:
                # استخدام الورقة الأولى إذا لم يتم تحديد اسم الورقة
                df = pd.read_excel(file, sheet_name=0)
        except Exception as e:
            flash(f'خطأ في قراءة ملف الإكسل: {str(e)}', 'danger')
            return redirect(url_for('person_data_index'))

        # التحقق من وجود الأعمدة المطلوبة
        required_columns = ['الاسم الشخصي']

        # تحويل أسماء الأعمدة إلى قائمة
        file_columns = df.columns.tolist()

        # طباعة أسماء الأعمدة للتشخيص
        print("أعمدة الملف:", file_columns)

        # محاولة مطابقة الأعمدة المطلوبة مع الأعمدة الموجودة
        column_mapping = {}
        for req_col in required_columns:
            # البحث عن تطابق دقيق
            if req_col in file_columns:
                column_mapping[req_col] = req_col
                print(f"تم العثور على العمود: {req_col}")
            else:
                # البحث عن تطابق جزئي
                for file_col in file_columns:
                    if isinstance(file_col, str) and (req_col in file_col or file_col in req_col):
                        column_mapping[req_col] = file_col
                        print(f"تم العثور على تطابق جزئي: {req_col} -> {file_col}")
                        break
                    # محاولة البحث عن كلمات مشابهة
                    elif isinstance(file_col, str):
                        if 'اسم' in file_col and req_col == 'الاسم الشخصي':
                            column_mapping[req_col] = file_col
                            print(f"تم العثور على تطابق بالكلمات المفتاحية: {req_col} -> {file_col}")
                            break

        # التحقق من وجود جميع الأعمدة المطلوبة
        missing_columns = [col for col in required_columns if col not in column_mapping]

        if missing_columns:
            flash(f'الأعمدة التالية مفقودة في ملف الإكسل: {", ".join(missing_columns)}', 'danger')
            return redirect(url_for('person_data_index'))

        # إضافة البيانات إلى قاعدة البيانات
        success_count = 0
        error_count = 0
        duplicate_records = []  # قائمة للسجلات المكررة
        new_records = []  # قائمة للسجلات الجديدة

        for _, row in df.iterrows():
            try:
                # استخدام التعيين الجديد للأعمدة
                name_col = column_mapping.get('الاسم الشخصي')

                if not name_col:
                    error_count += 1
                    continue

                # التحقق من أن القيم غير فارغة
                if pd.isna(row[name_col]):
                    error_count += 1
                    print(f"قيم فارغة: {row[name_col] if not pd.isna(row[name_col]) else 'فارغ'}")
                    continue

                # التحقق من وجود السجل مسبقاً
                existing_record = PersonData.query.filter_by(full_name=row[name_col]).first()
                if existing_record:
                    duplicate_records.append({
                        'full_name': row[name_col]
                    })
                    continue

                # معالجة القيم العددية
                age = row.get('العمر', None)
                if age and not pd.isna(age):
                    try:
                        age = int(age)
                    except:
                        age = None
                else:
                    age = None

                # إنشاء سجل جديد
                person_data = PersonData(
                    full_name=row[name_col],
                    nickname=row.get('الاسم المستعار', '') if not pd.isna(row.get('الاسم المستعار', '')) else None,
                    age=age,
                    governorate=row.get('المحافظة', '') if not pd.isna(row.get('المحافظة', '')) else None,
                    directorate=row.get('المديرية', '') if not pd.isna(row.get('المديرية', '')) else None,
                    uzla=row.get('العزلة', '') if not pd.isna(row.get('العزلة', '')) else None,
                    village=row.get('الحي/القرية', '') if not pd.isna(row.get('الحي/القرية', '')) else None,
                    qualification=row.get('المؤهل العلمي', '') if not pd.isna(row.get('المؤهل العلمي', '')) else None,
                    marital_status=row.get('الحالة الاجتماعية', '') if not pd.isna(row.get('الحالة الاجتماعية', '')) else None,
                    job=row.get('العمل', '') if not pd.isna(row.get('العمل', '')) else None,
                    agency=row.get('الإدارة', '') if not pd.isna(row.get('الإدارة', '')) else None,
                    work_place=row.get('مكان العمل', '') if not pd.isna(row.get('مكان العمل', '')) else None,
                    national_number=str(row.get('الرقم الوطني', '')) if not pd.isna(row.get('الرقم الوطني', '')) else None,
                    military_number=str(row.get('الرقم العسكري', '')) if not pd.isna(row.get('الرقم العسكري', '')) else None,
                    phone=str(row.get('رقم التلفون', '')) if not pd.isna(row.get('رقم التلفون', '')) else None
                )

                db.session.add(person_data)
                success_count += 1
                new_records.append({
                    'full_name': row[name_col]
                })
            except Exception as e:
                error_count += 1
                print(f"خطأ في إضافة السجل: {str(e)}")

        db.session.commit()

        # عرض رسالة نجاح مع تفاصيل
        if duplicate_records:
            flash(f'تم استيراد {success_count} سجل بنجاح من أصل {len(df)} سجل. تم تجاهل {len(duplicate_records)} سجل موجود مسبقاً.', 'warning')
        else:
            flash(f'تم استيراد {success_count} سجل بنجاح من أصل {len(df)} سجل.', 'success')

        return redirect(url_for('person_data_index'))
    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء استيراد البيانات: {str(e)}', 'danger')
        return redirect(url_for('person_data_index'))






# تم حذف مسارات الترحيب

# مسار صفحة بيانات الأشخاص - تم نقله إلى Blueprint
# @app.route('/person_data_table')
# @login_required
# def person_data_table():
#     """
#     صفحة بيانات الأشخاص
#     """
#     # التحقق من أن المستخدم هو مدير
#     if current_user.role != 'admin':
#         flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
#         return redirect(url_for('dashboard'))
#
#     return render_template('person_data_table.html', title='بيانات الأشخاص')

# مسار للحصول على بيانات الأشخاص بتنسيق JSON
@app.route('/person_data_json')
@login_required
def person_data_json():
    """
    الحصول على بيانات الأشخاص بتنسيق JSON
    """
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        return jsonify({'error': 'ليس لديك صلاحية للوصول إلى هذه البيانات'}), 403

    try:
        # جلب بيانات الأشخاص
        persons = PersonData.query.all()

        # تحويل البيانات إلى قائمة من القواميس
        data = []
        for person in persons:
            person_data = {
                'id': person.id,
                'full_name': person.full_name,
                'nickname': person.nickname if person.nickname else '',
                'age': person.age if person.age else '',
                'governorate': person.governorate if person.governorate else '',
                'directorate': person.directorate if person.directorate else '',
                'uzla': person.uzla if person.uzla else '',
                'village': person.village if person.village else '',
                'qualification': person.qualification if person.qualification else '',
                'marital_status': person.marital_status if person.marital_status else '',
                'job': person.job if person.job else '',
                'agency': person.agency if person.agency else '',
                'work_place': person.work_place if person.work_place else '',
                'national_number': person.national_number if person.national_number else '',
                'military_number': person.military_number if person.military_number else '',
                'phone': person.phone if person.phone else ''
            }
            data.append(person_data)

        return jsonify(data)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# مسار للحصول على بيانات شخص محدد بتنسيق JSON
@app.route('/person_data_get/<int:person_id>')
@login_required
def person_data_get(person_id):
    """
    الحصول على بيانات شخص محدد بتنسيق JSON
    """
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        return jsonify({'error': 'ليس لديك صلاحية للوصول إلى هذه البيانات'}), 403

    try:
        # البحث عن الشخص
        person = PersonData.query.get_or_404(person_id)

        # تحويل البيانات إلى قاموس
        person_data = {
            'id': person.id,
            'full_name': person.full_name,
            'nickname': person.nickname if person.nickname else '',
            'age': person.age if person.age else '',
            'governorate': person.governorate if person.governorate else '',
            'directorate': person.directorate if person.directorate else '',
            'uzla': person.uzla if person.uzla else '',
            'village': person.village if person.village else '',
            'qualification': person.qualification if person.qualification else '',
            'marital_status': person.marital_status if person.marital_status else '',
            'job': person.job if person.job else '',
            'agency': person.agency if person.agency else '',
            'work_place': person.work_place if person.work_place else '',
            'national_number': person.national_number if person.national_number else '',
            'military_number': person.military_number if person.military_number else '',
            'phone': person.phone if person.phone else ''
        }

        return jsonify(person_data)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# # مسار لإضافة شخص جديد - تم تعطيله لاستخدام Blueprint
# @app.route('/person_data_add', methods=['POST'])
# @login_required
# def person_data_add():
#     """
#     إضافة شخص جديد
#     """
#     # التحقق من أن المستخدم هو مدير
#     if current_user.role != 'admin':
#         return jsonify({'success': False, 'message': 'ليس لديك صلاحية للوصول إلى هذه الصفحة'}), 403
#
#     try:
#         # الحصول على البيانات من الطلب
#         data = request.json
#
#         # إنشاء كائن شخص جديد
#         person = PersonData(
#             full_name=data['full_name'],
#             nickname=data.get('nickname'),
#             age=int(data.get('age')) if data.get('age') else None,
#             governorate=data.get('governorate'),
#             directorate=data.get('directorate'),
#             uzla=data.get('uzla'),
#             village=data.get('village'),
#             qualification=data.get('qualification'),
#             marital_status=data.get('marital_status'),
#             job=data.get('job'),
#             agency=data.get('agency'),
#             work_place=data.get('work_place'),
#             national_number=data.get('national_number'),
#             military_number=data.get('military_number'),
#             phone=data.get('phone')
#         )
#
#         # إضافة الشخص إلى قاعدة البيانات
#         db.session.add(person)
#         db.session.commit()
#
#         return jsonify({'success': True, 'message': 'تمت إضافة الشخص بنجاح', 'id': person.id})
#     except Exception as e:
#         db.session.rollback()
#         return jsonify({'success': False, 'message': str(e)}), 500

# # مسار لتحديث بيانات شخص - تم تعطيله لاستخدام Blueprint
# @app.route('/person_data_update/<int:person_id>', methods=['POST'])
# @login_required
# def person_data_update(person_id):
#     """
#     تحديث بيانات شخص
#     """
#     # التحقق من أن المستخدم هو مدير
#     if current_user.role != 'admin':
#         return jsonify({'success': False, 'message': 'ليس لديك صلاحية للوصول إلى هذه الصفحة'}), 403
#
#     try:
#         # البحث عن الشخص
#         person = PersonData.query.get_or_404(person_id)
#
#         # الحصول على البيانات من الطلب
#         data = request.json
#
#         # تحديث بيانات الشخص
#         person.full_name = data['full_name']
#         person.nickname = data.get('nickname')
#         person.age = int(data.get('age')) if data.get('age') else None
#         person.governorate = data.get('governorate')
#         person.directorate = data.get('directorate')
#         person.uzla = data.get('uzla')
#         person.village = data.get('village')
#         person.qualification = data.get('qualification')
#         person.marital_status = data.get('marital_status')
#         person.job = data.get('job')
#         person.agency = data.get('agency')
#         person.work_place = data.get('work_place')
#         person.national_number = data.get('national_number')
#         person.military_number = data.get('military_number')
#         person.phone = data.get('phone')
#
#         # حفظ التغييرات
#         db.session.commit()
#
#         return jsonify({'success': True, 'message': 'تم تحديث بيانات الشخص بنجاح'})
#     except Exception as e:
#         db.session.rollback()
#         return jsonify({'success': False, 'message': str(e)}), 500

# # مسار لحذف شخص - تم تعطيله لاستخدام Blueprint
# @app.route('/person_data_delete/<int:person_id>', methods=['POST'])
# @login_required
# def person_data_delete(person_id):
#     """
#     حذف شخص
#     """
#     # التحقق من أن المستخدم هو مدير
#     if current_user.role != 'admin':
#         return jsonify({'success': False, 'message': 'ليس لديك صلاحية للوصول إلى هذه الصفحة'}), 403
#
#     try:
#         # البحث عن الشخص
#         person = PersonData.query.get_or_404(person_id)
#
#         # حذف الشخص
#         db.session.delete(person)
#         db.session.commit()
#
#         return jsonify({'success': True, 'message': 'تم حذف الشخص بنجاح'})
#     except Exception as e:
#         db.session.rollback()
#         return jsonify({'success': False, 'message': str(e)}), 500

# # مسار لتصدير بيانات الأشخاص إلى إكسل - تم تعطيله لاستخدام Blueprint
# @app.route('/person_data_export')
# @login_required
# def person_data_export():
#     """
#     تصدير بيانات الأشخاص إلى ملف إكسل
#     """
#     # التحقق من أن المستخدم هو مدير
#     if current_user.role != 'admin':
#         flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
#         return redirect(url_for('dashboard'))
#
#     try:
#         # جلب بيانات الأشخاص
#         persons = PersonData.query.all()
#
#         # إنشاء DataFrame
#         data = []
#         for person in persons:
#             person_data = {
#                 'الاسم الشخصي': person.full_name,
#                 'الاسم المستعار': person.nickname if person.nickname else '',
#                 'العمر': person.age if person.age else '',
#                 'المحافظة': person.governorate if person.governorate else '',
#                 'المديرية': person.directorate if person.directorate else '',
#                 'العزلة': person.uzla if person.uzla else '',
#                 'الحي/القرية': person.village if person.village else '',
#                 'المؤهل العلمي': person.qualification if person.qualification else '',
#                 'الحالة الاجتماعية': person.marital_status if person.marital_status else '',
#                 'العمل': person.job if person.job else '',
#                 'الإدارة': person.agency if person.agency else '',
#                 'مكان العمل': person.work_place if person.work_place else '',
#                 'الرقم الوطني': person.national_number if person.national_number else '',
#                 'الرقم العسكري': person.military_number if person.military_number else '',
#                 'رقم التلفون': person.phone if person.phone else ''
#             }
#             data.append(person_data)
#
#         # إنشاء DataFrame
#         df = pd.DataFrame(data)
#
#         # إنشاء ملف إكسل في الذاكرة
#         output = io.BytesIO()
#         with pd.ExcelWriter(output, engine='xlsxwriter') as writer:
#             df.to_excel(writer, sheet_name='بيانات الأشخاص', index=False)
#
#             # تنسيق ورقة العمل
#             workbook = writer.book
#             worksheet = writer.sheets['بيانات الأشخاص']
#
#             # تنسيق العناوين
#             header_format = workbook.add_format({
#                 'bold': True,
#                 'text_wrap': True,
#                 'valign': 'top',
#                 'fg_color': '#D7E4BC',
#                 'border': 1,
#                 'align': 'right'
#             })
#
#             # تطبيق التنسيق على العناوين
#             for col_num, value in enumerate(df.columns.values):
#                 worksheet.write(0, col_num, value, header_format)
#
#             # تعيين عرض الأعمدة
#             for i, column in enumerate(df.columns):
#                 column_width = max(df[column].astype(str).map(len).max(), len(column) + 2)
#                 worksheet.set_column(i, i, column_width)
#
#         # إعادة المؤشر إلى بداية الملف
#         output.seek(0)
#
#         # إنشاء اسم الملف
#         filename = f"بيانات_الأشخاص_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
#
#         # إرسال الملف
#         return send_file(
#             output,
#             as_attachment=True,
#             download_name=filename,
#             mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
#         )
#     except Exception as e:
#         flash(f'حدث خطأ أثناء تصدير البيانات: {str(e)}', 'danger')
#         return redirect(url_for('person_data.person_data_table'))

# # مسار لتنزيل قالب إكسل - تم تعطيله لاستخدام Blueprint
# @app.route('/person_data_template')
# @login_required
# def person_data_template():
#     """
#     تنزيل قالب إكسل لبيانات الأشخاص
#     """
#     # التحقق من أن المستخدم هو مدير
#     if current_user.role != 'admin':
#         flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
#         return redirect(url_for('dashboard'))
#
#     try:
#         # إنشاء DataFrame فارغ بالأعمدة المطلوبة
#         columns = [
#             'الاسم الشخصي', 'الاسم المستعار', 'العمر', 'المحافظة', 'المديرية',
#             'العزلة', 'الحي/القرية', 'المؤهل العلمي', 'الحالة الاجتماعية',
#             'العمل', 'الإدارة', 'مكان العمل', 'الرقم الوطني', 'الرقم العسكري', 'رقم التلفون'
#         ]
#         df = pd.DataFrame(columns=columns)
#
#         # إنشاء ملف إكسل في الذاكرة
#         output = io.BytesIO()
#         with pd.ExcelWriter(output, engine='xlsxwriter') as writer:
#             df.to_excel(writer, sheet_name='بيانات الأشخاص', index=False)
#
#             # تنسيق ورقة العمل
#             workbook = writer.book
#             worksheet = writer.sheets['بيانات الأشخاص']
#
#             # تنسيق العناوين
#             header_format = workbook.add_format({
#                 'bold': True,
#                 'text_wrap': True,
#                 'valign': 'top',
#                 'fg_color': '#D7E4BC',
#                 'border': 1,
#                 'align': 'right'
#             })
#
#             # تطبيق التنسيق على العناوين
#             for col_num, value in enumerate(df.columns.values):
#                 worksheet.write(0, col_num, value, header_format)
#
#             # تعيين عرض الأعمدة
#             for i, column in enumerate(df.columns):
#                 worksheet.set_column(i, i, len(column) + 5)
#
#             # تعيين اتجاه الورقة من اليمين إلى اليسار
#             worksheet.right_to_left()
#
#         # إعادة المؤشر إلى بداية الملف
#         output.seek(0)
#
#         # إنشاء اسم الملف
#         filename = f"قالب_بيانات_الأشخاص.xlsx"
#
#         # إرسال الملف
#         return send_file(
#             output,
#             as_attachment=True,
#             download_name=filename,
#             mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
#         )
#     except Exception as e:
#         flash(f'حدث خطأ أثناء تنزيل القالب: {str(e)}', 'danger')
#         return redirect(url_for('person_data.person_data_table'))

# # مسار لاستيراد بيانات الأشخاص من إكسل - تم تعطيله لاستخدام Blueprint
# @app.route('/person_data_import', methods=['POST'])
# @login_required
# def person_data_import():
#     """
#     استيراد بيانات الأشخاص من ملف إكسل
#     """
#     # التحقق من أن المستخدم هو مدير
#     if current_user.role != 'admin':
#         flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
#         return redirect(url_for('dashboard'))
#
#     try:
#         # التحقق من وجود ملف
#         if 'excel_file' not in request.files:
#             flash('لم يتم تحديد ملف', 'danger')
#             return redirect(url_for('person_data.person_data_table'))
#
#         file = request.files['excel_file']
#
#         # التحقق من أن الملف ليس فارغًا
#         if file.filename == '':
#             flash('لم يتم تحديد ملف', 'danger')
#             return redirect(url_for('person_data.person_data_table'))
#
#         # التحقق من امتداد الملف
#         if not file.filename.endswith(('.xlsx', '.xls')):
#             flash('يجب أن يكون الملف بتنسيق Excel (.xlsx, .xls)', 'danger')
#             return redirect(url_for('person_data.person_data_table'))
#
#         # قراءة ملف Excel
#         df = pd.read_excel(file)
#
#         # التحقق من وجود الأعمدة المطلوبة
#         required_columns = ['الاسم الشخصي']
#         missing_columns = [col for col in required_columns if col not in df.columns]
#         if missing_columns:
#             flash(f'الأعمدة التالية مفقودة في الملف: {", ".join(missing_columns)}', 'danger')
#             return redirect(url_for('person_data.person_data_table'))
#
#         # استيراد البيانات
#         success_count = 0
#         error_count = 0
#         error_messages = []
#
#         for index, row in df.iterrows():
#             try:
#                 # التحقق من أن الاسم الشخصي غير فارغ
#                 if pd.isna(row['الاسم الشخصي']):
#                     error_count += 1
#                     error_messages.append(f'الصف {index + 2}: الاسم الشخصي مطلوب')
#                     continue
#
#                 # إنشاء كائن شخص جديد
#                 person = PersonData(
#                     full_name=row['الاسم الشخصي'],
#                     nickname=row['الاسم المستعار'] if 'الاسم المستعار' in row and not pd.isna(row['الاسم المستعار']) else None,
#                     age=int(row['العمر']) if 'العمر' in row and not pd.isna(row['العمر']) else None,
#                     governorate=row['المحافظة'] if 'المحافظة' in row and not pd.isna(row['المحافظة']) else None,
#                     directorate=row['المديرية'] if 'المديرية' in row and not pd.isna(row['المديرية']) else None,
#                     uzla=row['العزلة'] if 'العزلة' in row and not pd.isna(row['العزلة']) else None,
#                     village=row['الحي/القرية'] if 'الحي/القرية' in row and not pd.isna(row['الحي/القرية']) else None,
#                     qualification=row['المؤهل العلمي'] if 'المؤهل العلمي' in row and not pd.isna(row['المؤهل العلمي']) else None,
#                     marital_status=row['الحالة الاجتماعية'] if 'الحالة الاجتماعية' in row and not pd.isna(row['الحالة الاجتماعية']) else None,
#                     job=row['العمل'] if 'العمل' in row and not pd.isna(row['العمل']) else None,
#                     agency=row['الإدارة'] if 'الإدارة' in row and not pd.isna(row['الإدارة']) else None,
#                     work_place=row['مكان العمل'] if 'مكان العمل' in row and not pd.isna(row['مكان العمل']) else None,
#                     national_number=str(row['الرقم الوطني']) if 'الرقم الوطني' in row and not pd.isna(row['الرقم الوطني']) else None,
#                     military_number=str(row['الرقم العسكري']) if 'الرقم العسكري' in row and not pd.isna(row['الرقم العسكري']) else None,
#                     phone=str(row['رقم التلفون']) if 'رقم التلفون' in row and not pd.isna(row['رقم التلفون']) else None
#                 )
#
#                 # إضافة الشخص إلى قاعدة البيانات
#                 db.session.add(person)
#                 db.session.commit()
#
#                 success_count += 1
#             except Exception as e:
#                 db.session.rollback()
#                 error_count += 1
#                 error_messages.append(f'الصف {index + 2}: {str(e)}')
#
#         # عرض رسالة النجاح
#         if success_count > 0:
#             flash(f'تم استيراد {success_count} سجل بنجاح', 'success')
#
#         # عرض رسالة الخطأ
#         if error_count > 0:
#             flash(f'فشل استيراد {error_count} سجل', 'warning')
#             for message in error_messages[:10]:  # عرض أول 10 أخطاء فقط
#                 flash(message, 'warning')
#             if len(error_messages) > 10:
#                 flash(f'... و {len(error_messages) - 10} أخطاء أخرى', 'warning')
#
#         return redirect(url_for('person_data.person_data_table'))
#     except Exception as e:
#         flash(f'حدث خطأ أثناء استيراد البيانات: {str(e)}', 'danger')
#         return redirect(url_for('person_data.person_data_table'))

# معالج أخطاء 400 لإرجاع JSON
@app.errorhandler(400)
def bad_request_error(error):
    return jsonify({
        'success': False,
        'message': f'طلب غير صحيح: {str(error)}'
    }), 400

# معالج أخطاء 500 لإرجاع JSON
@app.errorhandler(500)
def internal_error(error):
    return jsonify({
        'success': False,
        'message': f'خطأ داخلي في الخادم: {str(error)}'
    }), 500

# تشغيل التطبيق
if __name__ == '__main__':
    try:
        # تهيئة النظام الذكي للنسخ الاحتياطي إذا لم يتم تهيئته
        if not hasattr(smart_backup_manager, 'app') or smart_backup_manager.app is None:
            smart_backup_manager.init_app(app)
            print("✅ تم تفعيل النظام الذكي للنسخ الاحتياطي")

        print("🚀 بدء تشغيل الخادم...")

        # إعدادات النشر المرنة
        import os
        host = os.environ.get('HOST', '127.0.0.1')
        port = int(os.environ.get('PORT', 5000))
        debug = os.environ.get('DEBUG', 'True').lower() == 'true'

        # للنشر على الشبكة المحلية، استخدم HOST=0.0.0.0
        # For local network deployment, use HOST=0.0.0.0

        print(f"📡 الخادم: {host}:{port}")
        print(f"🔧 وضع التطوير: {debug}")

        app.run(debug=debug, host=host, port=port, threaded=True)
    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")
        import traceback
        traceback.print_exc()
        # تشغيل بدون debug mode في حالة الخطأ
        print("🔄 محاولة تشغيل بدون debug mode...")
        app.run(host='127.0.0.1', port=5000, threaded=True)






