@echo off
chcp 65001 >nul
echo ========================================
echo    إنشاء حزمة العميل النهائية
echo    Creating Final Client Package
echo ========================================
echo.

cd /d "%~dp0\.."

echo 📁 التحقق من وجود مجلد التوزيع...
echo 📁 Checking distribution folder...

if not exist "dist\TrainingSystem" (
    echo ❌ مجلد التوزيع غير موجود
    echo ❌ Distribution folder not found
    echo يرجى تشغيل build_optimized_exe.bat أولاً
    echo Please run build_optimized_exe.bat first
    pause
    exit /b 1
)

echo ✅ تم العثور على مجلد التوزيع
echo ✅ Distribution folder found
echo.

echo 📦 إنشاء حزمة العميل النهائية...
echo 📦 Creating final client package...

REM إنشاء مجلد الحزمة النهائية
set package_name=TrainingSystem_Final_%date:~-4,4%_%date:~-10,2%_%date:~-7,2%
set package_name=%package_name: =0%

if exist "%package_name%" (
    rmdir /s /q "%package_name%"
)

mkdir "%package_name%"

echo 📋 نسخ ملفات النظام...
echo 📋 Copying system files...

REM نسخ مجلد التوزيع بالكامل
xcopy "dist\TrainingSystem" "%package_name%\TrainingSystem" /e /i /h /y >nul

if errorlevel 1 (
    echo ❌ فشل في نسخ ملفات النظام
    echo ❌ Failed to copy system files
    pause
    exit /b 1
)

echo ✅ تم نسخ ملفات النظام
echo ✅ System files copied
echo.

echo 📚 إنشاء الوثائق...
echo 📚 Creating documentation...

REM إنشاء دليل التثبيت
(
echo ========================================
echo    🎓 نظام إدارة التدريب
echo    دليل التثبيت والتشغيل
echo ========================================
echo.
echo 📋 متطلبات النظام:
echo    • Windows 10 أو أحدث
echo    • 4 GB RAM كحد أدنى
echo    • 2 GB مساحة فارغة على القرص الصلب
echo    • لا يتطلب تثبيت Python أو أي برامج إضافية
echo.
echo 🚀 خطوات التشغيل:
echo    1. فك ضغط الملف في أي مكان على الجهاز
echo    2. افتح مجلد TrainingSystem
echo    3. انقر نقراً مزدوجاً على START_SYSTEM.bat
echo    4. انتظر حتى يفتح النظام في المتصفح تلقائياً
echo.
echo 👤 معلومات تسجيل الدخول الافتراضية:
echo    • اسم المستخدم: admin
echo    • كلمة المرور: admin123
echo    • ⚠️  يرجى تغيير كلمة المرور فوراً بعد أول تسجيل دخول
echo.
echo 🔧 استكشاف الأخطاء:
echo    • إذا لم يفتح النظام تلقائياً، افتح المتصفح واذهب إلى:
echo      http://localhost:5000
echo    • إذا ظهرت رسالة خطأ في البورت، أعد تشغيل النظام
echo    • تأكد من عدم تشغيل برامج أخرى تستخدم نفس البورت
echo.
echo 💾 النسخ الاحتياطية:
echo    • يتم إنشاء نسخة احتياطية تلقائياً عند بدء التشغيل
echo    • لإنشاء نسخة احتياطية يدوية، شغل BACKUP_DATA.bat
echo    • النسخ الاحتياطية تحفظ في مجلد backup
echo.
echo 📞 الدعم الفني:
echo    • البريد الإلكتروني: <EMAIL>
echo    • الهاتف: +967-xxx-xxx-xxx
echo    • الموقع: www.trainingsystem.com
echo.
echo © 2024 Training System. جميع الحقوق محفوظة
) > "%package_name%\دليل_التثبيت.txt"

REM إنشاء دليل المستخدم
(
echo ========================================
echo    🎓 نظام إدارة التدريب
echo    دليل المستخدم
echo ========================================
echo.
echo 📚 الوظائف الأساسية:
echo.
echo 1. 👤 إدارة المستخدمين:
echo    • إضافة مستخدمين جدد
echo    • تعديل صلاحيات المستخدمين
echo    • إدارة كلمات المرور
echo.
echo 2. 📊 إدارة الدورات:
echo    • إنشاء دورات تدريبية جديدة
echo    • تعديل معلومات الدورات
echo    • إدارة المشاركين في الدورات
echo.
echo 3. 👥 إدارة المشاركين:
echo    • إضافة مشاركين جدد
echo    • استيراد بيانات من ملفات Excel
echo    • تتبع حضور المشاركين
echo.
echo 4. 📈 التقارير:
echo    • تقارير الدورات
echo    • تقارير المشاركين
echo    • تقارير الحضور والغياب
echo    • تصدير التقارير إلى Excel
echo.
echo 5. 🔍 البحث والفلترة:
echo    • البحث في قاعدة البيانات
echo    • فلترة النتائج حسب معايير مختلفة
echo    • البحث المتقدم
echo.
echo 📋 نصائح للاستخدام:
echo    • احرص على عمل نسخة احتياطية دورياً
echo    • استخدم أسماء مستخدمين وكلمات مرور قوية
echo    • تأكد من صحة البيانات قبل الحفظ
echo    • راجع التقارير بانتظام للتأكد من دقة البيانات
echo.
echo 🔒 الأمان:
echo    • غير كلمة المرور الافتراضية فوراً
echo    • لا تشارك معلومات تسجيل الدخول
echo    • احتفظ بنسخة احتياطية من قاعدة البيانات
echo.
echo © 2024 Training System. جميع الحقوق محفوظة
) > "%package_name%\دليل_المستخدم.txt"

REM إنشاء ملف الإصدار
(
echo ========================================
echo    🎓 نظام إدارة التدريب
echo    معلومات الإصدار
echo ========================================
echo.
echo 📋 معلومات الإصدار:
echo    • رقم الإصدار: 1.0.0
echo    • تاريخ البناء: %date% %time%
echo    • نوع التوزيع: Standalone EXE
echo    • حجم الحزمة: سيتم حسابه تلقائياً
echo.
echo 🔧 التقنيات المستخدمة:
echo    • Python 3.11
echo    • Flask 2.3.3
echo    • SQLAlchemy 2.0.21
echo    • PyInstaller 5.13.2
echo.
echo 📦 محتويات الحزمة:
echo    • TrainingSystem.exe - الملف التنفيذي الرئيسي
echo    • _internal/ - ملفات النظام الداخلية
echo    • static/ - ملفات الواجهة ^(CSS, JS, Images^)
echo    • templates/ - قوالب HTML
echo    • training_system_deployment.db - قاعدة البيانات
echo    • START_SYSTEM.bat - ملف التشغيل السريع
echo    • BACKUP_DATA.bat - ملف النسخ الاحتياطي
echo    • SYSTEM_INFO.txt - معلومات النظام
echo.
echo 🆕 ميزات هذا الإصدار:
echo    • واجهة مستخدم محسنة باللغة العربية
echo    • نظام إدارة دورات تدريبية شامل
echo    • استيراد وتصدير بيانات Excel
echo    • نظام تقارير متقدم
echo    • نظام نسخ احتياطي ذكي
echo    • دعم البحث المتقدم
echo.
echo © 2024 Training System. جميع الحقوق محفوظة
) > "%package_name%\معلومات_الإصدار.txt"

echo ✅ تم إنشاء الوثائق
echo ✅ Documentation created
echo.

echo 📊 حساب حجم الحزمة...
echo 📊 Calculating package size...

for /f "tokens=3" %%a in ('dir "%package_name%" /-c /s ^| find "File(s)"') do set package_size=%%a

echo    📏 حجم الحزمة: %package_size% بايت
echo    📏 Package size: %package_size% bytes

REM تحديث معلومات الحجم في ملف الإصدار
echo    • حجم الحزمة: %package_size% بايت >> "%package_name%\معلومات_الإصدار.txt"

echo.
echo 🗜️  ضغط الحزمة النهائية...
echo 🗜️  Compressing final package...

REM إنشاء ملف مضغوط إذا كان 7-Zip متوفراً
where 7z >nul 2>&1
if not errorlevel 1 (
    echo تم العثور على 7-Zip، جاري الضغط...
    7z a -tzip "%package_name%.zip" "%package_name%\*" -mx9 >nul
    if not errorlevel 1 (
        echo ✅ تم إنشاء الملف المضغوط: %package_name%.zip
        echo ✅ Compressed file created: %package_name%.zip
    )
) else (
    echo ⚠️  7-Zip غير متوفر، لم يتم إنشاء ملف مضغوط
    echo ⚠️  7-Zip not available, compressed file not created
)

echo.
echo 🎉 تم إنشاء حزمة العميل النهائية بنجاح!
echo 🎉 Final client package created successfully!
echo.

echo 📦 محتويات الحزمة:
echo 📦 Package contents:
echo    📁 %package_name%\
echo    ├── 📁 TrainingSystem\
echo    │   ├── 🚀 TrainingSystem.exe
echo    │   ├── 📁 _internal\
echo    │   ├── 📁 static\
echo    │   ├── 📁 templates\
echo    │   ├── 🗄️  training_system_deployment.db
echo    │   ├── ⚡ START_SYSTEM.bat
echo    │   ├── 💾 BACKUP_DATA.bat
echo    │   └── ℹ️  SYSTEM_INFO.txt
echo    ├── 📖 دليل_التثبيت.txt
echo    ├── 📚 دليل_المستخدم.txt
echo    └── ℹ️  معلومات_الإصدار.txt

if exist "%package_name%.zip" (
    echo    └── 🗜️  %package_name%.zip
)

echo.
echo 📋 خطوات التسليم للعميل:
echo 📋 Client delivery steps:
echo    1. ✅ نسخ مجلد %package_name% إلى وسائط التخزين
echo    1. ✅ Copy %package_name% folder to storage media
echo    2. ✅ تضمين جميع ملفات الوثائق
echo    2. ✅ Include all documentation files
echo    3. ✅ تقديم الدعم الفني للتثبيت
echo    3. ✅ Provide technical support for installation
echo.

echo 🎯 الحزمة جاهزة للتسليم!
echo 🎯 Package ready for delivery!
echo.

pause
