#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار سريع للروابط
"""

import subprocess
import sys

def test_route(route):
    """اختبار رابط واحد"""
    try:
        cmd = f'curl -s -o /dev/null -w "%{{http_code}}" http://127.0.0.1:5000{route}'
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=5)
        return result.stdout.strip()
    except:
        return "ERROR"

def main():
    print("🔍 اختبار سريع للروابط")
    print("=" * 30)
    
    # اختبار روابط الترحيب
    welcome_routes = ['/welcome', '/welcome-text', '/hello']
    
    print("📋 روابط الترحيب:")
    for route in welcome_routes:
        status = test_route(route)
        if status == "404":
            print(f"   ✅ {route}: محذوف")
        elif status == "200":
            print(f"   ❌ {route}: يعمل")
        else:
            print(f"   ⚠️ {route}: {status}")
    
    # اختبار الروابط الرئيسية
    main_routes = ['/dashboard', '/courses']
    
    print("\n🔗 الروابط الرئيسية:")
    for route in main_routes:
        status = test_route(route)
        if status in ["200", "302"]:
            print(f"   ✅ {route}: يعمل ({status})")
        else:
            print(f"   ❌ {route}: {status}")

if __name__ == "__main__":
    main()
