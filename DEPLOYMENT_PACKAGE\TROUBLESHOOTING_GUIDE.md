# دليل استكشاف الأخطاء وإصلاحها
# Troubleshooting Guide

## 🔧 المشاكل الشائعة وحلولها / Common Issues and Solutions

---

## 1. 🎨 مشكلة تغيير التنسيق / Layout Issues

### المشكلة:
- تظهر الصفحات بتنسيق مكسور أو بدون CSS
- الألوان والخطوط لا تظهر بشكل صحيح
- الواجهة تبدو مختلفة عن المتوقع

### الأسباب المحتملة:
1. عدم تضمين ملفات CSS بشكل صحيح
2. مشاكل في مسارات الملفات الثابتة
3. عدم تحميل ملفات JavaScript

### الحلول:

#### الحل 1: التحقق من ملفات CSS
```bash
# التحقق من وجود مجلد static
dir static\css

# التحقق من ملفات CSS
dir static\css\*.css
```

#### الحل 2: إعادة نسخ ملفات static
```bash
# نسخ ملفات static من المشروع الأصلي
xcopy "المشروع_الأصلي\static" "dist\TrainingSystem\static" /e /i /h /y
```

#### الحل 3: التحقق من مسارات الملفات
- تأكد من أن مجلد `static` موجود في نفس مجلد الملف التنفيذي
- تحقق من أن ملفات CSS موجودة في `static\css\`
- تأكد من أن ملفات JavaScript موجودة في `static\js\`

---

## 2. 🔐 مشكلة فشل تسجيل الدخول / Login Failure

### المشكلة:
- لا يمكن تسجيل الدخول بالمعلومات الافتراضية
- رسالة خطأ "اسم المستخدم أو كلمة المرور غير صحيحة"
- الصفحة تعيد التوجيه إلى نفسها

### الأسباب المحتملة:
1. قاعدة البيانات فارغة أو تالفة
2. عدم إنشاء المستخدم الإداري الافتراضي
3. مشاكل في تشفير كلمة المرور

### الحلول:

#### الحل 1: التحقق من قاعدة البيانات
```bash
# التحقق من وجود قاعدة البيانات
dir training_system_deployment.db

# التحقق من حجم قاعدة البيانات (يجب أن يكون أكبر من 0)
for %f in (training_system_deployment.db) do echo %~zf
```

#### الحل 2: إعادة إنشاء قاعدة البيانات
```bash
# حذف قاعدة البيانات التالفة
del training_system_deployment.db

# تشغيل النظام مرة أخرى (سيتم إنشاء قاعدة بيانات جديدة)
TrainingSystem.exe
```

#### الحل 3: استخدام المعلومات الافتراضية الصحيحة
- **اسم المستخدم:** `admin`
- **كلمة المرور:** `admin123`
- تأكد من عدم وجود مسافات إضافية
- تأكد من أن Caps Lock غير مفعل

---

## 3. 🗄️ مشكلة قاعدة البيانات الفارغة / Empty Database

### المشكلة:
- لا توجد بيانات في النظام
- الجداول فارغة
- لا يمكن إضافة بيانات جديدة

### الأسباب المحتملة:
1. قاعدة البيانات لم يتم إنشاؤها بشكل صحيح
2. مشاكل في أذونات الملفات
3. قاعدة البيانات في مكان خاطئ

### الحلول:

#### الحل 1: إعادة إنشاء قاعدة البيانات
```bash
# إيقاف النظام
taskkill /f /im TrainingSystem.exe

# حذف قاعدة البيانات الحالية
del training_system_deployment.db

# تشغيل النظام مرة أخرى
TrainingSystem.exe
```

#### الحل 2: التحقق من الأذونات
```bash
# إعطاء أذونات كاملة لمجلد النظام
icacls . /grant Everyone:F /T
```

#### الحل 3: نسخ قاعدة بيانات جاهزة
```bash
# نسخ قاعدة بيانات من المشروع الأصلي
copy "المشروع_الأصلي\training_system.db" "training_system_deployment.db"
```

---

## 4. 🌐 مشاكل البورتات / Port Issues

### المشكلة:
- رسالة خطأ "البورت مستخدم بالفعل"
- لا يمكن الوصول للنظام عبر المتصفح
- النظام لا يبدأ

### الأسباب المحتملة:
1. البورت 5000 مستخدم من برنامج آخر
2. جدار الحماية يحجب البورت
3. مشاكل في إعدادات الشبكة

### الحلول:

#### الحل 1: التحقق من البورتات المستخدمة
```bash
# عرض البورتات المستخدمة
netstat -an | find "5000"

# البحث عن البرامج التي تستخدم البورت
netstat -ano | find "5000"
```

#### الحل 2: إيقاف البرامج المتضاربة
```bash
# إيقاف البرامج التي تستخدم البورت
taskkill /f /pid [رقم_العملية]
```

#### الحل 3: استخدام بورت مختلف
- النظام يبحث تلقائياً عن بورت متاح
- إذا لم يجد، جرب إعادة تشغيل الجهاز

---

## 5. 📁 مشاكل الملفات المفقودة / Missing Files

### المشكلة:
- رسائل خطأ حول ملفات مفقودة
- النظام لا يعمل بشكل كامل
- بعض الوظائف لا تعمل

### الأسباب المحتملة:
1. ملفات لم يتم نسخها بشكل صحيح
2. ملفات تم حذفها بالخطأ
3. مشاكل في عملية البناء

### الحلول:

#### الحل 1: التحقق من الملفات الأساسية
```bash
# التحقق من الملفات المطلوبة
dir TrainingSystem.exe
dir _internal
dir static
dir templates
```

#### الحل 2: إعادة بناء النظام
```bash
# تشغيل سكريبت البناء الرئيسي
DEPLOYMENT_PACKAGE\MASTER_BUILD.bat
```

#### الحل 3: نسخ الملفات المفقودة يدوياً
```bash
# نسخ ملفات من المشروع الأصلي
xcopy "المشروع_الأصلي\static" "static" /e /i /h /y
xcopy "المشروع_الأصلي\templates" "templates" /e /i /h /y
```

---

## 6. 🔄 مشاكل الأداء / Performance Issues

### المشكلة:
- النظام بطيء في التحميل
- استجابة بطيئة للصفحات
- استهلاك عالي للذاكرة

### الأسباب المحتملة:
1. قاعدة بيانات كبيرة الحجم
2. ملفات غير محسنة
3. مشاكل في النظام

### الحلول:

#### الحل 1: تحسين قاعدة البيانات
```bash
# تشغيل ملف تحسين قاعدة البيانات
sqlite3 training_system_deployment.db "VACUUM; ANALYZE;"
```

#### الحل 2: إعادة تشغيل النظام
```bash
# إيقاف النظام
taskkill /f /im TrainingSystem.exe

# انتظار 5 ثوانٍ
timeout /t 5

# تشغيل النظام مرة أخرى
TrainingSystem.exe
```

#### الحل 3: تنظيف الملفات المؤقتة
```bash
# تنظيف ملفات النظام المؤقتة
del /q %temp%\*.*
```

---

## 7. 🛠️ أدوات التشخيص / Diagnostic Tools

### أداة فحص النظام السريع:
```bash
@echo off
echo === فحص سريع للنظام ===
echo.

echo 1. التحقق من الملف التنفيذي:
if exist TrainingSystem.exe (echo ✅ موجود) else (echo ❌ مفقود)

echo 2. التحقق من قاعدة البيانات:
if exist training_system_deployment.db (echo ✅ موجودة) else (echo ❌ مفقودة)

echo 3. التحقق من مجلد static:
if exist static (echo ✅ موجود) else (echo ❌ مفقود)

echo 4. التحقق من مجلد templates:
if exist templates (echo ✅ موجود) else (echo ❌ مفقود)

echo 5. التحقق من البورت 5000:
netstat -an | find "5000" >nul
if errorlevel 1 (echo ✅ متاح) else (echo ❌ مستخدم)

pause
```

---

## 8. 📞 الحصول على المساعدة / Getting Help

### معلومات الدعم الفني:
- **البريد الإلكتروني:** <EMAIL>
- **الهاتف:** +967-xxx-xxx-xxx
- **الموقع:** www.trainingsystem.com

### عند طلب المساعدة، يرجى تقديم:
1. وصف مفصل للمشكلة
2. رسائل الخطأ (إن وجدت)
3. خطوات إعادة إنتاج المشكلة
4. إصدار النظام
5. نظام التشغيل المستخدم

### ملفات السجلات:
- تحقق من ملفات السجلات في مجلد `logs`
- ابحث عن ملفات `.log` في مجلد النظام
- احتفظ بنسخة من رسائل الخطأ

---

## 9. 🔒 نصائح الأمان / Security Tips

1. **غير كلمة المرور الافتراضية فوراً**
2. **استخدم كلمات مرور قوية**
3. **احتفظ بنسخ احتياطية منتظمة**
4. **لا تشارك معلومات تسجيل الدخول**
5. **حدث النظام بانتظام**

---

## 10. 📋 قائمة التحقق السريع / Quick Checklist

قبل طلب المساعدة، تأكد من:

- [ ] إعادة تشغيل النظام
- [ ] التحقق من وجود جميع الملفات
- [ ] استخدام المعلومات الافتراضية الصحيحة
- [ ] التحقق من البورتات
- [ ] تنظيف الملفات المؤقتة
- [ ] التحقق من أذونات الملفات
- [ ] إعادة تشغيل الجهاز

---

*تم إنشاء هذا الدليل بواسطة فريق تطوير نظام التدريب*
*© 2024 Training System. جميع الحقوق محفوظة*
