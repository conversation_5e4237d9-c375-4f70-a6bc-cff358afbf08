{% extends "layout.html" %}

{% block styles %}
<style>
    /* خلفية رسمية متحركة للصفحة */
    body {
        background:
            linear-gradient(135deg, #1e3a8a 0%, #3b82f6 25%, #60a5fa 50%, #93c5fd 75%, #dbeafe 100%);
        background-size: 400% 400%;
        animation: gradientShift 20s ease infinite;
        min-height: 100vh;
        position: relative;
        overflow-x: hidden;
    }

    @keyframes gradientShift {
        0% { background-position: 0% 50%; }
        50% { background-position: 100% 50%; }
        100% { background-position: 0% 50%; }
    }

    body::before {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background:
            radial-gradient(circle at 20% 80%, rgba(30, 58, 138, 0.3) 0%, transparent 50%),
            radial-gradient(circle at 80% 20%, rgba(59, 130, 246, 0.2) 0%, transparent 50%),
            radial-gradient(circle at 40% 40%, rgba(147, 197, 253, 0.2) 0%, transparent 50%),
            radial-gradient(circle at 60% 70%, rgba(219, 234, 254, 0.1) 0%, transparent 50%);
        pointer-events: none;
        z-index: -1;
        animation: floatingBubbles 25s ease-in-out infinite;
    }

    @keyframes floatingBubbles {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        33% { transform: translateY(-15px) rotate(120deg); }
        66% { transform: translateY(8px) rotate(240deg); }
    }

    /* تحسين الشريط الجانبي بتصميم زجاجي */
    .sidebar {
        background:
            linear-gradient(135deg, rgba(30, 58, 138, 0.95) 0%, rgba(30, 64, 175, 0.95) 50%, rgba(59, 130, 246, 0.95) 100%);
        color: white;
        min-height: calc(100vh - 56px);
        padding-top: 35px;
        border-radius: 25px;
        box-shadow:
            0 25px 50px rgba(30, 58, 138, 0.4),
            inset 0 1px 0 rgba(255, 255, 255, 0.2);
        backdrop-filter: blur(20px);
        border: 2px solid rgba(255, 255, 255, 0.15);
        position: relative;
        overflow: hidden;
    }

    .sidebar::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
        pointer-events: none;
    }

    /* تصميم صورة المستخدم الافتراضية */
    .user-avatar {
        width: 100px;
        height: 100px;
        border-radius: 50%;
        background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 15px;
        box-shadow:
            0 15px 30px rgba(30, 64, 175, 0.4),
            inset 0 1px 0 rgba(255, 255, 255, 0.3);
        border: 3px solid rgba(255, 255, 255, 0.2);
        position: relative;
        overflow: hidden;
        transition: all 0.3s ease;
    }

    .user-avatar::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, transparent 50%);
        border-radius: 50%;
    }

    .user-avatar i {
        font-size: 2.5rem;
        color: white;
        z-index: 2;
        position: relative;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }

    .user-avatar:hover {
        transform: scale(1.05);
        box-shadow:
            0 20px 40px rgba(30, 64, 175, 0.5),
            inset 0 1px 0 rgba(255, 255, 255, 0.4);
        border-color: rgba(255, 255, 255, 0.3);
    }

    .sidebar-link {
        color: rgba(255, 255, 255, 0.8);
        padding: 15px 20px;
        display: block;
        text-decoration: none;
        transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        border-radius: 15px;
        margin: 8px 15px;
        position: relative;
        overflow: hidden;
        font-weight: 500;
    }

    .sidebar-link::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
        transition: left 0.5s;
    }

    .sidebar-link:hover::before {
        left: 100%;
    }

    .sidebar-link:hover, .sidebar-link.active {
        color: white;
        background: linear-gradient(135deg, rgba(59, 130, 246, 0.4) 0%, rgba(147, 197, 253, 0.3) 100%);
        transform: translateX(8px);
        box-shadow:
            0 8px 20px rgba(59, 130, 246, 0.3),
            inset 0 1px 0 rgba(255, 255, 255, 0.2);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .sidebar-link i {
        margin-left: 10px;
        width: 20px;
        text-align: center;
    }

    /* تحسين الحاوي الرئيسي بتصميم زجاجي متطور */
    .main-container {
        background:
            linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
        backdrop-filter: blur(25px);
        border-radius: 30px;
        padding: 40px;
        margin-top: 25px;
        box-shadow:
            0 30px 60px rgba(30, 64, 175, 0.2),
            inset 0 1px 0 rgba(255, 255, 255, 0.3);
        border: 2px solid rgba(255, 255, 255, 0.2);
        position: relative;
        overflow: hidden;
    }

    .main-container::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 4px;
        background: linear-gradient(90deg, #1e40af, #3b82f6, #60a5fa, #93c5fd);
        background-size: 300% 100%;
        animation: shimmer 4s ease-in-out infinite;
    }

    .main-container::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
        pointer-events: none;
        opacity: 0.5;
    }

    @keyframes shimmer {
        0% { background-position: -300% 0; }
        100% { background-position: 300% 0; }
    }

    .course-card {
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        transition: all 0.3s;
        height: 100%;
    }

    .course-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
    }

    .course-image {
        height: 200px;
        background-size: cover;
        background-position: center;
    }

    .course-category {
        position: absolute;
        top: 10px;
        right: 10px;
        padding: 5px 10px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: bold;
    }

    .course-body {
        padding: 20px;
    }

    .course-title {
        font-weight: bold;
        margin-bottom: 10px;
    }

    .course-instructor {
        color: #6c757d;
        font-size: 0.9rem;
        margin-bottom: 10px;
    }

    .course-description {
        color: #495057;
        margin-bottom: 15px;
        font-size: 0.9rem;
    }

    .course-meta {
        display: flex;
        justify-content: space-between;
        border-top: 1px solid #e9ecef;
        padding-top: 15px;
    }

    .course-rating {
        color: #ffc107;
    }

    .course-students {
        color: #6c757d;
        font-size: 0.9rem;
    }

    .filter-section {
        background:
            linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
        backdrop-filter: blur(20px);
        padding: 30px;
        border-radius: 25px;
        margin-bottom: 30px;
        border: 2px solid rgba(255, 255, 255, 0.2);
        box-shadow:
            0 20px 40px rgba(30, 64, 175, 0.1),
            inset 0 1px 0 rgba(255, 255, 255, 0.3);
    }

    .form-label {
        color: #1e40af;
        font-weight: 600;
        margin-bottom: 8px;
    }

    .form-control, .form-select {
        border: 2px solid rgba(59, 130, 246, 0.2);
        border-radius: 15px;
        padding: 12px 15px;
        background: rgba(255, 255, 255, 0.9);
        backdrop-filter: blur(10px);
        transition: all 0.3s ease;
    }

    .form-control:focus, .form-select:focus {
        border-color: #3b82f6;
        box-shadow: 0 0 0 0.2rem rgba(59, 130, 246, 0.25);
        background: rgba(255, 255, 255, 1);
    }

    .btn {
        border-radius: 15px;
        padding: 12px 20px;
        font-weight: 600;
        transition: all 0.3s ease;
        border: none;
        position: relative;
        overflow: hidden;
    }

    .btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
        transition: left 0.5s;
    }

    .btn:hover::before {
        left: 100%;
    }

    .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
    }

    .pagination .page-link {
        border-radius: 15px;
        margin: 0 5px;
        border: 2px solid rgba(59, 130, 246, 0.2);
        color: #1e40af;
        background: rgba(255, 255, 255, 0.9);
        backdrop-filter: blur(10px);
        transition: all 0.3s ease;
    }

    .pagination .page-link:hover {
        background: #3b82f6;
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(59, 130, 246, 0.3);
    }

    .pagination .page-item.active .page-link {
        background: #1e40af;
        border-color: #1e40af;
        color: white;
    }

    .alert {
        border-radius: 20px;
        border: none;
        backdrop-filter: blur(10px);
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    }

    .card-footer {
        background: rgba(255, 255, 255, 0.1) !important;
        border-top: 1px solid rgba(255, 255, 255, 0.2);
        backdrop-filter: blur(10px);
    }
</style>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-3">
        <div class="sidebar rounded">
            <div class="text-center mb-4">
                <div class="user-avatar">
                    <i class="fas fa-user"></i>
                </div>
                <h5 class="mt-2">{{ current_user.username }}</h5>
                <p class="badge bg-primary">{{ current_user.role }}</p>
            </div>
            <hr>
            <a href="{{ url_for('dashboard') }}" class="sidebar-link">
                <i class="fas fa-tachometer-alt"></i> لوحة التحكم
            </a>
            <a href="{{ url_for('courses') }}" class="sidebar-link active">
                <i class="fas fa-graduation-cap"></i> الدورات التدريبية
            </a>
            {% if current_user.role == 'admin' %}
            <a href="{{ url_for('users') }}" class="sidebar-link">
                <i class="fas fa-users"></i> إدارة المستخدمين
            </a>
            <a href="{{ url_for('personal_data_excel') }}" class="sidebar-link">
                <i class="fas fa-id-card"></i> إدارة البيانات بالإكسل
            </a>
            {% endif %}
            <a href="#" class="sidebar-link">
                <i class="fas fa-calendar-alt"></i> الجدول الزمني
            </a>
            <a href="#" class="sidebar-link">
                <i class="fas fa-certificate"></i> الشهادات
            </a>
            <a href="{{ url_for('reports') }}" class="sidebar-link">
                <i class="fas fa-chart-bar"></i> التقارير
            </a>
            {% if current_user.role == 'admin' %}
            <a href="{{ url_for('reference_tables') }}" class="sidebar-link">
                <i class="fas fa-table"></i> الجداول الترميزية
            </a>
            <a href="#" class="sidebar-link">
                <i class="fas fa-cog"></i> الإعدادات
            </a>
            {% endif %}
        </div>
    </div>

    <div class="col-md-9">
        <div class="main-container">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 style="background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; font-weight: 900; font-size: 2.5rem; filter: drop-shadow(0 4px 8px rgba(30, 64, 175, 0.4));">
                    <i class="fas fa-graduation-cap" style="margin-left: 15px; animation: rotate 4s linear infinite; filter: drop-shadow(0 0 10px rgba(30, 64, 175, 0.5));"></i>
                    الدورات التدريبية
                </h2>
            <div>
                <a href="{{ url_for('search_courses') }}" class="btn btn-info me-2">
                    <i class="fas fa-search me-1"></i> بحث متقدم
                </a>
                {% if current_user.role in ['trainer', 'admin'] %}
                <a href="{{ url_for('add_course') }}" class="btn btn-primary">
                    <i class="fas fa-plus-circle me-1"></i> إضافة دورة جديدة
                </a>
                {% endif %}
            </div>
        </div>

        <div class="filter-section">
            <div class="row">
                <div class="col-md-4">
                    <div class="form-group">
                        <label for="category" class="form-label">التصنيف</label>
                        <select class="form-select" id="category">
                            <option value="">جميع التصنيفات</option>
                            <option value="programming">البرمجة</option>
                            <option value="design">التصميم</option>
                            <option value="management">الإدارة</option>
                            <option value="marketing">التسويق</option>
                            <option value="ai">الذكاء الاصطناعي</option>
                            <option value="database">قواعد البيانات</option>
                            <option value="other">أخرى</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label for="level" class="form-label">المستوى</label>
                        <select class="form-select" id="level">
                            <option value="">جميع المستويات</option>
                            <option value="beginner">مبتدئ</option>
                            <option value="intermediate">متوسط</option>
                            <option value="advanced">متقدم</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label for="search" class="form-label">بحث</label>
                        <input type="text" class="form-control" id="search" placeholder="اسم الدورة أو المدرب">
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            {% if courses %}
                {% for course in courses %}
                    <div class="col-md-4 mb-4">
                        <div class="card course-card">
                            <div class="course-image" style="background-image: url('{% if course.image %}{{ url_for('static', filename='uploads/courses/' + course.image) }}{% else %}{{ url_for('static', filename='img/default_course.jpg') }}{% endif %}');">
                                <span class="badge
                                    {% if course.category == 'programming' %}bg-primary
                                    {% elif course.category == 'design' %}bg-success
                                    {% elif course.category == 'management' %}bg-warning
                                    {% elif course.category == 'marketing' %}bg-danger
                                    {% elif course.category == 'ai' %}bg-secondary
                                    {% elif course.category == 'database' %}bg-info
                                    {% else %}bg-dark
                                    {% endif %} course-category">
                                    {% if course.category == 'programming' %}البرمجة
                                    {% elif course.category == 'design' %}التصميم
                                    {% elif course.category == 'management' %}الإدارة
                                    {% elif course.category == 'marketing' %}التسويق
                                    {% elif course.category == 'ai' %}الذكاء الاصطناعي
                                    {% elif course.category == 'database' %}قواعد البيانات
                                    {% else %}أخرى
                                    {% endif %}
                                </span>
                            </div>
                            <div class="course-body">
                                <h5 class="course-title">{{ course.title }}</h5>
                                <p class="course-instructor"><i class="fas fa-user-tie me-1"></i>
                                    {% if course.trainer_id and course.trainer %}
                                        {{ course.trainer.username }}
                                    {% else %}
                                        غير محدد
                                    {% endif %}
                                </p>
                                <p class="course-description">{{ course.description|truncate(100) }}</p>
                                <div class="course-meta">
                                    <div>
                                        <span class="badge
                                            {% if course.level == 'beginner' %}bg-success
                                            {% elif course.level == 'intermediate' %}bg-warning
                                            {% elif course.level == 'advanced' %}bg-danger
                                            {% endif %}">
                                            {% if course.level == 'beginner' %}مبتدئ
                                            {% elif course.level == 'intermediate' %}متوسط
                                            {% elif course.level == 'advanced' %}متقدم
                                            {% endif %}
                                        </span>
                                    </div>
                                    <div class="course-students">
                                        <i class="fas fa-calendar-alt me-1"></i> {{ course.start_date.strftime('%Y-%m-%d') }}
                                    </div>
                                </div>
                            </div>
                            <div class="card-footer bg-white text-center">
                                <a href="{{ url_for('course_details', course_id=course.id) }}" class="btn btn-primary">
                                    <i class="fas fa-info-circle me-1"></i> تفاصيل الدورة
                                </a>
                            </div>
                        </div>
                    </div>
                {% endfor %}
            {% else %}
                <div class="col-12 text-center py-5">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i> لا توجد دورات تدريبية متاحة حالياً
                    </div>
                    {% if current_user.role in ['trainer', 'admin'] %}
                        <a href="{{ url_for('add_course') }}" class="btn btn-primary mt-3">
                            <i class="fas fa-plus-circle me-1"></i> إضافة دورة جديدة
                        </a>
                    {% endif %}
                </div>
            {% endif %}
        </div>

        <!-- ترقيم الصفحات -->
        <nav aria-label="Page navigation">
            <ul class="pagination justify-content-center">
                <li class="page-item disabled">
                    <a class="page-link" href="#" tabindex="-1" aria-disabled="true">السابق</a>
                </li>
                <li class="page-item active"><a class="page-link" href="#">1</a></li>
                <li class="page-item"><a class="page-link" href="#">2</a></li>
                <li class="page-item"><a class="page-link" href="#">3</a></li>
                <li class="page-item">
                    <a class="page-link" href="#">التالي</a>
                </li>
            </ul>
        </nav>
        </div> <!-- إغلاق main-container -->
    </div>
</div>

<style>
@keyframes rotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
</style>
{% endblock %}
