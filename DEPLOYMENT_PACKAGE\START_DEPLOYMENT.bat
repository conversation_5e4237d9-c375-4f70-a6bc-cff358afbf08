@echo off
chcp 65001 >nul
color 0A
echo.
echo ████████████████████████████████████████████████████████████████
echo ██                                                            ██
echo ██    🎓 نظام إدارة التدريب - حزمة التوزيع الشاملة           ██
echo ██    Training Management System - Complete Deployment        ██
echo ██                                                            ██
echo ████████████████████████████████████████████████████████████████
echo.
echo 📦 مرحباً بك في حزمة التوزيع الشاملة لنظام إدارة التدريب
echo 📦 Welcome to the Complete Deployment Package
echo.
echo 🎯 هذه الحزمة تحل جميع المشاكل الشائعة في تحويل Python إلى EXE:
echo 🎯 This package solves all common Python to EXE conversion issues:
echo.
echo    ✅ مشكلة تغيير التنسيق (CSS/JS Issues)
echo    ✅ فشل تسجيل الدخول (Login Failures)  
echo    ✅ قاعدة البيانات الفارغة (Empty Database)
echo    ✅ مشاكل البورتات (Port Conflicts)
echo    ✅ الملفات المفقودة (Missing Files)
echo.
echo ════════════════════════════════════════════════════════════════
echo.

:menu
echo 📋 اختر العملية المطلوبة:
echo 📋 Choose the required operation:
echo.
echo    1️⃣  البناء الشامل التلقائي (مستحسن)
echo        Automatic Complete Build (Recommended)
echo.
echo    2️⃣  البناء خطوة بخطوة
echo        Step-by-Step Build
echo.
echo    3️⃣  إدارة قاعدة البيانات المتقدمة
echo        Advanced Database Management
echo.
echo    4️⃣  اختبار النظام الموجود
echo        Test Existing System
echo.
echo    5️⃣  عرض دليل استكشاف الأخطاء
echo        View Troubleshooting Guide
echo.
echo    6️⃣  معلومات المشروع
echo        Project Information
echo.
echo    0️⃣  خروج
echo        Exit
echo.
echo ════════════════════════════════════════════════════════════════
echo.

set /p choice="👆 أدخل رقم اختيارك (Enter your choice): "

if "%choice%"=="1" goto auto_build
if "%choice%"=="2" goto step_build
if "%choice%"=="3" goto database_management
if "%choice%"=="4" goto test_system
if "%choice%"=="5" goto troubleshooting
if "%choice%"=="6" goto project_info
if "%choice%"=="0" goto exit
goto invalid_choice

:auto_build
echo.
echo 🚀 بدء البناء الشامل التلقائي...
echo 🚀 Starting automatic complete build...
echo.
echo ⏰ هذه العملية قد تستغرق 10-20 دقيقة
echo ⏰ This process may take 10-20 minutes
echo.
echo 📋 سيتم تنفيذ الخطوات التالية تلقائياً:
echo 📋 The following steps will be executed automatically:
echo    1. تنظيف المشروع
echo    2. إنشاء بيئة افتراضية نظيفة
echo    3. تثبيت المتطلبات
echo    4. تحضير قاعدة البيانات
echo    5. بناء ملف EXE
echo    6. تحسين الملفات
echo    7. اختبار النظام
echo    8. إنشاء حزمة العميل
echo.
set /p confirm="هل تريد المتابعة؟ (y/n): "
if /i "%confirm%"=="y" (
    call MASTER_BUILD.bat
    echo.
    echo 🎉 تم الانتهاء من البناء الشامل!
    echo 🎉 Complete build finished!
    pause
) else (
    echo تم الإلغاء
    echo Cancelled
)
goto menu

:step_build
echo.
echo 📝 البناء خطوة بخطوة:
echo 📝 Step-by-step build:
echo.

:step_menu
echo    1. تنظيف المشروع (Project Cleanup)
echo    2. إنشاء بيئة افتراضية (Create Virtual Environment)
echo    3. تثبيت المتطلبات (Install Requirements)
echo    4. تحضير قاعدة البيانات (Prepare Database)
echo    5. بناء ملف EXE (Build EXE)
echo    6. تحسين الملفات (Optimize Files)
echo    7. اختبار النظام (Test System)
echo    8. إنشاء حزمة العميل (Create Client Package)
echo    9. العودة للقائمة الرئيسية (Back to Main Menu)
echo.

set /p step="اختر الخطوة (Choose step): "

if "%step%"=="1" call cleanup_project.bat
if "%step%"=="2" call create_clean_environment.bat
if "%step%"=="3" call install_requirements.bat
if "%step%"=="4" call prepare_database.bat
if "%step%"=="5" call build_optimized_exe.bat
if "%step%"=="6" call optimize_files.bat
if "%step%"=="7" call test_complete_system.bat
if "%step%"=="8" call create_client_package.bat
if "%step%"=="9" goto menu

echo.
goto step_menu

:database_management
echo.
echo 🗄️  إدارة قاعدة البيانات المتقدمة...
echo 🗄️  Advanced Database Management...
echo.
call database_options.bat
echo.
pause
goto menu

:test_system
echo.
echo 🧪 اختبار النظام الموجود...
echo 🧪 Testing existing system...
echo.

cd /d "%~dp0\.."

if exist "dist\TrainingSystem\TrainingSystem.exe" (
    echo ✅ تم العثور على النظام المبني
    echo ✅ Built system found
    echo.
    call "DEPLOYMENT_PACKAGE\test_complete_system.bat"
) else (
    echo ❌ لم يتم العثور على النظام المبني
    echo ❌ Built system not found
    echo.
    echo 💡 يرجى تشغيل البناء الشامل أولاً
    echo 💡 Please run complete build first
)

echo.
pause
goto menu

:troubleshooting
echo.
echo 📖 فتح دليل استكشاف الأخطاء...
echo 📖 Opening troubleshooting guide...
echo.

if exist "TROUBLESHOOTING_GUIDE.md" (
    start notepad "TROUBLESHOOTING_GUIDE.md"
) else (
    echo ❌ ملف الدليل غير موجود
    echo ❌ Guide file not found
)

echo.
pause
goto menu

:project_info
echo.
echo ════════════════════════════════════════════════════════════════
echo    ℹ️  معلومات المشروع / Project Information
echo ════════════════════════════════════════════════════════════════
echo.
echo 📋 اسم المشروع: نظام إدارة التدريب
echo 📋 Project Name: Training Management System
echo.
echo 🔢 الإصدار: 1.0.0
echo 🔢 Version: 1.0.0
echo.
echo 📅 تاريخ الإنشاء: %date%
echo 📅 Creation Date: %date%
echo.
echo 🛠️  التقنيات المستخدمة:
echo 🛠️  Technologies Used:
echo    • Python 3.11+
echo    • Flask 2.3.3
echo    • SQLAlchemy 2.0.21
echo    • PyInstaller 5.13.2
echo    • Bootstrap 5.3
echo.
echo 📦 محتويات الحزمة:
echo 📦 Package Contents:
echo    • سكريبتات البناء التلقائي
echo    • تطبيق Flask محسن للتوزيع
echo    • قوالب HTML محسنة
echo    • قاعدة بيانات جاهزة
echo    • دليل استكشاف الأخطاء الشامل
echo.
echo 🎯 المشاكل التي تحلها الحزمة:
echo 🎯 Issues Solved by This Package:
echo    ✅ تغيير التنسيق والواجهة
echo    ✅ فشل تسجيل الدخول
echo    ✅ قاعدة البيانات الفارغة
echo    ✅ مشاكل البورتات
echo    ✅ الملفات المفقودة
echo    ✅ مشاكل الأداء
echo.
echo 📞 الدعم الفني:
echo 📞 Technical Support:
echo    📧 البريد الإلكتروني: <EMAIL>
echo    🌐 الموقع: www.trainingsystem.com
echo.
echo © 2024 Training System. جميع الحقوق محفوظة
echo © 2024 Training System. All Rights Reserved
echo.
pause
goto menu

:invalid_choice
echo.
echo ❌ اختيار غير صحيح، يرجى المحاولة مرة أخرى
echo ❌ Invalid choice, please try again
echo.
pause
goto menu

:exit
echo.
echo 👋 شكراً لاستخدام حزمة التوزيع الشاملة
echo 👋 Thank you for using the Complete Deployment Package
echo.
echo 💡 نصائح أخيرة:
echo 💡 Final Tips:
echo    • احتفظ بنسخة احتياطية من البيانات
echo    • غير كلمة المرور الافتراضية
echo    • راجع دليل استكشاف الأخطاء عند الحاجة
echo.
echo 🎯 الحزمة النهائية ستكون جاهزة في مجلد TrainingSystem_Final_*
echo 🎯 Final package will be ready in TrainingSystem_Final_* folder
echo.
pause
exit /b 0
