@echo off
chcp 65001 >nul
echo ========================================
echo    تثبيت المتطلبات للتحويل إلى EXE
echo    Installing Requirements for EXE
echo ========================================
echo.

cd /d "%~dp0\.."

echo 🔧 التحقق من تفعيل البيئة الافتراضية...
echo 🔧 Checking virtual environment activation...

if not exist "venv_deployment\Scripts\activate.bat" (
    echo ❌ البيئة الافتراضية غير موجودة
    echo ❌ Virtual environment not found
    echo يرجى تشغيل create_clean_environment.bat أولاً
    echo Please run create_clean_environment.bat first
    pause
    exit /b 1
)

call venv_deployment\Scripts\activate.bat

echo ✅ تم تفعيل البيئة الافتراضية
echo ✅ Virtual environment activated
echo.

echo 📦 إنشاء ملف requirements محسن للتوزيع...
echo 📦 Creating optimized requirements file for deployment...

(
echo # Core Flask Framework
echo Flask==2.3.3
echo Flask-SQLAlchemy==3.0.5
echo Flask-Login==0.6.3
echo Flask-WTF==1.1.1
echo WTForms==3.0.1
echo.
echo # Database
echo SQLAlchemy==2.0.21
echo.
echo # Security
echo Werkzeug==2.3.7
echo email-validator==2.0.0
echo.
echo # Excel Processing
echo pandas==2.1.1
echo openpyxl==3.1.2
echo xlsxwriter==3.1.9
echo numpy==1.24.4
echo.
echo # Arabic Text Processing
echo arabic-reshaper==3.0.0
echo python-bidi==0.4.2
echo.
echo # Progress Bars
echo tqdm==4.66.1
echo.
echo # PyInstaller for EXE creation
echo pyinstaller==5.13.2
echo.
echo # Additional utilities
echo python-dateutil==2.8.2
echo pytz==2023.3
echo six==1.16.0
echo.
echo # DNS resolution for email validation
echo dnspython==2.4.2
) > requirements_deployment.txt

echo ✅ تم إنشاء ملف requirements_deployment.txt
echo ✅ Created requirements_deployment.txt
echo.

echo 📦 تثبيت المتطلبات الأساسية...
echo 📦 Installing core requirements...

pip install -r requirements_deployment.txt

if errorlevel 1 (
    echo ❌ فشل في تثبيت بعض المتطلبات
    echo ❌ Failed to install some requirements
    echo.
    echo 🔄 محاولة تثبيت المكتبات واحدة تلو الأخرى...
    echo 🔄 Trying to install libraries one by one...
    
    pip install Flask==2.3.3
    pip install Flask-SQLAlchemy==3.0.5
    pip install Flask-Login==0.6.3
    pip install Flask-WTF==1.1.1
    pip install WTForms==3.0.1
    pip install SQLAlchemy==2.0.21
    pip install Werkzeug==2.3.7
    pip install email-validator==2.0.0
    pip install pandas==2.1.1
    pip install openpyxl==3.1.2
    pip install xlsxwriter==3.1.9
    pip install numpy==1.24.4
    pip install arabic-reshaper==3.0.0
    pip install python-bidi==0.4.2
    pip install tqdm==4.66.1
    pip install pyinstaller==5.13.2
    pip install python-dateutil==2.8.2
    pip install pytz==2023.3
    pip install six==1.16.0
    pip install dnspython==2.4.2
)

echo.
echo 📦 التحقق من تثبيت PyInstaller...
echo 📦 Verifying PyInstaller installation...

pyinstaller --version
if errorlevel 1 (
    echo ⚠️  PyInstaller غير مثبت بشكل صحيح، محاولة إعادة التثبيت...
    echo ⚠️  PyInstaller not installed correctly, trying to reinstall...
    pip uninstall pyinstaller -y
    pip install pyinstaller==5.13.2
)

echo.
echo 📋 عرض المكتبات المثبتة...
echo 📋 Showing installed packages...

pip list

echo.
echo ✅ تم تثبيت جميع المتطلبات بنجاح!
echo ✅ All requirements installed successfully!
echo.

echo 📋 المكتبات الأساسية المثبتة:
echo 📋 Core libraries installed:
echo    ✓ Flask - إطار العمل الأساسي
echo    ✓ Flask-SQLAlchemy - قاعدة البيانات
echo    ✓ Flask-Login - نظام تسجيل الدخول
echo    ✓ pandas - معالجة البيانات
echo    ✓ openpyxl - ملفات Excel
echo    ✓ arabic-reshaper - النصوص العربية
echo    ✓ PyInstaller - تحويل إلى EXE
echo.

echo 🔄 الخطوة التالية: تشغيل prepare_database.bat
echo 🔄 Next step: Run prepare_database.bat
echo.

pause
